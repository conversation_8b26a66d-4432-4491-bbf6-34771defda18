#!/bin/bash

# Termina eventuali processi precedenti
echo "Killing any existing servers..."
pkill -f "python manage.py runserver" || true
pkill -f "node.*start" || true
sleep 1

echo "Starting Django server..."
cd /home/<USER>/projects/SimplePEC/SimplePEC
source venv/bin/activate

# Avvia il server Django in background
python manage.py runserver 0.0.0.0:8000 > django_server.log 2>&1 &
DJANGO_PID=$!
echo "Django server started with PID: $DJANGO_PID"

echo "Starting React development server..."
cd /home/<USER>/projects/SimplePEC/SimplePEC/frontend

# Assicurati che setupProxy.js sia corretto
cat > src/setupProxy.js << 'EOL'
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  console.log('🔧 Configurazione proxy attiva');
  
  // Configura proxy per API
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
    })
  );
  
  // Configura proxy per admin
  app.use(
    '/admin',
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
    })
  );
  
  console.log('✅ Proxy configurato per /api e /admin');
};
EOL

# Installa le dipendenze necessarie
npm install http-proxy-middleware@latest --save

# Avvia il server React in modalità standard
echo "Starting React server..."
BROWSER=none npm start > react_server.log 2>&1 &
REACT_PID=$!
echo "React server started with PID: $REACT_PID"

echo -e "\n✅ Servers are running!"
echo "- Django: http://localhost:8000"
echo "- React: http://localhost:3000"
echo -e "\nReact and Django logs are being saved to react_server.log and django_server.log"
echo -e "Press Ctrl+C to stop both servers\n"

# Wait for Ctrl+C and then kill both processes
trap "echo 'Stopping servers...'; kill $DJANGO_PID $REACT_PID; exit" INT
wait
