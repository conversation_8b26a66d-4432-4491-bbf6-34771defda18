from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin

from apps.pec.models import MessaggioPEC, AccountPEC
from apps.clienti.models import Cliente
from .serializers import (
    ConteggioSerializer, StatisticaAccountSerializer,
    StatisticaClienteSerializer, TrendTemporaleSerializer
)
from core.permissions import get_user_role

class ConteggiView(APIView):
    """
    View per ottenere i conteggi dei messaggi per stato
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, format=None):
        """
        Restituisce i conteggi dei messaggi per stato
        """
        user = request.user

        # Base query
        if user.is_superuser or user.is_staff:
            queryset = MessaggioPEC.objects.all()
        else:
            # Messaggi degli account autorizzati o clienti di cui è referente
            account_messages = MessaggioPEC.objects.filter(account_pec__utenti_autorizzati=user)
            client_messages = MessaggioPEC.objects.filter(cliente__referente__utente=user)
            queryset = (account_messages | client_messages).distinct()

        # Conteggio per stato
        da_leggere = queryset.filter(stato='DA_LEGGERE').count()
        da_assegnare = queryset.filter(stato='DA_ASSEGNARE').count()
        assegnato = queryset.filter(stato='ASSEGNATO').count()
        lavorato = queryset.filter(stato='LAVORATO').count()
        totale = queryset.count()

        data = {
            'da_leggere': da_leggere,
            'da_assegnare': da_assegnare,
            'assegnato': assegnato,
            'lavorato': lavorato,
            'totale': totale
        }

        serializer = ConteggioSerializer(data)
        return Response(serializer.data)


class StatistichePerAccountView(APIView):
    """
    View per ottenere le statistiche per account PEC
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, format=None):
        """
        Restituisce le statistiche relative agli account PEC
        """
        user = request.user

        # Base query per account
        if user.is_superuser or user.is_staff:
            accounts = AccountPEC.objects.all()
        else:
            accounts = AccountPEC.objects.filter(utenti_autorizzati=user)

        risultati = []
        for account in accounts:
            messaggi = MessaggioPEC.objects.filter(account_pec=account)

            # Filtra per permessi se non è admin
            if not (user.is_superuser or user.is_staff):
                client_messages = MessaggioPEC.objects.filter(
                    account_pec=account,
                    cliente__referente__utente=user
                )
                messaggi = (messaggi | client_messages).distinct()

            # Conteggi per questo account
            totale_messaggi = messaggi.count()
            da_leggere = messaggi.filter(stato='DA_LEGGERE').count()
            da_assegnare = messaggi.filter(stato='DA_ASSEGNARE').count()
            assegnato = messaggi.filter(stato='ASSEGNATO').count()
            lavorato = messaggi.filter(stato='LAVORATO').count()

            dati_account = {
                'account_id': account.id,
                'account_nome': account.nome,
                'account_email': account.indirizzo_email,
                'totale_messaggi': totale_messaggi,
                'da_leggere': da_leggere,
                'da_assegnare': da_assegnare,
                'assegnato': assegnato,
                'lavorato': lavorato
            }

            risultati.append(dati_account)

        serializer = StatisticaAccountSerializer(risultati, many=True)
        return Response(serializer.data)


class StatistichePerClienteView(APIView):
    """
    View per ottenere le statistiche per cliente
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, format=None):
        """
        Restituisce le statistiche relative ai clienti
        """
        user = request.user

        # Base query per clienti
        if user.is_superuser or user.is_staff:
            clienti = Cliente.objects.filter(attivo=True)
        else:
            clienti = Cliente.objects.filter(referente__utente=user, attivo=True).distinct()

        risultati = []
        for cliente in clienti:
            messaggi = MessaggioPEC.objects.filter(cliente=cliente)

            # Conteggi per questo cliente
            totale_messaggi = messaggi.count()
            da_leggere = messaggi.filter(stato='DA_LEGGERE').count()
            da_assegnare = messaggi.filter(stato='DA_ASSEGNARE').count()
            assegnato = messaggi.filter(stato='ASSEGNATO').count()
            lavorato = messaggi.filter(stato='LAVORATO').count()

            dati_cliente = {
                'cliente_id': cliente.id,
                'cliente_nome': cliente.nome,
                'cliente_codice': cliente.codice,
                'totale_messaggi': totale_messaggi,
                'da_leggere': da_leggere,
                'da_assegnare': da_assegnare,
                'assegnato': assegnato,
                'lavorato': lavorato
            }

            risultati.append(dati_cliente)

        serializer = StatisticaClienteSerializer(risultati, many=True)
        return Response(serializer.data)


class TrendTemporaleView(APIView):
    """
    View per ottenere il trend temporale dei messaggi
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, format=None):
        """
        Restituisce il trend temporale dei messaggi negli ultimi 30 giorni
        """
        user = request.user

        # Calcola la data di 30 giorni fa
        data_inizio = timezone.now().date() - timedelta(days=30)

        # Base query
        if user.is_superuser or user.is_staff:
            queryset = MessaggioPEC.objects.all()
        else:
            account_messages = MessaggioPEC.objects.filter(account_pec__utenti_autorizzati=user)
            client_messages = MessaggioPEC.objects.filter(cliente__referente__utente=user)
            queryset = (account_messages | client_messages).distinct()

        # Filtra per data
        queryset = queryset.filter(data_ricezione__date__gte=data_inizio)

        # Prepara i dati per il trend
        trend_data = {}
        current_date = data_inizio
        today = timezone.now().date()

        while current_date <= today:
            current_date_str = current_date.isoformat()
            trend_data[current_date_str] = {
                'data': current_date,
                'totale': 0,
                'da_leggere': 0,
                'da_assegnare': 0,
                'assegnato': 0,
                'lavorato': 0
            }
            current_date += timedelta(days=1)

        # Popola i dati
        for messaggio in queryset:
            data_msg = messaggio.data_ricezione.date().isoformat()
            if data_msg in trend_data:
                trend_data[data_msg]['totale'] += 1

                if messaggio.stato == 'DA_LEGGERE':
                    trend_data[data_msg]['da_leggere'] += 1
                elif messaggio.stato == 'DA_ASSEGNARE':
                    trend_data[data_msg]['da_assegnare'] += 1
                elif messaggio.stato == 'ASSEGNATO':
                    trend_data[data_msg]['assegnato'] += 1
                elif messaggio.stato == 'LAVORATO':
                    trend_data[data_msg]['lavorato'] += 1

        # Converte in lista
        result = list(trend_data.values())

        serializer = TrendTemporaleSerializer(result, many=True)
        return Response(serializer.data)


# Template views per il nuovo frontend Django
class DashboardView(LoginRequiredMixin, TemplateView):
    """Dashboard principale dell'applicazione"""
    template_name = 'dashboard/index.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        if user.is_superuser:
            # Admin vede tutti i messaggi
            queryset = MessaggioPEC.objects.all()
        elif user.is_manager():
            # Manager vede tutti i messaggi della sua organizzazione
            if user.organizzazione:
                queryset = MessaggioPEC.objects.filter(account_pec__organizzazione=user.organizzazione)
            else:
                queryset = MessaggioPEC.objects.none()
        elif user.is_staff_user():
            # Staff vede solo i messaggi degli account a cui è stato assegnato
            account_messages = MessaggioPEC.objects.filter(account_pec__in=user.account_pec_autorizzati.all())
            client_messages = MessaggioPEC.objects.filter(cliente__in=user.clienti_assegnati.all())
            queryset = (account_messages | client_messages).distinct()
        else:
            # Retrocompatibilità per altri utenti
            account_messages = MessaggioPEC.objects.filter(account_pec__utenti_autorizzati=user)
            client_messages = MessaggioPEC.objects.filter(cliente__referente__utente=user)
            queryset = (account_messages | client_messages).distinct()

        # Conteggio per stato
        conteggi = {
            'da_leggere': queryset.filter(stato='DA_LEGGERE').count(),
            'da_assegnare': queryset.filter(stato='DA_ASSEGNARE').count(),
            'assegnato': queryset.filter(stato='ASSEGNATO').count(),
            'lavorato': queryset.filter(stato='LAVORATO').count(),
            'totale': queryset.count()
        }

        # Statistiche per account in base al ruolo
        if user.is_superuser:
            # Admin vede tutti gli account
            accounts = AccountPEC.objects.all()
        elif user.is_manager():
            # Manager vede tutti gli account della sua organizzazione
            if user.organizzazione:
                accounts = AccountPEC.objects.filter(organizzazione=user.organizzazione)
            else:
                accounts = AccountPEC.objects.none()
        elif user.is_staff_user():
            # Staff vede solo gli account a cui è stato assegnato
            accounts = user.account_pec_autorizzati.all()
        else:
            # Retrocompatibilità per altri utenti
            accounts = AccountPEC.objects.filter(utenti_autorizzati=user)

        per_account = []
        for account in accounts:
            messaggi = MessaggioPEC.objects.filter(account_pec=account)

            # Filtra per permessi se non è admin
            if not (user.is_superuser or user.is_staff):
                client_messages = MessaggioPEC.objects.filter(
                    account_pec=account,
                    cliente__referente__utente=user
                )
                messaggi = (messaggi | client_messages).distinct()

            per_account.append({
                'id': account.id,
                'nome': account.nome,
                'email': account.indirizzo_email,
                'totale_messaggi': messaggi.count()
            })

        # Messaggi recenti e arricchimento delle informazioni
        messaggi_base = queryset.order_by('-data_ricezione')[:10]
        messaggi_recenti = []

        import re
        import email
        from email.header import decode_header

        for messaggio in messaggi_base:
            # Prepara le info di base del messaggio
            messaggio_info = {
                'id': messaggio.id,
                'oggetto': messaggio.oggetto,
                'data_ricezione': messaggio.data_ricezione,
                'stato': messaggio.stato,
                'get_stato_display': messaggio.get_stato_display(),
            }

            # Info sul cliente
            if messaggio.cliente:
                messaggio_info['cliente_info'] = {
                    'id': messaggio.cliente.id,
                    'nome': messaggio.cliente.nome,
                }
            else:
                messaggio_info['cliente_info'] = None

            # Account PEC info
            if messaggio.account_pec:
                messaggio_info['account_pec_info'] = messaggio.account_pec.nome
            else:
                messaggio_info['account_pec_info'] = None

            # Mittente info
            mittente_info = None

            # Verifica se il messaggio è una ricevuta
            is_ricevuta = False

            # Pattern comuni negli oggetti delle ricevute
            if re.search(r'ACCETTAZIONE|POSTA CERTIFICATA: accettazione', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'CONSEGNA|POSTA CERTIFICATA: avvenuta consegna', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'MANCATA CONSEGNA|POSTA CERTIFICATA: mancata consegna', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'PRESA IN CARICO', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'ANOMALIA MESSAGGIO|ANOMALIA', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'ERRORE DI CONSEGNA|MAILER.?DAEMON|DELIVERY.{0,10}(STATUS|FAIL|ERROR)', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True

            # Estrai il mittente appropriato in base al tipo di messaggio
            if messaggio.mittente:
                # Se c'è un mittente nel DB, lo usiamo come fallback
                mittente_info = {
                    'id': messaggio.mittente.id,
                    'nome': messaggio.mittente.nome,
                    'email': messaggio.mittente.email,
                }

            if messaggio.messaggio_completo:
                try:
                    # Per le ricevute, estrai info direttamente dal messaggio completo
                    if is_ricevuta:
                        email_message = email.message_from_bytes(messaggio.messaggio_completo)
                        from_header = email_message.get('From', '')
                        if from_header:
                            try:
                                decoded_parts = decode_header(from_header)
                                from_decoded = ' '.join([
                                    part.decode(encoding or 'utf-8', errors='replace')
                                    if isinstance(part, bytes) else part
                                    for part, encoding in decoded_parts
                                ])
                                mittente_info = {'nome': from_decoded, 'email': '', 'id': None}
                            except Exception:
                                mittente_info = {'nome': from_header, 'email': '', 'id': None}

                    # Per i messaggi normali, estrai da postacert.eml
                    else:
                        # Cerca postacert.eml
                        raw_message = messaggio.messaggio_completo.decode('latin-1', errors='replace')
                        postacert_found = False

                        # Cerca "filename="postacert.eml"" nel messaggio
                        postacert_pos = raw_message.find('filename="postacert.eml"')
                        if postacert_pos == -1:
                            postacert_pos = raw_message.find('filename=postacert.eml')

                        if postacert_pos > 0:
                            # Cerca il boundary precedente
                            boundary_start = raw_message.rfind('--', 0, postacert_pos)
                            if boundary_start > 0:
                                # Estrai il boundary
                                boundary_end = raw_message.find('\r\n', boundary_start)
                                if boundary_end > 0:
                                    boundary = raw_message[boundary_start:boundary_end].strip()

                                    # Trova l'inizio del contenuto (dopo i doppi CRLF)
                                    header_end = raw_message.find('\r\n\r\n', postacert_pos)
                                    if header_end > 0:
                                        # Trova il prossimo boundary (fine del contenuto)
                                        next_boundary = raw_message.find(boundary, header_end)
                                        if next_boundary > 0:
                                            # Estrai header e payload
                                            header = raw_message[boundary_start:header_end]
                                            payload_raw = raw_message[header_end+4:next_boundary-2]  # dopo \r\n\r\n, prima di \r\n--boundary

                                            # Determina l'encoding
                                            if 'Content-Transfer-Encoding: base64' in header:
                                                import base64
                                                payload = base64.b64decode(payload_raw)
                                            elif 'Content-Transfer-Encoding: quoted-printable' in header:
                                                import quopri
                                                payload = quopri.decodestring(payload_raw.encode('latin-1'))
                                            else:
                                                payload = payload_raw.encode('latin-1')

                                            # Abbiamo il payload di postacert.eml
                                            postacert_message = email.message_from_bytes(payload)
                                            postacert_found = True

                                            # Estrai il mittente
                                            if postacert_message:
                                                from_header = postacert_message.get('From', '')
                                                if from_header:
                                                    try:
                                                        decoded_parts = decode_header(from_header)
                                                        from_decoded = ' '.join([
                                                            part.decode(encoding or 'utf-8', errors='replace')
                                                            if isinstance(part, bytes) else part
                                                            for part, encoding in decoded_parts
                                                        ])
                                                        mittente_info = {'nome': from_decoded, 'email': '', 'id': None}
                                                    except Exception:
                                                        mittente_info = {'nome': from_header, 'email': '', 'id': None}

                        # Se non abbiamo trovato postacert.eml, proviamo con l'approccio del parser email
                        if not postacert_found:
                            email_message = email.message_from_bytes(messaggio.messaggio_completo)

                            # Cerca l'allegato postacert.eml
                            for part in email_message.walk():
                                filename = part.get_filename()
                                content_disp = part.get('Content-Disposition', '')

                                if ((filename and "postacert.eml" in filename.lower()) or
                                   ("filename=\"postacert.eml\"" in content_disp.lower()) or
                                   part.get_content_type() == "message/rfc822"):
                                    # Estratto postacert.eml
                                    postacert_content = part.get_payload(decode=True)
                                    if postacert_content:
                                        inner_message = email.message_from_bytes(postacert_content)
                                        from_header = inner_message.get('From', '')
                                        if from_header:
                                            try:
                                                decoded_parts = decode_header(from_header)
                                                from_decoded = ' '.join([
                                                    part.decode(encoding or 'utf-8', errors='replace')
                                                    if isinstance(part, bytes) else part
                                                    for part, encoding in decoded_parts
                                                ])
                                                mittente_info = {'nome': from_decoded, 'email': '', 'id': None}
                                                break
                                            except Exception:
                                                mittente_info = {'nome': from_header, 'email': '', 'id': None}
                                                break

                except Exception:
                    # In caso di errore, usiamo il mittente salvato nel DB come fallback
                    pass

            # Se non abbiamo trovato nessun mittente, usa un valore di default
            if not mittente_info:
                mittente_info = {'nome': 'Non disponibile', 'email': '', 'id': None}

            messaggio_info['mittente_info'] = mittente_info

            # Aggiungi il messaggio con le info arricchite al risultato
            messaggi_recenti.append(messaggio_info)

        # Passaggio dei dati al template
        context['conteggi'] = conteggi
        context['per_account'] = per_account
        context['messaggi_recenti'] = messaggi_recenti

        # Aggiungi dati per i grafici con vari range temporali
        from datetime import timedelta
        import calendar

        # Prepara dati per diversi periodi di tempo
        def get_trend_data(days):
            data_inizio = timezone.now().date() - timedelta(days=days)
            trend_data = {}

            # Inizializza i dati per ogni giorno nel periodo
            current_date = data_inizio
            today = timezone.now().date()

            # Per periodi lunghi, aggreghiamo per settimana o mese
            if days > 180:
                # Aggregazione mensile per periodi molto lunghi (1 anno)
                current_month = data_inizio.replace(day=1)
                while current_month <= today:
                    next_month = (current_month.replace(day=28) + timedelta(days=4)).replace(day=1)
                    month_end = next_month - timedelta(days=1)
                    if month_end > today:
                        month_end = today

                    month_label = current_month.strftime('%b %Y')  # Es. "Gen 2023"
                    trend_data[month_label] = {
                        'label': month_label,
                        'start_date': current_month,
                        'end_date': month_end,
                        'count': 0,
                        'da_leggere': 0,
                        'da_assegnare': 0,
                        'assegnato': 0,
                        'lavorato': 0
                    }

                    current_month = next_month
            elif days > 31:
                # Aggregazione settimanale
                week_start = current_date - timedelta(days=current_date.weekday())
                while week_start <= today:
                    week_end = week_start + timedelta(days=6)
                    if week_end > today:
                        week_end = today

                    week_label = f"{week_start.strftime('%d/%m')} - {week_end.strftime('%d/%m')}"
                    trend_data[week_label] = {
                        'label': week_label,
                        'start_date': week_start,
                        'end_date': week_end,
                        'count': 0,
                        'da_leggere': 0,
                        'da_assegnare': 0,
                        'assegnato': 0,
                        'lavorato': 0
                    }

                    week_start = week_start + timedelta(days=7)
            else:
                # Aggregazione giornaliera
                while current_date <= today:
                    date_label = current_date.strftime('%d/%m')
                    trend_data[date_label] = {
                        'label': date_label,
                        'date': current_date,
                        'count': 0,
                        'da_leggere': 0,
                        'da_assegnare': 0,
                        'assegnato': 0,
                        'lavorato': 0
                    }
                    current_date += timedelta(days=1)

            # Filtra i messaggi per il periodo di tempo
            messaggi_periodo = queryset.filter(data_ricezione__date__gte=data_inizio)

            # Popoliamo i dati
            for messaggio in messaggi_periodo:
                msg_date = messaggio.data_ricezione.date()

                if days > 180:
                    # Troviamo il mese corrispondente
                    month_start = msg_date.replace(day=1)
                    month_label = month_start.strftime('%b %Y')

                    if month_label in trend_data:
                        trend_data[month_label]['count'] += 1

                        if messaggio.stato == 'DA_LEGGERE':
                            trend_data[month_label]['da_leggere'] += 1
                        elif messaggio.stato == 'DA_ASSEGNARE':
                            trend_data[month_label]['da_assegnare'] += 1
                        elif messaggio.stato == 'ASSEGNATO':
                            trend_data[month_label]['assegnato'] += 1
                        elif messaggio.stato == 'LAVORATO':
                            trend_data[month_label]['lavorato'] += 1
                elif days > 31:
                    # Troviamo la settimana corrispondente
                    week_start = msg_date - timedelta(days=msg_date.weekday())
                    week_end = week_start + timedelta(days=6)
                    if week_end > today:
                        week_end = today

                    week_label = f"{week_start.strftime('%d/%m')} - {week_end.strftime('%d/%m')}"

                    if week_label in trend_data:
                        trend_data[week_label]['count'] += 1

                        if messaggio.stato == 'DA_LEGGERE':
                            trend_data[week_label]['da_leggere'] += 1
                        elif messaggio.stato == 'DA_ASSEGNARE':
                            trend_data[week_label]['da_assegnare'] += 1
                        elif messaggio.stato == 'ASSEGNATO':
                            trend_data[week_label]['assegnato'] += 1
                        elif messaggio.stato == 'LAVORATO':
                            trend_data[week_label]['lavorato'] += 1
                else:
                    date_label = msg_date.strftime('%d/%m')

                    if date_label in trend_data:
                        trend_data[date_label]['count'] += 1

                        if messaggio.stato == 'DA_LEGGERE':
                            trend_data[date_label]['da_leggere'] += 1
                        elif messaggio.stato == 'DA_ASSEGNARE':
                            trend_data[date_label]['da_assegnare'] += 1
                        elif messaggio.stato == 'ASSEGNATO':
                            trend_data[date_label]['assegnato'] += 1
                        elif messaggio.stato == 'LAVORATO':
                            trend_data[date_label]['lavorato'] += 1

            # Converti in lista ordinata per cronologia
            return list(trend_data.values())

        # Ottieni dati per tutti i periodi supportati
        context['trend_data'] = {
            '7': get_trend_data(7),
            '30': get_trend_data(30),
            '90': get_trend_data(90),
            '180': get_trend_data(180),
            '365': get_trend_data(365),
        }

        return context

class DocumentationView(LoginRequiredMixin, TemplateView):
    """Vista per la documentazione utente"""
    template_name = 'docs/documentation.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        # Determina il ruolo dell'utente
        if user.is_superuser:
            user_role = "Superuser"
        else:
            user_role = get_user_role(user) or "Utente"

        context['user_role'] = user_role
        return context
