from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
import json

class SidebarPreferenceView(APIView):
    """
    API per gestire le preferenze della sidebar
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Salva la preferenza per la sidebar (collassata o espansa)
        """
        try:
            data = json.loads(request.body)
            collapsed = data.get('collapsed', False)
            
            # Salva la preferenza nella sessione
            request.session['sidebar_collapsed'] = collapsed
            request.session.modified = True
            
            return Response({'status': 'success'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class ThemePreferenceView(APIView):
    """
    API per gestire le preferenze del tema (light/dark)
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Salva la preferenza per il tema (light/dark)
        """
        try:
            data = json.loads(request.body)
            dark_mode = data.get('dark_mode', False)
            
            # Salva la preferenza nella sessione
            request.session['dark_mode'] = dark_mode
            request.session.modified = True
            
            return Response({'status': 'success'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
