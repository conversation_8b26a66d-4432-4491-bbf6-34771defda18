from rest_framework import serializers

class ConteggioSerializer(serializers.Serializer):
    """Serializer per i conteggi dei messaggi per stato"""
    
    da_leggere = serializers.IntegerField()
    da_assegnare = serializers.IntegerField()
    assegnato = serializers.IntegerField()
    lavorato = serializers.IntegerField()
    totale = serializers.IntegerField()


class StatisticaAccountSerializer(serializers.Serializer):
    """Serializer per le statistiche relative agli account PEC"""
    
    account_id = serializers.IntegerField()
    account_nome = serializers.CharField()
    account_email = serializers.CharField()
    totale_messaggi = serializers.IntegerField()
    da_leggere = serializers.IntegerField()
    da_assegnare = serializers.IntegerField()
    assegnato = serializers.IntegerField()
    lavorato = serializers.IntegerField()


class StatisticaClienteSerializer(serializers.Serializer):
    """Serializer per le statistiche relative ai clienti"""
    
    cliente_id = serializers.IntegerField()
    cliente_nome = serializers.CharField()
    cliente_codice = serializers.CharField()
    totale_messaggi = serializers.IntegerField()
    da_leggere = serializers.IntegerField()
    da_assegnare = serializers.IntegerField()
    assegnato = serializers.IntegerField()
    lavorato = serializers.IntegerField()


class TrendTemporaleSerializer(serializers.Serializer):
    """Serializer per i trend temporali dei messaggi"""
    
    data = serializers.DateField()
    totale = serializers.IntegerField()
    da_leggere = serializers.IntegerField()
    da_assegnare = serializers.IntegerField()
    assegnato = serializers.IntegerField()
    lavorato = serializers.IntegerField()