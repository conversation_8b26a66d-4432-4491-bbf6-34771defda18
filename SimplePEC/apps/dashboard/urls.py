from django.urls import path
from .views import DashboardView, DocumentationView
from .api import SidebarPreferenceView, ThemePreferenceView

urlpatterns = [
    path('', DashboardView.as_view(), name='dashboard'),
    path('documentation/', DocumentationView.as_view(), name='documentation'),
]

# API endpoints per le preferenze utente
api_urlpatterns = [
    path('api/preferences/sidebar/', SidebarPreferenceView.as_view(), name='preferences-sidebar'),
    path('api/preferences/theme/', ThemePreferenceView.as_view(), name='preferences-theme'),
]

urlpatterns += api_urlpatterns