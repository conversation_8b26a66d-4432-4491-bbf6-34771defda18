# Generated by Django 5.2 on 2025-05-07 16:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_organizzazione_utentepersonalizzato_organizzazione'),
        ('clienti', '0002_cliente_codice_fiscale_cliente_email_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='cliente',
            name='organizzazione',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clienti', to='accounts.organizzazione'),
        ),
        migrations.AddField(
            model_name='cliente',
            name='utenti_assegnati',
            field=models.ManyToManyField(blank=True, related_name='clienti_assegnati', to=settings.AUTH_USER_MODEL),
        ),
    ]
