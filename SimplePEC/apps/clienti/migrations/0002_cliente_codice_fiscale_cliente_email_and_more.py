# Generated by Django 5.2 on 2025-05-05 20:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('clienti', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='cliente',
            name='codice_fiscale',
            field=models.CharField(blank=True, help_text='Codice fiscale del cliente', max_length=16),
        ),
        migrations.AddField(
            model_name='cliente',
            name='email',
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AddField(
            model_name='cliente',
            name='indirizzo',
            field=models.Char<PERSON>ield(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='cliente',
            name='note',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='cliente',
            name='partita_iva',
            field=models.CharField(blank=True, help_text='Partita IVA del cliente', max_length=11),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='cliente',
            name='telefono',
            field=models.Char<PERSON>ield(blank=True, max_length=20),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='cliente',
            name='codice',
            field=models.CharField(blank=True, help_text='Codice cliente opzionale', max_length=50, unique=True),
        ),
    ]
