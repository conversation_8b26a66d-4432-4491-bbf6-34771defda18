from rest_framework import serializers
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Contatto
from apps.accounts.serializers import UtenteSerializer

class ClienteSerializer(serializers.ModelSerializer):
    """Serializer per il modello Cliente"""
    
    class Meta:
        model = Cliente
        fields = ['id', 'nome', 'codice', 'attivo', 'data_creazione']


class ReferenteSerializer(serializers.ModelSerializer):
    """Serializer per il modello Referente"""
    
    utente_info = UtenteSerializer(source='utente', read_only=True)
    cliente_info = ClienteSerializer(source='cliente', read_only=True)
    
    class Meta:
        model = Referente
        fields = ['id', 'utente', 'cliente', 'utente_info', 'cliente_info']


class ContattoSerializer(serializers.ModelSerializer):
    """Serializer per il modello Contatto"""
    
    clienti_info = ClienteSerializer(source='clienti', many=True, read_only=True)
    
    class Meta:
        model = Contatto
        fields = ['id', 'nome', 'email', 'pec', 'telefono', 'note', 'clienti', 'clienti_info']