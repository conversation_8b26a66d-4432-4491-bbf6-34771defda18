from django.urls import path
from .views import (
    ClienteListView, ClienteDetailView, create_cliente, update_cliente,
    add_referente, update_note, update_referente, delete_referente,
    search_users, add_referente_existing, AssegnaUtentiClienteView
)

urlpatterns = [
    path('clienti/', ClienteListView.as_view(), name='cliente_list'),
    path('clienti/<int:pk>/', ClienteDetailView.as_view(), name='cliente_detail'),
    path('clienti/create/', create_cliente, name='cliente_create'),
    path('clienti/<int:pk>/update/', update_cliente, name='cliente_update'),
    path('clienti/<int:cliente_id>/add-referente/', add_referente, name='cliente_add_referente'),
    path('clienti/<int:cliente_id>/add-referente-existing/', add_referente_existing, name='cliente_add_referente_existing'),
    path('clienti/<int:cliente_id>/update-note/', update_note, name='cliente_update_note'),
    path('clienti/<int:pk>/assegna-utenti/', AssegnaUtentiClienteView.as_view(), name='cliente_assegna_utenti'),
    path('referenti/<int:referente_id>/update/', update_referente, name='referente_update'),
    path('referenti/<int:referente_id>/delete/', delete_referente, name='referente_delete'),
    path('api/search-users/', search_users, name='search_users'),
]