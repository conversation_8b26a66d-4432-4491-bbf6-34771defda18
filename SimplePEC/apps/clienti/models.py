from django.db import models
from apps.accounts.models import UtentePersonalizzato

class Cliente(models.Model):
    nome = models.Char<PERSON>ield(max_length=200)
    codice = models.CharField(max_length=50, blank=True, unique=True, help_text="Codice cliente opzionale")
    partita_iva = models.CharField(max_length=11, blank=True, help_text="Partita IVA del cliente")
    codice_fiscale = models.CharField(max_length=16, blank=True, help_text="Codice fiscale del cliente")
    indirizzo = models.CharField(max_length=255, blank=True)
    email = models.EmailField(blank=True)
    telefono = models.CharField(max_length=20, blank=True)
    note = models.TextField(blank=True)
    attivo = models.BooleanField(default=True)
    data_creazione = models.DateTimeField(auto_now_add=True)
    organizzazione = models.ForeignKey('accounts.Organizzazione', on_delete=models.SET_NULL, null=True, blank=True, related_name='clienti')
    utenti_assegnati = models.ManyToManyField(UtentePersonalizzato, blank=True, related_name='clienti_assegnati')

    def save(self, *args, **kwargs):
        # Se il codice è vuoto, genera un codice basato su partita iva o nome
        if not self.codice:
            if self.partita_iva:
                self.codice = f"P{self.partita_iva}"
            else:
                # Prende le prime 3 lettere del nome e aggiunge un numero progressivo
                base_code = ''.join(c for c in self.nome[:3].upper() if c.isalnum())
                existing_codes = Cliente.objects.filter(
                    codice__startswith=base_code
                ).count()
                self.codice = f"{base_code}{existing_codes + 1:03d}"

        super().save(*args, **kwargs)

    def __str__(self):
        return self.nome

    class Meta:
        verbose_name = "Cliente"
        verbose_name_plural = "Clienti"

class Referente(models.Model):
    utente = models.ForeignKey(UtentePersonalizzato, on_delete=models.CASCADE)
    cliente = models.ForeignKey(Cliente, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('utente', 'cliente')
        verbose_name = "Referente"
        verbose_name_plural = "Referenti"

    def __str__(self):
        return f"{self.utente.username} - {self.cliente.nome}"

class Contatto(models.Model):
    nome = models.CharField(max_length=200)
    email = models.EmailField()
    pec = models.EmailField(blank=True)
    telefono = models.CharField(max_length=20, blank=True)
    note = models.TextField(blank=True)
    clienti = models.ManyToManyField(Cliente, blank=True)

    def __str__(self):
        return self.nome

    class Meta:
        verbose_name = "Contatto"
        verbose_name_plural = "Contatti"
