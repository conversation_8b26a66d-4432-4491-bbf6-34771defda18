from django import forms
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Contatto
from apps.accounts.models import UtentePersonalizzato

class ClienteForm(forms.ModelForm):
    """Form per la creazione/modifica di un cliente"""

    class Meta:
        model = Cliente
        fields = ['nome', 'codice', 'partita_iva', 'codice_fiscale', 'indirizzo', 'email', 'telefono', 'note', 'attivo']
        widgets = {
            'nome': forms.TextInput(attrs={'class': 'form-control'}),
            'codice': forms.TextInput(attrs={'class': 'form-control'}),
            'partita_iva': forms.TextInput(attrs={'class': 'form-control'}),
            'codice_fiscale': forms.TextInput(attrs={'class': 'form-control'}),
            'indirizzo': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control'}),
            'note': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'attivo': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

class ReferenteForm(forms.ModelForm):
    """Form per la creazione/modifica di un referente"""

    class Meta:
        model = Referente
        fields = ['cliente', 'utente']
        widgets = {
            'cliente': forms.Select(attrs={'class': 'form-select'}),
            'utente': forms.Select(attrs={'class': 'form-select'})
        }

class ContattoForm(forms.ModelForm):
    """Form per la creazione/modifica di un contatto"""

    class Meta:
        model = Contatto
        fields = ['nome', 'email', 'pec', 'telefono', 'clienti']
        widgets = {
            'nome': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'pec': forms.EmailInput(attrs={'class': 'form-control'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control'}),
            'clienti': forms.SelectMultiple(attrs={'class': 'form-select'})
        }

class AssegnaUtentiForm(forms.Form):
    """Form per assegnare utenti a un cliente"""

    utenti = forms.ModelMultipleChoiceField(
        queryset=UtentePersonalizzato.objects.none(),
        widget=forms.CheckboxSelectMultiple,
        required=False
    )

    def __init__(self, *args, **kwargs):
        organizzazione = kwargs.pop('organizzazione', None)
        super().__init__(*args, **kwargs)

        if organizzazione:
            # Filtra gli utenti per organizzazione e ruolo Staff
            from core.permissions import STAFF_GROUP
            self.fields['utenti'].queryset = UtentePersonalizzato.objects.filter(
                organizzazione=organizzazione,
                groups__name=STAFF_GROUP
            )
        else:
            # Se non c'è organizzazione, mostra tutti gli utenti Staff
            from core.permissions import STAFF_GROUP
            self.fields['utenti'].queryset = UtentePersonalizzato.objects.filter(
                groups__name=STAFF_GROUP
            )
