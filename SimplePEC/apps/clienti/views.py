from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Contatto
from .serializers import ClienteSerializer, ReferenteSerializer, ContattoSerializer
from apps.pec.permissions import PermessiPEC, IsClienteAuthorized
from django.views.generic import ListView, DetailView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import redirect, get_object_or_404, render
from django.contrib import messages
from django.urls import reverse
from django.http import JsonResponse
from django.db.models import Q
from apps.accounts.models import UtentePersonalizzato
from django.contrib.auth.decorators import login_required
from .forms import ClienteForm, ReferenteForm, ContattoForm, AssegnaUtentiForm
from core.permissions import manager_required, can_manage_client

class ClienteViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sui clienti
    """
    queryset = Cliente.objects.all()
    serializer_class = ClienteSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['attivo']
    search_fields = ['nome', 'codice']
    ordering_fields = ['nome', 'codice', 'data_creazione']

    def get_permissions(self):
        """
        Solo gli amministratori e i Manager possono creare/modificare/eliminare clienti
        """
        from core.permissions import is_manager

        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            # Verifica se l'utente è un Manager
            if self.request.user.is_authenticated and is_manager(self.request.user):
                permission_classes = [permissions.IsAuthenticated]
            else:
                permission_classes = [permissions.IsAdminUser]  # Fallback per admin
        else:
            permission_classes = [permissions.IsAuthenticated, IsClienteAuthorized]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filtra i clienti in base ai permessi dell'utente
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutti i clienti
        if user.is_superuser:
            return Cliente.objects.all()

        # Se l'utente è un Manager, vede tutti i clienti della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return Cliente.objects.filter(organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo i clienti a cui è stato assegnato
        if user.is_staff_user():
            return user.clienti_assegnati.all()

        # Retrocompatibilità: altri utenti vedono i clienti di cui sono referenti
        return Cliente.objects.filter(referente__utente=user).distinct()

    @action(detail=True, methods=['get'], url_path='referenti')
    def referenti(self, request, pk=None):
        """
        Restituisce i referenti di un cliente
        """
        cliente = self.get_object()
        referenti = Referente.objects.filter(cliente=cliente)
        serializer = ReferenteSerializer(referenti, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='contatti')
    def contatti(self, request, pk=None):
        """
        Restituisce i contatti associati a un cliente
        """
        cliente = self.get_object()
        contatti = Contatto.objects.filter(clienti=cliente)
        serializer = ContattoSerializer(contatti, many=True)
        return Response(serializer.data)


class ReferenteViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sui referenti
    """
    queryset = Referente.objects.all()
    serializer_class = ReferenteSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['cliente', 'utente']

    def get_permissions(self):
        """
        Solo gli amministratori e i Manager possono gestire i referenti
        """
        from core.permissions import is_manager

        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            # Verifica se l'utente è un Manager
            if self.request.user.is_authenticated and is_manager(self.request.user):
                permission_classes = [permissions.IsAuthenticated]
            else:
                permission_classes = [permissions.IsAdminUser]  # Fallback per admin
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Gli amministratori vedono tutti i referenti
        I Manager vedono tutti i referenti dei clienti della loro organizzazione
        Gli utenti Staff vedono solo i referenti dei clienti a cui sono assegnati
        Gli utenti normali vedono solo i referenti dei clienti di cui sono referenti
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutti i referenti
        if user.is_superuser:
            return Referente.objects.all()

        # Se l'utente è un Manager, vede tutti i referenti dei clienti della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return Referente.objects.filter(cliente__organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo i referenti dei clienti a cui è stato assegnato
        if user.is_staff_user():
            return Referente.objects.filter(cliente__in=user.clienti_assegnati.all())

        # Retrocompatibilità: altri utenti vedono i referenti dei clienti di cui sono referenti
        return Referente.objects.filter(cliente__referente__utente=user).distinct()


class ContattoViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sui contatti
    """
    queryset = Contatto.objects.all()
    serializer_class = ContattoSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['clienti']
    search_fields = ['nome', 'email', 'pec', 'telefono']
    ordering_fields = ['nome', 'email']

    def get_queryset(self):
        """
        Gli amministratori vedono tutti i contatti
        I Manager vedono tutti i contatti dei clienti della loro organizzazione
        Gli utenti Staff vedono solo i contatti dei clienti a cui sono assegnati
        Gli utenti normali vedono solo i contatti dei clienti di cui sono referenti
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutti i contatti
        if user.is_superuser:
            return Contatto.objects.all()

        # Se l'utente è un Manager, vede tutti i contatti dei clienti della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return Contatto.objects.filter(clienti__organizzazione=user.organizzazione).distinct()

        # Se l'utente è uno Staff, vede solo i contatti dei clienti a cui è stato assegnato
        if user.is_staff_user():
            return Contatto.objects.filter(clienti__in=user.clienti_assegnati.all()).distinct()

        # Retrocompatibilità: altri utenti vedono i contatti dei clienti di cui sono referenti
        return Contatto.objects.filter(clienti__referente__utente=user).distinct()


# Template views per il nuovo frontend Django
class ClienteListView(LoginRequiredMixin, ListView):
    """Lista dei clienti"""
    model = Cliente
    template_name = 'clienti/cliente_list.html'
    context_object_name = 'clienti'

    def get_queryset(self):
        """Filtra i clienti in base ai permessi dell'utente e ai parametri di ricerca"""
        user = self.request.user

        # Filtro base per i permessi in base al ruolo
        if user.is_superuser:
            # Admin vede tutti i clienti
            queryset = Cliente.objects.all()
        elif user.is_manager():
            # Manager vede tutti i clienti della sua organizzazione
            if user.organizzazione:
                queryset = Cliente.objects.filter(organizzazione=user.organizzazione)
            else:
                queryset = Cliente.objects.none()
        elif user.is_staff_user():
            # Staff vede solo i clienti a cui è stato assegnato
            queryset = user.clienti_assegnati.all()
        else:
            # Altri utenti vedono i clienti di cui sono referenti (retrocompatibilità)
            queryset = Cliente.objects.filter(referente__utente=user).distinct()

        # Applica i filtri di ricerca
        search_query = self.request.GET.get('q', '')
        if search_query:
            queryset = queryset.filter(
                Q(nome__icontains=search_query) |
                Q(codice__icontains=search_query) |
                Q(partita_iva__icontains=search_query) |
                Q(codice_fiscale__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(telefono__icontains=search_query)
            )

        # Filtro per stato (attivo/inattivo)
        stato_filter = self.request.GET.get('stato', '')
        if stato_filter == 'attivo':
            queryset = queryset.filter(attivo=True)
        elif stato_filter == 'inattivo':
            queryset = queryset.filter(attivo=False)

        return queryset

    def get_context_data(self, **kwargs):
        """Aggiunge i parametri di ricerca e visualizzazione al contesto"""
        context = super().get_context_data(**kwargs)

        # Aggiunge i parametri di ricerca al contesto
        context['search_query'] = self.request.GET.get('q', '')
        context['stato_filter'] = self.request.GET.get('stato', '')

        # Aggiunge il tipo di visualizzazione al contesto (default: card)
        context['view_type'] = self.request.GET.get('view', 'card')

        # Aggiunge i ruoli al contesto
        context['is_manager'] = self.request.user.is_manager()
        context['is_staff'] = self.request.user.is_staff_user()

        return context


class ClienteDetailView(LoginRequiredMixin, DetailView):
    """Dettaglio di un cliente"""
    model = Cliente
    template_name = 'clienti/cliente_detail.html'
    context_object_name = 'cliente'

    def get_queryset(self):
        """Filtra i clienti in base ai permessi dell'utente"""
        user = self.request.user

        # Filtro base per i permessi in base al ruolo
        if user.is_superuser:
            # Admin vede tutti i clienti
            return Cliente.objects.all()
        elif user.is_manager():
            # Manager vede tutti i clienti della sua organizzazione
            if user.organizzazione:
                return Cliente.objects.filter(organizzazione=user.organizzazione)
            else:
                return Cliente.objects.none()
        elif user.is_staff_user():
            # Staff vede solo i clienti a cui è stato assegnato
            return user.clienti_assegnati.all()
        else:
            # Altri utenti vedono i clienti di cui sono referenti (retrocompatibilità)
            return Cliente.objects.filter(referente__utente=user).distinct()

    def get_context_data(self, **kwargs):
        """Aggiunge i referenti, i contatti e i messaggi al contesto"""
        context = super().get_context_data(**kwargs)
        cliente = self.get_object()
        context['referenti'] = Referente.objects.filter(cliente=cliente)
        context['contatti'] = Contatto.objects.filter(clienti=cliente)

        # Aggiungi i messaggi PEC associati a questo cliente
        from apps.pec.models import MessaggioPEC
        context['messaggi'] = MessaggioPEC.objects.filter(cliente=cliente).order_by('-data_ricezione')[:10]  # ultimi 10 messaggi

        # Aggiungi i ruoli al contesto
        context['is_manager'] = self.request.user.is_manager()
        context['is_staff'] = self.request.user.is_staff_user()

        # Verifica se l'utente può modificare il cliente
        context['can_edit'] = self.request.user.is_superuser or (
            self.request.user.is_manager() and
            cliente.organizzazione == self.request.user.organizzazione
        ) or (
            self.request.user.is_staff_user() and
            cliente in self.request.user.clienti_assegnati.all()
        )

        return context


def create_cliente(request):
    """Crea un nuovo cliente"""
    if request.method == 'POST':
        # Verifica i permessi (solo Manager possono creare clienti)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo i Manager possono creare clienti')
            return redirect('cliente_list')

        # Estrai i dati dal form
        nome = request.POST.get('nome')
        partita_iva = request.POST.get('partita_iva', '')
        codice_fiscale = request.POST.get('codice_fiscale', '')
        codice = request.POST.get('codice', '')
        indirizzo = request.POST.get('indirizzo', '')
        email = request.POST.get('email', '')
        telefono = request.POST.get('telefono', '')
        note = request.POST.get('note', '')
        attivo = 'attivo' in request.POST

        # Valida i dati
        if not nome:
            messages.error(request, 'Il nome del cliente è obbligatorio')
            return redirect('cliente_list')

        try:
            # Crea il nuovo cliente
            cliente = Cliente.objects.create(
                nome=nome,
                partita_iva=partita_iva,
                codice_fiscale=codice_fiscale,
                codice=codice,
                indirizzo=indirizzo,
                email=email,
                telefono=telefono,
                note=note,
                attivo=attivo
            )

            # Associa il cliente all'organizzazione del Manager
            if request.user.is_manager() and request.user.organizzazione:
                cliente.organizzazione = request.user.organizzazione
                cliente.save()

            messages.success(request, f'Cliente "{nome}" creato con successo')
            return redirect('cliente_detail', pk=cliente.id)
        except Exception as e:
            messages.error(request, f'Errore nella creazione del cliente: {str(e)}')
            return redirect('cliente_list')

    return redirect('cliente_list')


def update_cliente(request, pk):
    """Aggiorna i dati di un cliente esistente"""
    if request.method == 'POST':
        # Ottieni il cliente
        cliente = get_object_or_404(Cliente, pk=pk)

        # Verifica i permessi
        if request.user.is_superuser:
            # Admin può modificare qualsiasi cliente
            pass
        elif request.user.is_manager():
            # Manager può modificare solo i clienti della sua organizzazione
            if cliente.organizzazione != request.user.organizzazione:
                messages.error(request, 'Non puoi modificare clienti di altre organizzazioni')
                return redirect('cliente_list')
        elif request.user.is_staff_user():
            # Staff può modificare solo i clienti a cui è stato assegnato
            if cliente not in request.user.clienti_assegnati.all():
                messages.error(request, 'Non hai il permesso di modificare questo cliente')
                return redirect('cliente_list')
        else:
            messages.error(request, 'Non hai il permesso di modificare i clienti')
            return redirect('cliente_list')

        # Estrai i dati dal form
        nome = request.POST.get('nome')
        partita_iva = request.POST.get('partita_iva', '')
        codice_fiscale = request.POST.get('codice_fiscale', '')
        codice = request.POST.get('codice', '')
        indirizzo = request.POST.get('indirizzo', '')
        email = request.POST.get('email', '')
        telefono = request.POST.get('telefono', '')
        note = request.POST.get('note', '')
        attivo = 'attivo' in request.POST

        # Valida i dati
        if not nome:
            messages.error(request, 'Il nome del cliente è obbligatorio')
            return redirect('cliente_detail', pk=pk)

        try:
            # Aggiorna i dati del cliente
            cliente.nome = nome
            cliente.partita_iva = partita_iva
            cliente.codice_fiscale = codice_fiscale
            if codice:  # Solo se è stato specificato un nuovo codice
                cliente.codice = codice
            cliente.indirizzo = indirizzo
            cliente.email = email
            cliente.telefono = telefono
            cliente.note = note

            # Solo Manager e Admin possono modificare lo stato attivo
            if request.user.is_superuser or request.user.is_manager():
                cliente.attivo = attivo

            cliente.save()

            messages.success(request, f'Cliente "{nome}" aggiornato con successo')
        except Exception as e:
            messages.error(request, f'Errore nell\'aggiornamento del cliente: {str(e)}')

        return redirect('cliente_detail', pk=pk)

    return redirect('cliente_list')


def add_referente(request, cliente_id):
    """Reindirizza all'aggiunta di un referente esistente"""
    # Reindirizza sempre alla funzione per aggiungere un utente esistente
    return add_referente_existing(request, cliente_id)



    return redirect('cliente_list')


def update_note(request, cliente_id):
    """Aggiorna le note di un cliente"""
    if request.method == 'POST':
        # Ottieni il cliente
        cliente = get_object_or_404(Cliente, pk=cliente_id)

        # Verifica i permessi
        if request.user.is_superuser:
            # Admin può modificare qualsiasi cliente
            pass
        elif request.user.is_manager():
            # Manager può modificare solo i clienti della sua organizzazione
            if cliente.organizzazione != request.user.organizzazione:
                messages.error(request, 'Non puoi modificare clienti di altre organizzazioni')
                return redirect('cliente_list')
        elif request.user.is_staff_user():
            # Staff può modificare solo i clienti a cui è stato assegnato
            if cliente not in request.user.clienti_assegnati.all():
                messages.error(request, 'Non hai il permesso di modificare questo cliente')
                return redirect('cliente_list')
        else:
            messages.error(request, 'Non hai il permesso di modificare le note')
            return redirect('cliente_detail', pk=cliente_id)

        # Estrai i dati dal form
        note = request.POST.get('note', '')

        try:
            # Aggiorna le note del cliente
            cliente.note = note
            cliente.save()

            messages.success(request, 'Note aggiornate con successo')
        except Exception as e:
            messages.error(request, f'Errore nell\'aggiornamento delle note: {str(e)}')

        return redirect('cliente_detail', pk=cliente_id)

    return redirect('cliente_list')


def update_referente(request, referente_id):
    """Aggiorna i dati di un referente"""
    if request.method == 'POST':
        # Verifica i permessi (solo Admin e Manager possono modificare referenti)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo gli amministratori e i manager possono modificare i referenti')
            return redirect('cliente_list')

        # Ottieni il referente
        referente = get_object_or_404(Referente, pk=referente_id)
        cliente_id = referente.cliente.id

        # Estrai i dati dal form
        nome = request.POST.get('nome')
        cognome = request.POST.get('cognome')
        email = request.POST.get('email', '')
        telefono = request.POST.get('telefono', '')

        # Valida i dati
        if not (nome and cognome):
            messages.error(request, 'Nome e cognome sono obbligatori')
            return redirect('cliente_detail', pk=cliente_id)

        # Aggiorna i dati dell'utente associato
        utente = referente.utente

        try:
            # Se l'email è cambiata, verifica che non esista già
            if email and email != utente.email:
                from apps.accounts.models import UtentePersonalizzato
                if UtentePersonalizzato.objects.filter(email=email).exclude(id=utente.id).exists():
                    messages.error(request, f'Esiste già un utente con l\'email {email}')
                    return redirect('cliente_detail', pk=cliente_id)

            # Aggiorna i dati
            utente.first_name = nome
            utente.last_name = cognome
            if email:
                utente.email = email
            utente.telefono = telefono
            utente.save()

            messages.success(request, f'Referente {nome} {cognome} aggiornato con successo')
        except Exception as e:
            messages.error(request, f'Errore nell\'aggiornamento del referente: {str(e)}')

        return redirect('cliente_detail', pk=cliente_id)

    return redirect('cliente_list')


def delete_referente(request, referente_id):
    """Elimina un referente"""
    if request.method == 'POST':
        # Verifica i permessi
        if not (request.user.is_superuser or request.user.is_staff):
            messages.error(request, 'Non hai il permesso di eliminare i referenti')
            return redirect('cliente_list')

        # Ottieni il referente
        referente = get_object_or_404(Referente, pk=referente_id)
        cliente_id = referente.cliente.id
        nome_completo = f"{referente.utente.first_name} {referente.utente.last_name}"

        try:
            # Elimina il referente (ma non l'utente associato)
            referente.delete()

            messages.success(request, f'Referente {nome_completo} eliminato con successo')
        except Exception as e:
            messages.error(request, f'Errore nell\'eliminazione del referente: {str(e)}')

        return redirect('cliente_detail', pk=cliente_id)

    return redirect('cliente_list')


class AssegnaUtentiClienteView(LoginRequiredMixin, View):
    """Vista per assegnare utenti Staff a un cliente"""

    def get(self, request, pk):
        """Mostra il form per assegnare utenti a un cliente"""
        # Verifica i permessi (solo Manager possono assegnare utenti)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo i Manager possono assegnare utenti ai clienti')
            return redirect('cliente_detail', pk=pk)

        # Ottieni il cliente
        cliente = get_object_or_404(Cliente, pk=pk)

        # Verifica che il cliente appartenga all'organizzazione del Manager
        if request.user.is_manager() and request.user.organizzazione:
            if cliente.organizzazione != request.user.organizzazione:
                messages.error(request, 'Non puoi assegnare utenti a clienti di altre organizzazioni')
                return redirect('cliente_detail', pk=pk)

        # Prepara il form
        form = AssegnaUtentiForm(organizzazione=request.user.organizzazione)

        # Seleziona gli utenti già assegnati
        form.fields['utenti'].initial = cliente.utenti_assegnati.all()

        # Prepara il contesto
        context = {
            'cliente': cliente,
            'form': form
        }

        return render(request, 'clienti/assegna_utenti.html', context)

    def post(self, request, pk):
        """Processa il form per assegnare utenti a un cliente"""
        # Verifica i permessi (solo Manager possono assegnare utenti)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo i Manager possono assegnare utenti ai clienti')
            return redirect('cliente_detail', pk=pk)

        # Ottieni il cliente
        cliente = get_object_or_404(Cliente, pk=pk)

        # Verifica che il cliente appartenga all'organizzazione del Manager
        if request.user.is_manager() and request.user.organizzazione:
            if cliente.organizzazione != request.user.organizzazione:
                messages.error(request, 'Non puoi assegnare utenti a clienti di altre organizzazioni')
                return redirect('cliente_detail', pk=pk)

        # Processa il form
        form = AssegnaUtentiForm(request.POST, organizzazione=request.user.organizzazione)

        if form.is_valid():
            # Ottieni gli utenti selezionati
            utenti_selezionati = form.cleaned_data['utenti']

            # Aggiorna gli utenti assegnati
            cliente.utenti_assegnati.set(utenti_selezionati)

            messages.success(request, f'Utenti assegnati con successo al cliente {cliente.nome}')
            return redirect('cliente_detail', pk=pk)

        # Se il form non è valido, mostra nuovamente il form con gli errori
        context = {
            'cliente': cliente,
            'form': form
        }

        return render(request, 'clienti/assegna_utenti.html', context)


@login_required
def search_users(request):
    """API per cercare utenti in base a nome, cognome, email o username"""
    # Verifica i permessi
    if not (request.user.is_superuser or request.user.is_staff or request.user.is_manager()):
        return JsonResponse({'error': 'Permesso negato'}, status=403)

    # Ottieni il termine di ricerca
    q = request.GET.get('q', '').strip()

    if len(q) < 3:
        return JsonResponse({'error': 'Il termine di ricerca deve contenere almeno 3 caratteri'}, status=400)

    # Cerca utenti che corrispondono al termine di ricerca
    users = UtentePersonalizzato.objects.filter(
        Q(username__icontains=q) |
        Q(email__icontains=q) |
        Q(first_name__icontains=q) |
        Q(last_name__icontains=q)
    ).order_by('first_name', 'last_name')[:10]  # Limita a 10 risultati

    # Formatta i risultati
    results = [
        {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name
        }
        for user in users
    ]

    return JsonResponse(results, safe=False)


@login_required
def add_referente_existing(request, cliente_id):
    """Associa un utente esistente come referente di un cliente"""
    if request.method == 'POST':
        # Verifica i permessi (solo Admin e Manager possono aggiungere referenti)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo gli amministratori e i manager possono aggiungere referenti')
            return redirect('cliente_detail', pk=cliente_id)

        # Ottieni il cliente
        cliente = get_object_or_404(Cliente, pk=cliente_id)

        # Ottieni l'ID dell'utente
        user_id = request.POST.get('user_id')

        if not user_id:
            messages.error(request, 'Seleziona un utente valido')
            return redirect('cliente_detail', pk=cliente_id)

        try:
            # Ottieni l'utente
            utente = UtentePersonalizzato.objects.get(pk=user_id)

            # Verifica se il referente esiste già
            if Referente.objects.filter(utente=utente, cliente=cliente).exists():
                messages.warning(request, f'Referente {utente.first_name} {utente.last_name} è già associato a questo cliente')
            else:
                # Crea il referente
                Referente.objects.create(
                    utente=utente,
                    cliente=cliente
                )
                messages.success(request, f'Referente {utente.first_name} {utente.last_name} aggiunto con successo')

        except UtentePersonalizzato.DoesNotExist:
            messages.error(request, 'Utente non trovato')
        except Exception as e:
            messages.error(request, f'Errore nell\'aggiunta del referente: {str(e)}')

        return redirect('cliente_detail', pk=cliente_id)

    return redirect('cliente_list')


@login_required
def delete_referente(request, referente_id):
    """Elimina un referente da un cliente"""
    if request.method == 'POST':
        # Verifica i permessi (solo Admin e Manager possono eliminare referenti)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo gli amministratori e i manager possono eliminare i referenti')
            return redirect('cliente_list')

        # Ottieni il referente
        referente = get_object_or_404(Referente, pk=referente_id)
        cliente_id = referente.cliente.id

        try:
            # Elimina il referente
            referente.delete()
            messages.success(request, 'Referente rimosso con successo')
        except Exception as e:
            messages.error(request, f'Errore nella rimozione del referente: {str(e)}')

        return redirect('cliente_detail', pk=cliente_id)

    return redirect('cliente_list')