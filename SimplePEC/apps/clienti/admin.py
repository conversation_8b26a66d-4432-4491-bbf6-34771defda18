from django.contrib import admin
from .models import <PERSON><PERSON><PERSON>, Referente, Contatto

class ReferenteInline(admin.TabularInline):
    model = Referente
    extra = 1

class ClienteAdmin(admin.ModelAdmin):
    list_display = ('nome', 'codice', 'attivo', 'data_creazione')
    list_filter = ('attivo',)
    search_fields = ('nome', 'codice')
    inlines = [ReferenteInline]

class ReferenteAdmin(admin.ModelAdmin):
    list_display = ('utente', 'cliente')
    list_filter = ('cliente',)
    search_fields = ('utente__username', 'cliente__nome')

class ContattoAdmin(admin.ModelAdmin):
    list_display = ('nome', 'email', 'pec', 'telefono')
    search_fields = ('nome', 'email', 'pec', 'telefono')
    filter_horizontal = ('clienti',)

admin.site.register(Cliente, ClienteAdmin)
admin.site.register(Referente, ReferenteAdmin)
admin.site.register(<PERSON><PERSON><PERSON>, ContattoAdmin)
