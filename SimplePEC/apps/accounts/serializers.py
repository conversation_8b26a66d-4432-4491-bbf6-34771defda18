from rest_framework import serializers
from .models import UtentePersonalizza<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from apps.pec.models import AccountPEC

class GruppoUtentiSerializer(serializers.ModelSerializer):
    """Serializer per il modello GruppoUtenti"""
    
    class Meta:
        model = GruppoUtenti
        fields = ['id', 'nome', 'tipo', 'descrizione']


class AccountPECMinimalSerializer(serializers.ModelSerializer):
    """Serializer minimo per AccountPEC, usato in UtenteSerializer"""
    
    class Meta:
        model = AccountPEC
        fields = ['id', 'nome', 'indirizzo_email']


class UtenteSerializer(serializers.ModelSerializer):
    """Serializer per il modello UtentePersonalizzato"""
    
    gruppo_nome = serializers.StringRelatedField(source='gruppo', read_only=True)
    account_pec_autorizzati = AccountPECMinimalSerializer(many=True, read_only=True)
    
    class Meta:
        model = UtentePersonalizzato
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 
                  'is_active', 'gruppo', 'gruppo_nome', 'account_pec_autorizzati']
        extra_kwargs = {
            'password': {'write_only': True}
        }
    
    def create(self, validated_data):
        """Override per gestire la creazione di utenti con password criptata"""
        password = validated_data.pop('password', None)
        user = UtentePersonalizzato(**validated_data)
        if password:
            user.set_password(password)
        user.save()
        return user
    
    def update(self, instance, validated_data):
        """Override per gestire l'aggiornamento di utenti e password"""
        password = validated_data.pop('password', None)
        user = super().update(instance, validated_data)
        if password:
            user.set_password(password)
            user.save()
        return user


class AccountAssociazioneSerializer(serializers.Serializer):
    """Serializer per gestire l'associazione di account PEC a utenti"""
    
    account_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=True
    )