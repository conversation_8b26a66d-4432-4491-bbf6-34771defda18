from django import forms
from django.contrib.auth.forms import PasswordChangeForm
from .models import UtentePersonalizzato

class UserEditForm(forms.ModelForm):
    """Form per la modifica dei dati dell'utente"""
    
    class Meta:
        model = UtentePersonalizzato
        fields = ['first_name', 'last_name', 'email', 'telefono', 'immagine_profilo']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control'}),
            'immagine_profilo': forms.FileInput(attrs={'class': 'form-control'})
        }
        labels = {
            'first_name': 'Nome',
            'last_name': 'Cognome',
            'email': 'Email',
            'telefono': 'Telefono',
            'immagine_profilo': 'Immagine profilo'
        }

class CustomPasswordChangeForm(PasswordChangeForm):
    """Form personalizzato per il cambio password"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['old_password'].widget.attrs.update({'class': 'form-control'})
        self.fields['new_password1'].widget.attrs.update({'class': 'form-control'})
        self.fields['new_password2'].widget.attrs.update({'class': 'form-control'})
        
        self.fields['old_password'].label = 'Password attuale'
        self.fields['new_password1'].label = 'Nuova password'
        self.fields['new_password2'].label = 'Conferma nuova password'
