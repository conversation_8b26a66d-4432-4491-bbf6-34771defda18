from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import UtentePersonalizzato, GruppoUtenti, Organizzazione

class OrganizzazioneAdmin(admin.ModelAdmin):
    list_display = ('ragione_sociale', 'email', 'pec', 'telefono', 'data_creazione')
    search_fields = ('ragione_sociale', 'email', 'pec')
    list_filter = ('data_creazione',)
    fieldsets = (
        ('Informazioni principali', {'fields': ('ragione_sociale', 'logo')}),
        ('Contatti', {'fields': ('indirizzo', 'telefono', 'email', 'pec')}),
    )

class UtentePersonalizzatoAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'gruppo', 'organizzazione', 'is_staff')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'gruppo', 'organizzazione')
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Informazioni personali', {'fields': ('first_name', 'last_name', 'email', 'telefono', 'immagine_profilo')}),
        ('Permessi', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Date importanti', {'fields': ('last_login', 'date_joined')}),
        ('Impostazioni SimplePEC', {'fields': ('gruppo', 'organizzazione')}),
    )
    filter_horizontal = ('groups', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'gruppo', 'organizzazione'),
        }),
    )

class GruppoUtentiAdmin(admin.ModelAdmin):
    list_display = ('nome', 'tipo', 'descrizione')
    list_filter = ('tipo',)
    search_fields = ('nome', 'descrizione')

admin.site.register(UtentePersonalizzato, UtentePersonalizzatoAdmin)
admin.site.register(GruppoUtenti, GruppoUtentiAdmin)
admin.site.register(Organizzazione, OrganizzazioneAdmin)
