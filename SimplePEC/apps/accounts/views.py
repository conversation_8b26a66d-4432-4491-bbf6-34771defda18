from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import UtentePersonalizzato, GruppoUtenti, Organizzazione
from .serializers import UtenteSerializer, GruppoUtentiSerializer, AccountAssociazioneSerializer
from apps.pec.models import AccountPEC
from django.shortcuts import get_object_or_404, render, redirect
from django.contrib.auth.views import LoginView, LogoutView, PasswordChangeView
from django.views.generic import ListView, DetailView, TemplateView, UpdateView, CreateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponseRedirect, JsonResponse
import json
from .forms import UserEditForm, CustomPasswordChangeForm
from django.contrib.auth.decorators import login_required
from core.permissions import MANAGER_GROUP, STAFF_GROUP, add_user_to_group, create_user_groups

class GruppoUtentiViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sui gruppi utenti
    """
    queryset = GruppoUtenti.objects.all()
    serializer_class = GruppoUtentiSerializer
    permission_classes = [permissions.IsAdminUser]


class UtenteViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sugli utenti
    """
    queryset = UtentePersonalizzato.objects.all()
    serializer_class = UtenteSerializer

    def get_permissions(self):
        """
        Solo gli amministratori possono creare/modificare/eliminare utenti
        Gli utenti normali possono solo leggere i propri dati
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Gli amministratori vedono tutti gli utenti
        Gli utenti normali vedono solo se stessi
        """
        user = self.request.user
        if user.is_staff:
            return UtentePersonalizzato.objects.all()
        return UtentePersonalizzato.objects.filter(id=user.id)

    @action(detail=True, methods=['post'], url_path='account-autorizzati')
    def account_autorizzati(self, request, pk=None):
        """
        Modifica gli account PEC autorizzati per un utente
        """
        utente = self.get_object()
        serializer = AccountAssociazioneSerializer(data=request.data)

        if serializer.is_valid():
            account_ids = serializer.validated_data['account_ids']
            accounts = AccountPEC.objects.filter(id__in=account_ids)

            # Aggiorna gli account autorizzati
            utente.account_pec_autorizzati.set(accounts)

            # Restituisce l'utente aggiornato
            return Response(UtenteSerializer(utente).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Template views per il nuovo frontend Django
class CustomLoginView(LoginView):
    """Vista personalizzata per il login con template moderni"""
    template_name = 'auth/login.html'
    redirect_authenticated_user = True

    def form_valid(self, form):
        """Metodo chiamato quando il form di login è valido"""
        response = super().form_valid(form)
        # Disattiviamo completamente la modalità demo
        self.request.session['is_mock_mode'] = False
        
        # Controlla se l'utente deve cambiare la password al primo accesso
        user = form.get_user()
        if user.richiedi_cambio_password:
            messages.warning(self.request, 'È necessario cambiare la password per continuare a utilizzare il sistema.')
            # Imposta un flag nella sessione per indicare che l'utente deve cambiare la password
            self.request.session['richiedi_cambio_password'] = True
            # Reindirizza alla pagina di cambio password
            return redirect('password_change')
            
        return response

class UserListView(LoginRequiredMixin, ListView):
    """Lista degli utenti del sistema"""
    model = UtentePersonalizzato
    template_name = 'accounts/user_list.html'
    context_object_name = 'users'

    def get_queryset(self):
        user = self.request.user

        # Se l'utente è un superuser o staff di Django, vede tutti gli utenti
        if user.is_superuser or user.is_staff:
            return UtentePersonalizzato.objects.all()

        # Se l'utente è un Manager, vede tutti gli utenti della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return UtentePersonalizzato.objects.filter(organizzazione=user.organizzazione)

        # Gli utenti normali vedono solo se stessi
        return UtentePersonalizzato.objects.filter(id=user.id)

    def get_context_data(self, **kwargs):
        """Aggiunge le organizzazioni al contesto"""
        context = super().get_context_data(**kwargs)
        context['organizzazioni'] = Organizzazione.objects.all()
        return context

class UserDetailView(LoginRequiredMixin, DetailView):
    """Dettaglio di un utente"""
    model = UtentePersonalizzato
    template_name = 'accounts/user_detail.html'
    context_object_name = 'user_obj'

    def get_queryset(self):
        user = self.request.user

        # Se l'utente è un superuser o staff di Django, vede tutti gli utenti
        if user.is_superuser or user.is_staff:
            return UtentePersonalizzato.objects.all()

        # Se l'utente è un Manager, vede tutti gli utenti della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return UtentePersonalizzato.objects.filter(organizzazione=user.organizzazione)

        # Gli utenti normali vedono solo se stessi
        return UtentePersonalizzato.objects.filter(id=user.id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Aggiungi tutti gli account PEC disponibili per l'assegnazione
        from apps.pec.models import AccountPEC, LogAccesso, LogAssociazione, MessaggioPEC
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta, datetime
        from django.contrib.admin.models import LogEntry, ADDITION, CHANGE, DELETION
        from django.contrib.contenttypes.models import ContentType

        # Filtra gli account PEC in base all'utente corrente
        if self.request.user.is_staff:
            # Gli amministratori vedono tutti gli account PEC
            context['all_accounts'] = AccountPEC.objects.all()
        elif self.request.user.is_manager() and self.request.user.organizzazione:
            # I Manager vedono solo gli account PEC della propria organizzazione
            context['all_accounts'] = AccountPEC.objects.filter(organizzazione=self.request.user.organizzazione)
        else:
            # Gli utenti normali vedono solo gli account PEC a cui sono autorizzati
            context['all_accounts'] = self.request.user.account_pec_autorizzati.all()

        # Ottieni l'utente di cui stiamo visualizzando il profilo
        user_obj = self.get_object()

        # Calcola le statistiche
        # 1. Messaggi gestiti (visualizzati)
        messaggi_gestiti = LogAccesso.objects.filter(utente=user_obj).count()

        # 2. Assegnazioni effettuate
        assegnazioni = LogAssociazione.objects.filter(utente=user_obj).count()

        # 3. Sincronizzazioni (non abbiamo un log diretto, ma possiamo contare i messaggi per account)
        # Contiamo i messaggi degli account autorizzati come proxy per le sincronizzazioni
        account_autorizzati = user_obj.account_pec_autorizzati.all()
        sincronizzazioni = 0
        for account in account_autorizzati:
            if account.ultima_sincronizzazione:
                sincronizzazioni += 1

        # Aggiungi le statistiche al contesto
        context['statistiche'] = {
            'messaggi_gestiti': messaggi_gestiti,
            'assegnazioni': assegnazioni,
            'sincronizzazioni': sincronizzazioni
        }

        # Gestione del filtro per data delle attività
        data_inizio = self.request.GET.get('data_inizio', None)
        data_fine = self.request.GET.get('data_fine', None)

        # Gestione della scheda attiva
        tab_attiva = self.request.GET.get('tab', 'account')
        context['tab_attiva'] = tab_attiva

        # Imposta i valori predefiniti se non specificati
        oggi = timezone.now().date()
        if not data_inizio:
            data_inizio = oggi.strftime('%Y-%m-%d')
        if not data_fine:
            data_fine = oggi.strftime('%Y-%m-%d')

        # Converti le stringhe in oggetti date
        try:
            data_inizio_obj = datetime.strptime(data_inizio, '%Y-%m-%d').date()
            data_fine_obj = datetime.strptime(data_fine, '%Y-%m-%d').date()
            # Aggiungi un giorno alla data di fine per includere l'intero giorno
            data_fine_obj_next = data_fine_obj + timedelta(days=1)
        except ValueError:
            # In caso di errore, usa la data odierna
            data_inizio_obj = oggi
            data_fine_obj = oggi
            data_fine_obj_next = oggi + timedelta(days=1)
            data_inizio = oggi.strftime('%Y-%m-%d')
            data_fine = oggi.strftime('%Y-%m-%d')

        # Aggiungi le date al contesto per il form di filtro
        context['filtro_date'] = {
            'data_inizio': data_inizio,
            'data_fine': data_fine
        }

        # Raccogli i log di attività dell'utente
        attivita = []

        # 1. Log di accesso ai messaggi
        log_accessi = LogAccesso.objects.filter(
            utente=user_obj,
            data_accesso__date__gte=data_inizio_obj,
            data_accesso__date__lt=data_fine_obj_next
        ).order_by('-data_accesso')

        for log in log_accessi:
            attivita.append({
                'tipo': 'accesso',
                'data': log.data_accesso,
                'descrizione': f'Accesso al messaggio "{log.messaggio.oggetto}"',
                'oggetto': log.messaggio,
                'url': f'/messages/{log.messaggio.id}/'
            })

        # 2. Log di associazione messaggi-clienti
        log_associazioni = LogAssociazione.objects.filter(
            utente=user_obj,
            data_associazione__date__gte=data_inizio_obj,
            data_associazione__date__lt=data_fine_obj_next
        ).order_by('-data_associazione')

        for log in log_associazioni:
            attivita.append({
                'tipo': 'associazione',
                'data': log.data_associazione,
                'descrizione': f'Associazione del messaggio "{log.messaggio.oggetto}" al cliente "{log.cliente.nome}"',
                'oggetto': log.messaggio,
                'url': f'/messages/{log.messaggio.id}/'
            })

        # 3. Log di amministrazione (creazione/modifica/eliminazione)
        log_admin = LogEntry.objects.filter(
            user=user_obj,
            action_time__date__gte=data_inizio_obj,
            action_time__date__lt=data_fine_obj_next
        ).order_by('-action_time')

        for log in log_admin:
            if log.action_flag == ADDITION:
                azione = "Creazione"
            elif log.action_flag == CHANGE:
                azione = "Modifica"
            elif log.action_flag == DELETION:
                azione = "Eliminazione"
            else:
                azione = "Azione"

            attivita.append({
                'tipo': 'admin',
                'data': log.action_time,
                'descrizione': f'{azione} di {log.object_repr}',
                'oggetto': None,
                'url': None
            })

        # Ordina tutte le attività per data (più recenti prima)
        attivita.sort(key=lambda x: x['data'], reverse=True)

        context['attivita'] = attivita

        # Dati per il grafico di attività nel tempo
        # Prendiamo gli ultimi 30 giorni
        oggi = timezone.now().date()
        inizio_periodo = oggi - timedelta(days=30)

        # Contiamo gli accessi per giorno
        accessi_per_giorno = LogAccesso.objects.filter(
            utente=user_obj,
            data_accesso__date__gte=inizio_periodo
        ).extra(
            select={'giorno': "DATE(data_accesso)"}
        ).values('giorno').annotate(
            conteggio=Count('id')
        ).order_by('giorno')

        # Prepara i dati per il grafico
        labels = []
        dati = []

        # Crea un dizionario con i conteggi per ogni giorno
        conteggi_per_giorno = {}
        for x in accessi_per_giorno:
            giorno = x['giorno']
            # Gestisci sia il caso in cui giorno sia un oggetto datetime che una stringa
            if hasattr(giorno, 'strftime'):
                data_str = giorno.strftime('%Y-%m-%d')
            else:
                # Se è una stringa, usala direttamente
                data_str = str(giorno)
            conteggi_per_giorno[data_str] = x['conteggio']

        # Genera le etichette e i dati per tutti i giorni nel periodo
        data_corrente = inizio_periodo
        while data_corrente <= oggi:
            data_str = data_corrente.strftime('%Y-%m-%d')
            labels.append(data_corrente.strftime('%d/%m'))
            dati.append(conteggi_per_giorno.get(data_str, 0))
            data_corrente += timedelta(days=1)

        import json
        context['grafico_attivita'] = {
            'labels': json.dumps(labels),
            'dati': json.dumps(dati)
        }

        return context

class ProfileView(LoginRequiredMixin, TemplateView):
    """Profilo dell'utente corrente"""
    template_name = 'accounts/profile.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_obj'] = self.request.user
        return context

class AssegnaAccountView(LoginRequiredMixin, DetailView):
    """Vista per assegnare account PEC a un utente"""
    model = UtentePersonalizzato
    template_name = 'accounts/user_detail.html'
    context_object_name = 'user_obj'

    def get_queryset(self):
        user = self.request.user

        # Se l'utente è un superuser o staff di Django, vede tutti gli utenti
        if user.is_superuser or user.is_staff:
            return UtentePersonalizzato.objects.all()

        # Se l'utente è un Manager, vede tutti gli utenti della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return UtentePersonalizzato.objects.filter(organizzazione=user.organizzazione)

        # Gli utenti normali vedono solo se stessi
        return UtentePersonalizzato.objects.filter(id=user.id)

    def post(self, request, *args, **kwargs):
        # Verifica i permessi
        if not (request.user.is_superuser or request.user.is_staff or request.user.is_manager()):
            messages.error(request, "Non hai i permessi per assegnare account agli utenti")
            return redirect('user_list')

        user = self.get_object()

        # Se l'utente è un Manager, può assegnare account solo agli utenti della sua organizzazione
        if request.user.is_manager() and not request.user.is_staff:
            if user.organizzazione != request.user.organizzazione:
                messages.error(request, "Non puoi assegnare account a utenti di altre organizzazioni")
                return redirect('user_list')

        account_ids = request.POST.getlist('account_ids')

        # Rimuovi tutti gli account esistenti se l'opzione è selezionata
        if 'clear_existing' in request.POST:
            user.account_pec_autorizzati.clear()

        # Aggiungi i nuovi account selezionati
        from apps.pec.models import AccountPEC, UtenteAccountPEC
        for account_id in account_ids:
            try:
                account = AccountPEC.objects.get(id=account_id)

                # Se l'utente è un Manager, verifica che l'account appartenga alla sua organizzazione
                if request.user.is_manager() and not request.user.is_staff:
                    if account.organizzazione != request.user.organizzazione:
                        messages.error(request, f"Non puoi assegnare l'account {account.nome} perché non appartiene alla tua organizzazione")
                        continue

                # Aggiungi l'account all'utente tramite il modello intermedio
                user.account_pec_autorizzati.add(account)

                # Ottieni o crea l'oggetto UtenteAccountPEC
                autorizzazione, created = UtenteAccountPEC.objects.get_or_create(
                    utente=user,
                    account=account
                )

                # Aggiorna i permessi specifici
                autorizzazione.puo_aggiornare_stato = f'puo_aggiornare_stato_{account.id}' in request.POST
                autorizzazione.puo_assegnare_cliente = f'puo_assegnare_cliente_{account.id}' in request.POST
                autorizzazione.puo_assegnare_referente = f'puo_assegnare_referente_{account.id}' in request.POST
                autorizzazione.save()

            except AccountPEC.DoesNotExist:
                pass

        return redirect('user_detail', pk=user.id)


class TogglePermissionView(LoginRequiredMixin, View):
    """Vista per attivare/disattivare un permesso specifico per un utente su un account PEC"""

    def post(self, request, user_id):
        # Verifica i permessi
        if not (request.user.is_superuser or request.user.is_staff or request.user.is_manager()):
            return JsonResponse({
                'status': 'error',
                'message': "Non hai i permessi per modificare i permessi degli utenti"
            }, status=403)

        try:
            # Ottieni i dati dalla richiesta JSON
            data = json.loads(request.body)
            permission_type = data.get('permission_type')
            account_id = data.get('account_id')

            # Verifica che i dati necessari siano presenti
            if not permission_type or not account_id:
                return JsonResponse({
                    'status': 'error',
                    'message': "Dati mancanti nella richiesta"
                }, status=400)

            # Verifica che il tipo di permesso sia valido
            valid_permissions = ['puo_aggiornare_stato', 'puo_assegnare_cliente', 'puo_assegnare_referente']
            if permission_type not in valid_permissions:
                return JsonResponse({
                    'status': 'error',
                    'message': f"Tipo di permesso non valido: {permission_type}"
                }, status=400)

            # Ottieni l'utente e l'account
            user = UtentePersonalizzato.objects.get(id=user_id)
            account = AccountPEC.objects.get(id=account_id)

            # Se l'utente è un Manager, può modificare permessi solo per utenti della sua organizzazione
            if request.user.is_manager() and not request.user.is_staff:
                if user.organizzazione != request.user.organizzazione:
                    return JsonResponse({
                        'status': 'error',
                        'message': "Non puoi modificare i permessi di utenti di altre organizzazioni"
                    }, status=403)

                # Verifica anche che l'account appartenga alla sua organizzazione
                if account.organizzazione != request.user.organizzazione:
                    return JsonResponse({
                        'status': 'error',
                        'message': f"Non puoi modificare i permessi per l'account {account.nome} perché non appartiene alla tua organizzazione"
                    }, status=403)

            # Ottieni o crea l'autorizzazione
            from apps.pec.models import UtenteAccountPEC
            autorizzazione, created = UtenteAccountPEC.objects.get_or_create(
                utente=user,
                account=account
            )

            # Ottieni il valore attuale del permesso
            current_value = getattr(autorizzazione, permission_type)

            # Inverti il valore del permesso
            setattr(autorizzazione, permission_type, not current_value)
            autorizzazione.save()

            # Restituisci il nuovo stato del permesso
            return JsonResponse({
                'status': 'success',
                'enabled': getattr(autorizzazione, permission_type),
                'message': f"Permesso {permission_type} aggiornato con successo"
            })

        except UtentePersonalizzato.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': "Utente non trovato"
            }, status=404)
        except AccountPEC.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': "Account PEC non trovato"
            }, status=404)
        except json.JSONDecodeError:
            return JsonResponse({
                'status': 'error',
                'message': "Formato JSON non valido"
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f"Errore: {str(e)}"
            }, status=500)


class RimuoviAccountView(LoginRequiredMixin, View):
    """Vista per rimuovere un singolo account PEC da un utente"""

    def post(self, request, user_id, account_id):
        # Verifica i permessi
        if not (request.user.is_superuser or request.user.is_staff or request.user.is_manager()):
            messages.error(request, "Non hai i permessi per rimuovere account dagli utenti")
            return redirect('user_list')

        # Ottieni l'utente e l'account
        try:
            user = UtentePersonalizzato.objects.get(id=user_id)
            account = AccountPEC.objects.get(id=account_id)
        except (UtentePersonalizzato.DoesNotExist, AccountPEC.DoesNotExist):
            messages.error(request, "Utente o account non trovato")
            return redirect('user_list')

        # Se l'utente è un Manager, può rimuovere account solo dagli utenti della sua organizzazione
        if request.user.is_manager() and not request.user.is_staff:
            if user.organizzazione != request.user.organizzazione:
                messages.error(request, "Non puoi rimuovere account da utenti di altre organizzazioni")
                return redirect('user_list')

            # Verifica anche che l'account appartenga alla sua organizzazione
            if account.organizzazione != request.user.organizzazione:
                messages.error(request, f"Non puoi rimuovere l'account {account.nome} perché non appartiene alla tua organizzazione")
                return redirect('user_detail', pk=user_id)

        # Rimuovi l'account dall'utente
        if account in user.account_pec_autorizzati.all():
            user.account_pec_autorizzati.remove(account)
            messages.success(request, f"Account {account.nome} rimosso con successo dall'utente {user.get_full_name()}")
        else:
            messages.warning(request, f"L'account {account.nome} non era assegnato all'utente {user.get_full_name()}")

        return redirect('user_detail', pk=user_id)

class UserEditView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """Vista per la modifica dei dati dell'utente"""
    model = UtentePersonalizzato
    form_class = UserEditForm
    template_name = 'accounts/user_edit.html'
    context_object_name = 'user_obj'

    def test_func(self):
        """Verifica che l'utente possa modificare il profilo"""
        user_obj = self.get_object()

        # L'utente può modificare il proprio profilo
        if self.request.user.id == user_obj.id:
            return True

        # Gli admin possono modificare qualsiasi utente
        if self.request.user.is_superuser or self.request.user.is_staff:
            return True

        # I Manager possono modificare gli utenti della loro organizzazione
        if self.request.user.is_manager() and self.request.user.organizzazione:
            return user_obj.organizzazione == self.request.user.organizzazione

        return False

    def get_success_url(self):
        """Ritorna l'URL di successo dopo la modifica"""
        messages.success(self.request, "Profilo aggiornato con successo")
        return reverse('user_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        """Gestisce il form valido"""
        # Se è stata caricata una nuova immagine, elimina la vecchia
        if 'immagine_profilo' in form.changed_data and self.object.immagine_profilo:
            # Salva il riferimento alla vecchia immagine
            old_image = self.object.immagine_profilo

            # Dopo il salvataggio del form, elimina la vecchia immagine
            if old_image:
                import os
                if os.path.isfile(old_image.path):
                    os.remove(old_image.path)

        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        """Aggiunge dati al contesto"""
        context = super().get_context_data(**kwargs)
        context['password_form'] = CustomPasswordChangeForm(self.request.user)
        # Aggiungi le organizzazioni al contesto
        context['organizzazioni'] = Organizzazione.objects.all()
        return context

    def post(self, request, *args, **kwargs):
        """Gestisce la richiesta POST"""
        self.object = self.get_object()
        form = self.get_form()

        if form.is_valid():
            # Gestisci l'organizzazione se l'utente è staff
            if request.user.is_staff and 'organizzazione' in request.POST:
                organizzazione_id = request.POST.get('organizzazione')
                if organizzazione_id:
                    try:
                        organizzazione = Organizzazione.objects.get(id=organizzazione_id)
                        self.object.organizzazione = organizzazione
                    except Organizzazione.DoesNotExist:
                        self.object.organizzazione = None
                else:
                    self.object.organizzazione = None

            # Gestisci il ruolo utente (Manager o Staff)
            if (request.user.is_staff or request.user.is_manager()) and 'user_role' in request.POST:
                user_role = request.POST.get('user_role')

                # Verifica se l'utente è un Manager e sta cercando di modificare un utente della sua organizzazione
                if request.user.is_manager() and not request.user.is_staff:
                    if self.object.organizzazione != request.user.organizzazione:
                        messages.error(request, "Non puoi modificare il ruolo di utenti di altre organizzazioni")
                        return redirect('user_detail', pk=self.object.pk)

                # Imposta il ruolo dell'utente
                if user_role == 'manager':
                    from core.permissions import set_user_as_manager
                    set_user_as_manager(self.object)
                    messages.success(request, f"L'utente {self.object.get_full_name()} è stato impostato come Manager")
                elif user_role == 'staff':
                    from core.permissions import set_user_as_staff
                    set_user_as_staff(self.object)
                    messages.success(request, f"L'utente {self.object.get_full_name()} è stato impostato come Staff")
                elif user_role == '':
                    # Rimuovi l'utente da entrambi i gruppi
                    from core.permissions import remove_user_from_group, MANAGER_GROUP, STAFF_GROUP
                    if self.object.is_manager():
                        remove_user_from_group(self.object, MANAGER_GROUP)
                    if self.object.is_staff_user():
                        remove_user_from_group(self.object, STAFF_GROUP)
                    messages.success(request, f"L'utente {self.object.get_full_name()} è stato rimosso dai gruppi Manager e Staff")

            return self.form_valid(form)
        else:
            return self.form_invalid(form)

class UserPasswordChangeView(LoginRequiredMixin, UserPassesTestMixin, PasswordChangeView):
    """Vista per il cambio password"""
    form_class = CustomPasswordChangeForm
    template_name = 'accounts/password_change.html'

    def test_func(self):
        """Verifica che l'utente possa cambiare la password"""
        user_obj = get_object_or_404(UtentePersonalizzato, pk=self.kwargs['pk'])

        # L'utente può cambiare la propria password
        if self.request.user.id == user_obj.id:
            return True

        # Gli admin possono cambiare la password di qualsiasi utente
        if self.request.user.is_superuser or self.request.user.is_staff:
            return True

        # I Manager possono cambiare la password degli utenti della loro organizzazione
        if self.request.user.is_manager() and self.request.user.organizzazione:
            return user_obj.organizzazione == self.request.user.organizzazione

        return False

    def get_success_url(self):
        """Ritorna l'URL di successo dopo il cambio password"""
        messages.success(self.request, "Password cambiata con successo")
        
        # Resetta il flag richiedi_cambio_password se presente
        user = self.request.user
        if user.richiedi_cambio_password:
            user.richiedi_cambio_password = False
            user.save()
            
            # Rimuovi il flag dalla sessione
            if 'richiedi_cambio_password' in self.request.session:
                del self.request.session['richiedi_cambio_password']
        
        return reverse('user_detail', kwargs={'pk': self.kwargs['pk']})


# Viste per la gestione delle organizzazioni
class OrganizzazioneListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """Lista delle organizzazioni"""
    model = Organizzazione
    template_name = 'accounts/organizzazione_list.html'
    context_object_name = 'organizzazioni'

    def test_func(self):
        """Gli amministratori e i Manager possono vedere la lista delle organizzazioni"""
        return self.request.user.is_staff or self.request.user.is_manager()

    def get_queryset(self):
        """
        Gli amministratori vedono tutte le organizzazioni,
        i Manager vedono solo la propria organizzazione
        """
        if self.request.user.is_staff:
            return Organizzazione.objects.all()
        elif self.request.user.is_manager() and self.request.user.organizzazione:
            return Organizzazione.objects.filter(id=self.request.user.organizzazione.id)
        return Organizzazione.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Aggiungi statistiche per ogni organizzazione
        for org in context['organizzazioni']:
            org.num_utenti = org.utenti.count()
            org.num_account = org.account_pec.count()

        # Aggiungi un flag per indicare se l'utente è un amministratore
        context['is_admin'] = self.request.user.is_staff
        return context


class OrganizzazioneDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """Dettaglio di un'organizzazione"""
    model = Organizzazione
    template_name = 'accounts/organizzazione_detail.html'
    context_object_name = 'organizzazione'

    def test_func(self):
        """Solo gli amministratori o gli utenti dell'organizzazione possono vedere i dettagli"""
        if self.request.user.is_staff:
            return True
        return self.request.user.organizzazione and self.request.user.organizzazione.id == self.get_object().id

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        organizzazione = self.get_object()

        # Aggiungi gli utenti dell'organizzazione
        context['utenti'] = organizzazione.utenti.all()

        # Aggiungi gli account PEC dell'organizzazione
        context['account_pec'] = organizzazione.account_pec.all()

        # Aggiungi tutti gli utenti e account PEC per i modali di associazione
        context['all_users'] = UtentePersonalizzato.objects.all()
        context['all_accounts'] = AccountPEC.objects.all()

        # Aggiungi un flag per indicare se l'utente è un amministratore
        context['is_admin'] = self.request.user.is_staff

        return context


class OrganizzazioneCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Creazione di una nuova organizzazione"""
    model = Organizzazione
    template_name = 'accounts/organizzazione_form.html'
    fields = ['ragione_sociale', 'indirizzo', 'telefono', 'email', 'pec', 'logo']

    def test_func(self):
        """Solo gli amministratori possono creare organizzazioni"""
        return self.request.user.is_staff

    def get_success_url(self):
        messages.success(self.request, "Organizzazione creata con successo")
        return reverse('organizzazione_detail', kwargs={'pk': self.object.pk})


class OrganizzazioneUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """Modifica di un'organizzazione esistente"""
    model = Organizzazione
    template_name = 'accounts/organizzazione_form.html'
    fields = ['ragione_sociale', 'indirizzo', 'telefono', 'email', 'pec', 'logo']

    def test_func(self):
        """
        Gli amministratori possono modificare qualsiasi organizzazione,
        i Manager possono modificare solo la propria organizzazione
        """
        if self.request.user.is_staff:
            return True

        # Verifica se l'utente è un Manager e sta cercando di modificare la propria organizzazione
        if self.request.user.is_manager() and self.request.user.organizzazione:
            return self.request.user.organizzazione.id == self.get_object().id

        return False

    def get_success_url(self):
        messages.success(self.request, "Organizzazione aggiornata con successo")
        return reverse('organizzazione_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        """Gestisce il form valido"""
        # Se è stato caricato un nuovo logo, elimina il vecchio
        if 'logo' in form.changed_data and self.object.logo:
            # Salva il riferimento al vecchio logo
            old_logo = self.object.logo

            # Dopo il salvataggio del form, elimina il vecchio logo
            if old_logo:
                import os
                if os.path.isfile(old_logo.path):
                    os.remove(old_logo.path)

        return super().form_valid(form)


class OrganizzazioneDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """Eliminazione di un'organizzazione"""
    model = Organizzazione
    template_name = 'accounts/organizzazione_confirm_delete.html'
    success_url = reverse_lazy('organizzazione_list')

    def test_func(self):
        """Solo gli amministratori possono eliminare le organizzazioni"""
        return self.request.user.is_staff

    def delete(self, request, *args, **kwargs):
        messages.success(request, "Organizzazione eliminata con successo")
        return super().delete(request, *args, **kwargs)


class OrganizzazioneAssociaUtentiView(LoginRequiredMixin, UserPassesTestMixin, View):
    """Associa utenti a un'organizzazione"""

    def test_func(self):
        """Solo gli amministratori possono associare utenti alle organizzazioni"""
        return self.request.user.is_staff

    def post(self, request, pk):
        organizzazione = get_object_or_404(Organizzazione, pk=pk)
        user_ids = request.POST.getlist('user_ids')
        clear_existing = 'clear_existing_users' in request.POST

        try:
            # Se richiesto, rimuovi tutte le associazioni esistenti
            if clear_existing:
                UtentePersonalizzato.objects.filter(organizzazione=organizzazione).update(organizzazione=None)

            # Associa gli utenti selezionati
            if user_ids:
                utenti = UtentePersonalizzato.objects.filter(id__in=user_ids)
                for utente in utenti:
                    utente.organizzazione = organizzazione
                    utente.save()

                messages.success(request, f"{len(utenti)} utenti associati a {organizzazione.ragione_sociale}")
            else:
                messages.warning(request, "Nessun utente selezionato")
        except Exception as e:
            messages.error(request, f"Errore durante l'associazione degli utenti: {str(e)}")

        return redirect('organizzazione_detail', pk=pk)


@login_required
def create_user(request):
    """Vista per la creazione di un nuovo utente"""
    # Verifica i permessi (solo admin e Manager possono creare utenti)
    if not (request.user.is_superuser or request.user.is_staff or request.user.is_manager()):
        messages.error(request, "Non hai i permessi per creare nuovi utenti")
        return redirect('user_list')

    # Assicurati che i gruppi Manager e Staff esistano
    create_user_groups()

    if request.method == 'POST':
        # Estrai i dati dal form
        first_name = request.POST.get('first_name', '')
        last_name = request.POST.get('last_name', '')
        email = request.POST.get('email', '')
        is_active = 'is_active' in request.POST
        is_staff = False  # Imposta sempre a False indipendentemente dal valore inviato
        organizzazione_id = request.POST.get('organizzazione', '')
        user_role = request.POST.get('user_role', '')  # 'manager' o 'staff'

        # Validazione
        if not (first_name and last_name and email):
            messages.error(request, "Tutti i campi obbligatori devono essere compilati")
            return redirect('user_list')

        if not user_role:
            messages.error(request, "È necessario selezionare un ruolo (Manager o Staff)")
            return redirect('user_list')

        # Verifica se l'email è già in uso
        if UtentePersonalizzato.objects.filter(email=email).exists():
            messages.error(request, f"L'email '{email}' è già in uso")
            return redirect('user_list')
            
        # Usa l'email come username
        username = email
        
        # Genera una password casuale
        import random, string
        password = ''.join(random.choices(string.ascii_letters + string.digits + string.punctuation, k=12))

        try:
            # Crea il nuovo utente
            utente = UtentePersonalizzato.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                is_active=is_active,
                is_staff=is_staff,
                richiedi_cambio_password=True  # Imposta il flag per richiedere il cambio password
            )
            
            # Invia email con le credenziali
            from django.core.mail import send_mail
            from django.conf import settings
            
            subject = 'Credenziali di accesso - SimplePEC'
            message = f"""Gentile {first_name} {last_name},

Sono state create le tue credenziali di accesso per SimplePEC:

Username: {username}
Password temporanea: {password}

Al primo accesso ti verrà richiesto di cambiare la password.

Cordiali saluti,
Il team di SimplePEC"""
            
            try:
                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                    [email],
                    fail_silently=False,
                )
                messages.success(request, f"Utente creato con successo. Le credenziali sono state inviate all'indirizzo {email}")
            except Exception as e:
                messages.warning(request, f"Utente creato con successo ma non è stato possibile inviare l'email con le credenziali: {str(e)}")
                messages.info(request, f"Username: {username} - Password temporanea: {password}")


            # Gestione dell'organizzazione - imposta sempre l'organizzazione dell'utente corrente
            if request.user.organizzazione:
                utente.organizzazione = request.user.organizzazione
                utente.save()
            else:
                messages.warning(request, "Non sei associato a nessuna organizzazione, l'utente è stato creato senza organizzazione")

            # Assegna il ruolo all'utente (Manager o Staff)
            if user_role == 'manager':
                add_user_to_group(utente, MANAGER_GROUP)
                messages.info(request, f"Utente aggiunto al gruppo {MANAGER_GROUP}")
            elif user_role == 'staff':
                add_user_to_group(utente, STAFF_GROUP)
                messages.info(request, f"Utente aggiunto al gruppo {STAFF_GROUP}")
            else:
                messages.warning(request, f"Ruolo non specificato o non valido: '{user_role}'")

            messages.success(request, f"Utente {utente.get_full_name()} creato con successo")
            return redirect('user_detail', pk=utente.id)
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            messages.error(request, f"Errore nella creazione dell'utente: {str(e)}")
            messages.error(request, f"Dettagli errore: {error_traceback}")
            return redirect('user_list')

    # Se la richiesta non è POST, reindirizza alla lista utenti
    return redirect('user_list')

class OrganizzazioneAssociaAccountPECView(LoginRequiredMixin, UserPassesTestMixin, View):
    """Associa account PEC a un'organizzazione"""

    def test_func(self):
        """Solo gli amministratori possono associare account PEC alle organizzazioni"""
        return self.request.user.is_staff

    def post(self, request, pk):
        organizzazione = get_object_or_404(Organizzazione, pk=pk)
        account_ids = request.POST.getlist('account_ids')
        clear_existing = 'clear_existing_accounts' in request.POST

        try:
            # Se richiesto, rimuovi tutte le associazioni esistenti
            if clear_existing:
                AccountPEC.objects.filter(organizzazione=organizzazione).update(organizzazione=None)

            # Associa gli account PEC selezionati
            if account_ids:
                accounts = AccountPEC.objects.filter(id__in=account_ids)
                for account in accounts:
                    account.organizzazione = organizzazione
                    account.save()

                messages.success(request, f"{len(accounts)} account PEC associati a {organizzazione.ragione_sociale}")
            else:
                messages.warning(request, "Nessun account PEC selezionato")
        except Exception as e:
            messages.error(request, f"Errore durante l'associazione degli account PEC: {str(e)}")

        return redirect('organizzazione_detail', pk=pk)
