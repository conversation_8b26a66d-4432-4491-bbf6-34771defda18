# Generated by Django 5.2 on 2025-05-06 15:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_utentepersonalizzato_immagine_profilo'),
    ]

    operations = [
        migrations.CreateModel(
            name='Organizzazione',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ragione_sociale', models.CharField(max_length=200)),
                ('indirizzo', models.CharField(blank=True, max_length=255)),
                ('telefono', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('pec', models.EmailField(blank=True, max_length=254)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='organizzazioni/')),
                ('data_creazione', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Organizzazione',
                'verbose_name_plural': 'Organizzazioni',
            },
        ),
        migrations.AddField(
            model_name='utentepersonalizzato',
            name='organizzazione',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='utenti', to='accounts.organizzazione'),
        ),
    ]
