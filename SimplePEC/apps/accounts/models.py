from django.db import models
from django.contrib.auth.models import AbstractUser

class Organizzazione(models.Model):
    ragione_sociale = models.CharField(max_length=200)
    indirizzo = models.CharField(max_length=255, blank=True)
    telefono = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    pec = models.EmailField(blank=True)
    logo = models.ImageField(upload_to='organizzazioni/', blank=True, null=True)
    data_creazione = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.ragione_sociale

    class Meta:
        verbose_name = "Organizzazione"
        verbose_name_plural = "Organizzazioni"

class GruppoUtenti(models.Model):
    TIPO_CHOICES = (
        ('AMM', 'Amministrazione'),
        ('RIF', 'Riferimento'),
        ('LEG', 'Legale'),
    )
    nome = models.Char<PERSON>ield(max_length=100)
    tipo = models.Char<PERSON>ield(max_length=3, choices=TIPO_CHOICES)
    descrizione = models.TextField(blank=True)

    def __str__(self):
        return self.nome

    class Meta:
        verbose_name = "Gruppo Utenti"
        verbose_name_plural = "Gruppi Utenti"

class UtentePersonalizzato(AbstractUser):
    gruppo = models.ForeignKey(GruppoUtenti, on_delete=models.SET_NULL, null=True)
    organizzazione = models.ForeignKey(Organizzazione, on_delete=models.SET_NULL, null=True, blank=True, related_name='utenti')
    telefono = models.CharField(max_length=20, blank=True, default='')
    immagine_profilo = models.ImageField(upload_to='profili/', blank=True, null=True)
    richiedi_cambio_password = models.BooleanField(default=False, help_text='Se attivo, l\'utente dovrà cambiare la password al primo accesso')
    # La relazione con AccountPEC viene gestita in modo diverso per evitare importazioni circolari
    # account_pec_autorizzati verrà definita come una relazione ManyToManyField in un modello separato

    def __str__(self):
        return f"{self.first_name} {self.last_name}" if self.first_name else self.username

    def is_manager(self):
        """Verifica se l'utente è un Manager"""
        from core.permissions import is_manager
        return is_manager(self)

    def is_staff_user(self):
        """Verifica se l'utente è uno Staff"""
        from core.permissions import is_staff
        return is_staff(self)

    def get_role(self):
        """Restituisce il ruolo dell'utente (Manager, Staff o None)"""
        from core.permissions import get_user_role
        return get_user_role(self)

    def get_assigned_accounts(self):
        """Restituisce gli account PEC assegnati all'utente"""
        if self.is_manager() and self.organizzazione:
            # I Manager vedono tutti gli account della loro organizzazione
            from apps.pec.models import AccountPEC
            return AccountPEC.objects.filter(organizzazione=self.organizzazione)
        else:
            # Gli Staff vedono solo gli account a cui sono stati assegnati
            return self.account_pec_autorizzati.all()

    class Meta:
        verbose_name = "Utente"
        verbose_name_plural = "Utenti"
