from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.views import PasswordChangeView
from django.urls import reverse
from django.contrib import messages
from .forms import CustomPasswordChangeForm

class PasswordChangeViewGeneral(LoginRequiredMixin, PasswordChangeView):
    """Vista per il cambio password senza parametro pk"""
    form_class = CustomPasswordChangeForm
    template_name = 'accounts/password_change.html'

    def get_success_url(self):
        """Ritorna l'URL di successo dopo il cambio password"""
        messages.success(self.request, "Password cambiata con successo")
        
        # Resetta il flag richiedi_cambio_password se presente
        user = self.request.user
        if user.richiedi_cambio_password:
            user.richiedi_cambio_password = False
            user.save()
            
            # Rimuovi il flag dalla sessione
            if 'richiedi_cambio_password' in self.request.session:
                del self.request.session['richiedi_cambio_password']
        
        return reverse('user_detail', kwargs={'pk': self.request.user.pk})