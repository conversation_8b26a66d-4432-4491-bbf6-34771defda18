from django.urls import path
from .views import (
    CustomLoginView, UserListView, UserDetailView, ProfileView,
    AssegnaAccountView, RimuoviAccountView, UserEditView, UserPasswordChangeView,
    OrganizzazioneListView, OrganizzazioneDetailView, OrganizzazioneCreateView,
    OrganizzazioneUpdateView, OrganizzazioneDeleteView, OrganizzazioneAssociaUtentiView,
    OrganizzazioneAssociaAccountPECView, create_user, TogglePermissionView
)
from .views_password import PasswordChangeViewGeneral
from django.contrib.auth.views import LogoutView
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Aggiungiamo percorsi per entrambi i pattern di URL
    path('login/', CustomLoginView.as_view(), name='login'),
    path('accounts/login/', CustomLoginView.as_view()),  # URL alternativo usato da Django di default
    path('logout/', LogoutView.as_view(), name='logout'),
    path('accounts/logout/', LogoutView.as_view()),  # URL alternativo
    path('profile/', ProfileView.as_view(), name='profile'),
    
    # Aggiungiamo il percorso per il cambio password
    path('password_change/', PasswordChangeViewGeneral.as_view(), name='password_change'),

    # Utenti
    path('users/', UserListView.as_view(), name='user_list'),
    path('users/create/', create_user, name='user_create'),
    path('users/<int:pk>/', UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/assegna-account/', AssegnaAccountView.as_view(), name='assegna_account'),
    path('users/<int:user_id>/rimuovi-account/<int:account_id>/', RimuoviAccountView.as_view(), name='rimuovi_account'),
    path('users/<int:user_id>/toggle-permission/', TogglePermissionView.as_view(), name='toggle_permission'),
    path('users/<int:pk>/edit/', UserEditView.as_view(), name='user_edit'),
    path('users/<int:pk>/password/', UserPasswordChangeView.as_view(), name='user_password_change'),

    # Organizzazioni
    path('organizzazioni/', OrganizzazioneListView.as_view(), name='organizzazione_list'),
    path('organizzazioni/create/', OrganizzazioneCreateView.as_view(), name='organizzazione_create'),
    path('organizzazioni/<int:pk>/', OrganizzazioneDetailView.as_view(), name='organizzazione_detail'),
    path('organizzazioni/<int:pk>/edit/', OrganizzazioneUpdateView.as_view(), name='organizzazione_update'),
    path('organizzazioni/<int:pk>/delete/', OrganizzazioneDeleteView.as_view(), name='organizzazione_delete'),
    path('organizzazioni/<int:pk>/associa-utenti/', OrganizzazioneAssociaUtentiView.as_view(), name='organizzazione_associa_utenti'),
    path('organizzazioni/<int:pk>/associa-account-pec/', OrganizzazioneAssociaAccountPECView.as_view(), name='organizzazione_associa_account_pec'),
]

# Aggiungi gli URL per i file media in modalità debug
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)