from django import template

register = template.Library()

@register.filter
def unread_messages_count(accounts):
    """
    Conta i messaggi da leggere per gli account PEC specificati.
    Uso: {{ user.account_pec_autorizzati.all|unread_messages_count }}
    """
    if not accounts:
        return 0

    # Importiamo qui per evitare importazioni circolari
    from apps.pec.models import MessaggioPEC

    # Contiamo i messaggi con stato 'DA_LEGGERE' per tutti gli account specificati
    unread_count = MessaggioPEC.objects.filter(
        account_pec__in=accounts,
        stato='DA_LEGGERE'
    ).count()

    return unread_count

@register.filter
def split(value, delimiter):
    """
    Divide una stringa in base al delimitatore specificato.
    Uso: {{ "a,b,c"|split:"," }} -> ['a', 'b', 'c']
    """
    return value.split(delimiter)

@register.filter
def index(value, index):
    """
    Restituisce l'elemento all'indice specificato.
    Uso: {{ my_list|index:0 }} -> primo elemento
    """
    try:
        return value[index]
    except (IndexError, TypeError):
        return ''
