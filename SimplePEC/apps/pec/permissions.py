from rest_framework import permissions

class PermessiPEC:
    """
    Classe di utilità per la gestione dei permessi relativi alla PEC
    """

    @staticmethod
    def is_ricevuta(messaggio):
        """
        Verifica se un messaggio è una ricevuta PEC
        Questa funzione è utilizzata per scopi informativi e di visualizzazione
        """
        import re
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Verificando se il messaggio {messaggio.id} è una ricevuta. Oggetto: {messaggio.oggetto}")

        # Per risolvere il problema dei permessi, dal punto di vista del controllo
        # autorizzazioni consideriamo tutti i messaggi come non ricevute
        # Questo garantisce che i permessi vengano sempre applicati in base alle 
        # impostazioni account-utente indipendentemente dal tipo di messaggio
        if True:
            # Commento il comportamento originale ma restituisco False per tutti i messaggi
            # ai fini dell'autorizzazione. Questo fa sì che il sistema tratti tutti
            # i messaggi in modo identico per quanto riguarda i permessi.
            
            # Continuiamo a loggare per debug, ma restituiremo sempre False
            
            # Verifica se il mittente è estratto da postacert.eml (tipico delle ricevute)
            if messaggio.mittente_postacert:
                logger.info(f"Messaggio {messaggio.id} ha mittente_postacert: {messaggio.mittente_postacert}")
                # Identificato come ricevuta ma restituiamo False per i permessi
            
            # Pattern comuni negli oggetti delle ricevute
            if re.search(r'ACCETTAZIONE|POSTA CERTIFICATA: accettazione', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come ricevuta di ACCETTAZIONE - ma trattato come normale per permessi")
            elif re.search(r'CONSEGNA|POSTA CERTIFICATA: avvenuta consegna', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come ricevuta di CONSEGNA - ma trattato come normale per permessi")
            elif re.search(r'MANCATA CONSEGNA|POSTA CERTIFICATA: mancata consegna', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come ricevuta di MANCATA CONSEGNA - ma trattato come normale per permessi")
            elif re.search(r'PRESA IN CARICO', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come ricevuta di PRESA IN CARICO - ma trattato come normale per permessi")
            elif re.search(r'ANOMALIA MESSAGGIO|ANOMALIA', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come ANOMALIA - ma trattato come normale per permessi")
            elif re.search(r'ERRORE DI CONSEGNA|MAILER.?DAEMON|DELIVERY.{0,10}(STATUS|FAIL|ERROR)', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come ERRORE DI CONSEGNA - ma trattato come normale per permessi")
            
            # Se non abbiamo trovato un tipo di ricevuta nell'oggetto, verifichiamo anche il contenuto
            elif messaggio.contenuto:
                if re.search(r'ricevuta di accettazione', messaggio.contenuto, re.IGNORECASE):
                    logger.info(f"Messaggio {messaggio.id} identificato come ricevuta di accettazione dal contenuto - ma trattato come normale per permessi")
                elif re.search(r'ricevuta di avvenuta consegna', messaggio.contenuto, re.IGNORECASE):
                    logger.info(f"Messaggio {messaggio.id} identificato come ricevuta di avvenuta consegna dal contenuto - ma trattato come normale per permessi")
                elif re.search(r'avviso di non accettazione', messaggio.contenuto, re.IGNORECASE):
                    logger.info(f"Messaggio {messaggio.id} identificato come avviso di non accettazione dal contenuto - ma trattato come normale per permessi")
                elif re.search(r'avviso di mancata consegna', messaggio.contenuto, re.IGNORECASE):
                    logger.info(f"Messaggio {messaggio.id} identificato come avviso di mancata consegna dal contenuto - ma trattato come normale per permessi")
            
            # Aggiungiamo un controllo più generico per i messaggi PEC
            elif re.search(r'POSTA CERTIFICATA|PEC', messaggio.oggetto, re.IGNORECASE):
                logger.info(f"Messaggio {messaggio.id} identificato come PEC generico dall'oggetto - ma trattato come normale per permessi")
            else:
                logger.info(f"Messaggio {messaggio.id} NON identificato come ricevuta")
            
            # Per risolvere il problema dei permessi, restituiamo sempre False
            # in modo che tutti i messaggi vengano trattati come normali messaggi PEC
            # per quanto riguarda le autorizzazioni
            return False

    @staticmethod
    def get_messaggio_originale_id(messaggio):
        """
        Cerca di ottenere l'ID del messaggio originale per una ricevuta PEC
        Restituisce None se non è possibile trovarlo o se il messaggio non è una ricevuta
        """
        import re
        import logging

        logger = logging.getLogger(__name__)

        # Verifica se è una ricevuta
        if not PermessiPEC.is_ricevuta(messaggio):
            return None

        # Cerca l'ID del messaggio originale nell'oggetto o nel contenuto
        # Questo è un approccio semplificato, potrebbe essere necessario migliorarlo
        if messaggio.contenuto:
            # Cerca pattern come "Identificativo messaggio: <id>"
            match = re.search(r'identificativo messaggio:?\s*([a-zA-Z0-9\-\.]+)', messaggio.contenuto, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    @staticmethod
    def utente_puo_vedere_account(utente, account_id):
        """
        Verifica se l'utente ha accesso all'account PEC specificato
        """
        if utente.is_superuser:
            return True

        # Verifica se l'utente è autorizzato a vedere l'account
        return utente.account_pec_autorizzati.filter(id=account_id).exists()

    @staticmethod
    def utente_puo_vedere_messaggio(utente, messaggio):
        """
        Verifica se l'utente ha accesso al messaggio PEC specificato
        """
        if utente.is_superuser:
            return True

        # Verifica se l'utente è autorizzato a vedere l'account del messaggio
        if utente.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists():
            return True

        # Verifica se l'utente è referente del cliente associato al messaggio
        if messaggio.cliente and messaggio.cliente.referente_set.filter(utente=utente).exists():
            return True

        return False

    @staticmethod
    def utente_puo_aggiornare_stato(utente, messaggio):
        """
        Verifica se l'utente può aggiornare lo stato del messaggio
        """
        import logging
        logger = logging.getLogger(__name__)

        # Log per debug
        logger.info(f"Verifica permesso aggiornamento stato per utente {utente.username} su messaggio {messaggio.id}")
        logger.info(f"Utente è superuser: {utente.is_superuser}")
        logger.info(f"Utente è manager: {utente.is_manager()}")
        logger.info(f"Utente è staff: {utente.is_staff_user()}")

        # Verifica se il messaggio è una ricevuta
        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)
        logger.info(f"Messaggio {messaggio.id} è una ricevuta: {is_ricevuta}")

        # Manager e superuser possono sempre aggiornare lo stato
        if utente.is_superuser or utente.is_manager():
            logger.info(f"Utente {utente.username} può aggiornare stato perché è superuser o manager")
            return True

        # Per gli utenti Staff, verifica il permesso specifico
        if utente.is_staff_user():
            # Prima verifichiamo se l'utente ha accesso all'account
            has_account_access = utente.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()
            logger.info(f"Utente {utente.username} ha accesso all'account: {has_account_access}")

            # Se l'utente ha accesso all'account, verifichiamo i permessi specifici
            if has_account_access:
                from apps.pec.models import UtenteAccountPEC
                try:
                    autorizzazione = UtenteAccountPEC.objects.get(
                        utente=utente,
                        account=messaggio.account_pec
                    )
                    logger.info(f"Autorizzazione trovata: puo_aggiornare_stato={autorizzazione.puo_aggiornare_stato}")

                    # Applica il permesso a tutti i messaggi dell'account, indipendentemente 
                    # dal fatto che siano messaggi PEC normali o ricevute
                    if autorizzazione.puo_aggiornare_stato:
                        logger.info(f"Utente {utente.username} può aggiornare stato perché ha il permesso specifico")
                        return True
                    else:
                        logger.info(f"Utente {utente.username} NON può aggiornare stato perché non ha il permesso specifico")
                except UtenteAccountPEC.DoesNotExist:
                    logger.info(f"Nessuna autorizzazione trovata per utente {utente.username} su account {messaggio.account_pec.id}")

        logger.info(f"Utente {utente.username} NON può aggiornare stato")
        return False

    @staticmethod
    def utente_puo_assegnare_cliente(utente, messaggio):
        """
        Verifica se l'utente può assegnare un cliente al messaggio
        """
        import logging
        logger = logging.getLogger(__name__)

        # Log per debug
        logger.info(f"Verifica permesso assegnazione cliente per utente {utente.username} su messaggio {messaggio.id}")
        logger.info(f"Utente è superuser: {utente.is_superuser}")
        logger.info(f"Utente è manager: {utente.is_manager()}")
        logger.info(f"Utente è staff: {utente.is_staff_user()}")

        # Verifica se il messaggio è una ricevuta
        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)
        logger.info(f"Messaggio {messaggio.id} è una ricevuta: {is_ricevuta}")

        # Manager e superuser possono sempre assegnare clienti
        if utente.is_superuser or utente.is_manager():
            logger.info(f"Utente {utente.username} può assegnare cliente perché è superuser o manager")
            return True

        # Per gli utenti Staff, verifica il permesso specifico
        if utente.is_staff_user():
            # Prima verifichiamo se l'utente ha accesso all'account
            has_account_access = utente.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()
            logger.info(f"Utente {utente.username} ha accesso all'account: {has_account_access}")

            # Se l'utente ha accesso all'account, verifichiamo i permessi specifici
            if has_account_access:
                from apps.pec.models import UtenteAccountPEC
                try:
                    autorizzazione = UtenteAccountPEC.objects.get(
                        utente=utente,
                        account=messaggio.account_pec
                    )
                    logger.info(f"Autorizzazione trovata: puo_assegnare_cliente={autorizzazione.puo_assegnare_cliente}")

                    # Applica il permesso a tutti i messaggi dell'account, indipendentemente 
                    # dal fatto che siano messaggi PEC normali o ricevute
                    if autorizzazione.puo_assegnare_cliente:
                        logger.info(f"Utente {utente.username} può assegnare cliente perché ha il permesso specifico")
                        return True
                    else:
                        logger.info(f"Utente {utente.username} NON può assegnare cliente perché non ha il permesso specifico")
                except UtenteAccountPEC.DoesNotExist:
                    logger.info(f"Nessuna autorizzazione trovata per utente {utente.username} su account {messaggio.account_pec.id}")

        logger.info(f"Utente {utente.username} NON può assegnare cliente")
        return False

    @staticmethod
    def utente_puo_assegnare_referente(utente, messaggio):
        """
        Verifica se l'utente può assegnare un referente al messaggio
        """
        import logging
        logger = logging.getLogger(__name__)

        # Log per debug
        logger.info(f"Verifica permesso assegnazione referente per utente {utente.username} su messaggio {messaggio.id}")
        logger.info(f"Utente è superuser: {utente.is_superuser}")
        logger.info(f"Utente è manager: {utente.is_manager()}")
        logger.info(f"Utente è staff: {utente.is_staff_user()}")

        # Verifica se il messaggio è una ricevuta
        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)
        logger.info(f"Messaggio {messaggio.id} è una ricevuta: {is_ricevuta}")

        # Manager e superuser possono sempre assegnare referenti
        if utente.is_superuser or utente.is_manager():
            logger.info(f"Utente {utente.username} può assegnare referente perché è superuser o manager")
            return True

        # Per gli utenti Staff, verifica il permesso specifico
        if utente.is_staff_user():
            # Prima verifichiamo se l'utente ha accesso all'account
            has_account_access = utente.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()
            logger.info(f"Utente {utente.username} ha accesso all'account: {has_account_access}")

            # Se l'utente ha accesso all'account, verifichiamo i permessi specifici
            if has_account_access:
                from apps.pec.models import UtenteAccountPEC
                try:
                    autorizzazione = UtenteAccountPEC.objects.get(
                        utente=utente,
                        account=messaggio.account_pec
                    )
                    logger.info(f"Autorizzazione trovata: puo_assegnare_referente={autorizzazione.puo_assegnare_referente}")

                    # Applica il permesso a tutti i messaggi dell'account, indipendentemente 
                    # dal fatto che siano messaggi PEC normali o ricevute
                    if autorizzazione.puo_assegnare_referente:
                        logger.info(f"Utente {utente.username} può assegnare referente perché ha il permesso specifico")
                        return True
                    else:
                        logger.info(f"Utente {utente.username} NON può assegnare referente perché non ha il permesso specifico")
                except UtenteAccountPEC.DoesNotExist:
                    logger.info(f"Nessuna autorizzazione trovata per utente {utente.username} su account {messaggio.account_pec.id}")

        logger.info(f"Utente {utente.username} NON può assegnare referente")
        return False

    @staticmethod
    def utente_puo_vedere_cliente(utente, cliente_id):
        """
        Verifica se l'utente ha accesso al cliente specificato
        """
        if utente.is_superuser:
            return True

        # Ottieni il cliente
        from apps.clienti.models import Cliente
        try:
            cliente = Cliente.objects.get(id=cliente_id)
        except Cliente.DoesNotExist:
            return False

        # Se l'utente è un Manager, può vedere tutti i clienti della sua organizzazione
        if utente.is_manager() and utente.organizzazione:
            return cliente.organizzazione == utente.organizzazione

        # Se l'utente è uno Staff, può vedere solo i clienti a cui è stato assegnato
        if utente.is_staff_user():
            return utente.clienti_assegnati.filter(id=cliente_id).exists()

        # Retrocompatibilità: verifica se l'utente è referente del cliente
        return utente.referente_set.filter(cliente_id=cliente_id).exists()


class IsAccountAuthorized(permissions.BasePermission):
    """
    Permission per verificare se l'utente è autorizzato a vedere un account PEC
    """

    def has_object_permission(self, request, view, obj):
        return PermessiPEC.utente_puo_vedere_account(request.user, obj.id)


class IsMessageAuthorized(permissions.BasePermission):
    """
    Permission per verificare se l'utente è autorizzato a vedere un messaggio PEC
    """

    def has_object_permission(self, request, view, obj):
        return PermessiPEC.utente_puo_vedere_messaggio(request.user, obj)


class IsClienteAuthorized(permissions.BasePermission):
    """
    Permission per verificare se l'utente è autorizzato a vedere un cliente
    """

    def has_object_permission(self, request, view, obj):
        return PermessiPEC.utente_puo_vedere_cliente(request.user, obj.id)


class CanUpdateMessageStatus(permissions.BasePermission):
    """
    Permission per verificare se l'utente può aggiornare lo stato di un messaggio
    """

    def has_object_permission(self, request, view, obj):
        return PermessiPEC.utente_puo_aggiornare_stato(request.user, obj)


class CanAssignClientToMessage(permissions.BasePermission):
    """
    Permission per verificare se l'utente può assegnare un cliente a un messaggio
    """

    def has_object_permission(self, request, view, obj):
        return PermessiPEC.utente_puo_assegnare_cliente(request.user, obj)


class CanAssignReferentToMessage(permissions.BasePermission):
    """
    Permission per verificare se l'utente può assegnare un referente a un messaggio
    """

    def has_object_permission(self, request, view, obj):
        return PermessiPEC.utente_puo_assegnare_referente(request.user, obj)