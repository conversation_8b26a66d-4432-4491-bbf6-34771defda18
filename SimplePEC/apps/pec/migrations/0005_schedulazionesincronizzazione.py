# Generated by Django 5.2 on 2025-05-09 19:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pec', '0004_utenteaccountpec'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SchedulazioneSincronizzazione',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attiva', models.BooleanField(default=True, help_text='Se la schedulazione è attiva')),
                ('frequenza', models.CharField(choices=[('OGNI_ORA', 'Ogni ora'), ('OGNI_3_ORE', 'Ogni 3 ore'), ('OGNI_6_ORE', 'Ogni 6 ore'), ('OGNI_12_ORE', 'Ogni 12 ore'), ('GIORNALIERA', 'Giornaliera'), ('SETTIMANALE', 'Settimanale')], default='GIORNALIERA', max_length=20)),
                ('tipo_sincronizzazione', models.CharField(choices=[('RECENTI', 'Solo messaggi recenti'), ('COMPLETA', 'Sincronizzazione completa')], default='RECENTI', max_length=20)),
                ('ora_inizio', models.TimeField(blank=True, help_text='Ora di inizio della sincronizzazione (per frequenze giornaliere o settimanali)', null=True)),
                ('giorno_settimana', models.IntegerField(blank=True, choices=[(0, 'Lunedì'), (1, 'Martedì'), (2, 'Mercoledì'), (3, 'Giovedì'), (4, 'Venerdì'), (5, 'Sabato'), (6, 'Domenica')], help_text='Giorno della settimana (solo per frequenza settimanale)', null=True)),
                ('data_creazione', models.DateTimeField(auto_now_add=True)),
                ('ultima_modifica', models.DateTimeField(auto_now=True)),
                ('task_id', models.CharField(blank=True, help_text='ID del task Celery associato', max_length=50, null=True)),
                ('account_pec', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedulazioni', to='pec.accountpec')),
                ('creata_da', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='schedulazioni_create', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Schedulazione Sincronizzazione',
                'verbose_name_plural': 'Schedulazioni Sincronizzazione',
                'ordering': ['account_pec__nome', 'frequenza'],
            },
        ),
    ]
