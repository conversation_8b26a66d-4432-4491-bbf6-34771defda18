# Generated by Django 5.2 on 2025-05-05 15:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('clienti', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountPEC',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=100)),
                ('indirizzo_email', models.EmailField(max_length=254, unique=True)),
                ('server_imap', models.CharField(max_length=100)),
                ('porta_imap', models.IntegerField()),
                ('server_smtp', models.CharField(max_length=100)),
                ('porta_smtp', models.IntegerField()),
                ('username', models.Char<PERSON><PERSON>(max_length=100)),
                ('password', models.Char<PERSON>ield(max_length=100)),
                ('attivo', models.BooleanField(default=True)),
                ('ultima_sincronizzazione', models.DateTimeField(blank=True, null=True)),
                ('utenti_autorizzati', models.ManyToManyField(blank=True, related_name='account_pec_autorizzati', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Account PEC',
                'verbose_name_plural': 'Account PEC',
            },
        ),
        migrations.CreateModel(
            name='MessaggioPEC',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('oggetto', models.CharField(max_length=500)),
                ('contenuto', models.TextField()),
                ('data_ricezione', models.DateTimeField()),
                ('messaggio_completo', models.BinaryField()),
                ('identificativo_messaggio', models.CharField(max_length=200, unique=True)),
                ('stato', models.CharField(choices=[('DA_LEGGERE', 'Da leggere'), ('DA_ASSEGNARE', 'Da assegnare'), ('ASSEGNATO', 'Assegnato'), ('LAVORATO', 'Lavorato')], default='DA_LEGGERE', max_length=20)),
                ('account_pec', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pec.accountpec')),
                ('cliente', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='clienti.cliente')),
                ('destinatari', models.ManyToManyField(related_name='messaggi_ricevuti', to='clienti.contatto')),
                ('mittente', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messaggi_inviati', to='clienti.contatto')),
                ('referente', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='clienti.referente')),
            ],
            options={
                'verbose_name': 'Messaggio PEC',
                'verbose_name_plural': 'Messaggi PEC',
            },
        ),
        migrations.CreateModel(
            name='LogAssociazione',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_associazione', models.DateTimeField(auto_now_add=True)),
                ('cliente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='clienti.cliente')),
                ('utente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('messaggio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pec.messaggiopec')),
            ],
            options={
                'verbose_name': 'Log Associazione',
                'verbose_name_plural': 'Log Associazioni',
            },
        ),
        migrations.CreateModel(
            name='LogAccesso',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_accesso', models.DateTimeField(auto_now_add=True)),
                ('utente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('messaggio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pec.messaggiopec')),
            ],
            options={
                'verbose_name': 'Log Accesso',
                'verbose_name_plural': 'Log Accessi',
            },
        ),
        migrations.CreateModel(
            name='Allegato',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome_file', models.CharField(max_length=255)),
                ('tipo_file', models.CharField(max_length=100)),
                ('dimensione', models.IntegerField()),
                ('contenuto', models.BinaryField()),
                ('messaggio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allegati', to='pec.messaggiopec')),
            ],
            options={
                'verbose_name': 'Allegato',
                'verbose_name_plural': 'Allegati',
            },
        ),
    ]
