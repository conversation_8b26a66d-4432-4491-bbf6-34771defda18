# Generated by Django 5.2 on 2025-05-08 21:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pec', '0003_accountpec_organizzazione'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UtenteAccountPEC',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('puo_aggiornare_stato', models.BooleanField(default=True, help_text="L'utente può aggiornare lo stato dei messaggi")),
                ('puo_assegnare_cliente', models.BooleanField(default=True, help_text="L'utente può assegnare clienti ai messaggi")),
                ('puo_assegnare_referente', models.BooleanField(default=True, help_text="L'utente può assegnare referenti ai messaggi")),
                ('data_assegnazione', models.DateTimeField(auto_now_add=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='autorizzazioni_utente', to='pec.accountpec')),
                ('utente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='autorizzazioni_account', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Autorizzazione Account PEC',
                'verbose_name_plural': 'Autorizzazioni Account PEC',
                'unique_together': {('utente', 'account')},
            },
        ),
    ]
