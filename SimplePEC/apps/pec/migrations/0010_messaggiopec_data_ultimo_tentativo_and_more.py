# Generated by Django 4.2.21 on 2025-05-29 17:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pec', '0009_messaggiopec_inoltrato_a'),
    ]

    operations = [
        migrations.AddField(
            model_name='messaggiopec',
            name='data_ultimo_tentativo',
            field=models.DateTimeField(blank=True, help_text="Data e ora dell'ultimo tentativo di invio", null=True),
        ),
        migrations.AddField(
            model_name='messaggiopec',
            name='errore_invio',
            field=models.TextField(blank=True, help_text="Dettagli dell'errore durante l'invio", null=True),
        ),
        migrations.AddField(
            model_name='messaggiopec',
            name='numero_tentativi',
            field=models.PositiveIntegerField(default=0, help_text='Numero di tentativi di invio effettuati'),
        ),
        migrations.AlterField(
            model_name='messaggiopec',
            name='stato',
            field=models.Char<PERSON>ield(choices=[('DA_LEGGERE', 'Da leggere'), ('DA_ASSEGNARE', 'Da assegnare'), ('ASSEGNATO', 'Assegnato'), ('LAVORATO', 'Lavorato'), ('IN_INVIO', 'In invio'), ('INVIATO', 'Inviato'), ('ERRORE_INVIO', 'Errore invio')], default='DA_LEGGERE', max_length=20),
        ),
    ]
