from rest_framework import serializers
from .models import AccountPEC, MessaggioPEC, Allegato, LogAccesso, LogAssociazione, SchedulazioneSincronizzazione
from apps.clienti.serializers import ClienteSerializer, ReferenteSerializer, ContattoSerializer

class AccountPECSerializer(serializers.ModelSerializer):
    """Serializer per il modello AccountPEC"""

    class Meta:
        model = AccountPEC
        fields = ['id', 'nome', 'indirizzo_email', 'server_imap', 'porta_imap',
                 'server_smtp', 'porta_smtp', 'username', 'password', 'attivo',
                 'ultima_sincronizzazione']
        extra_kwargs = {
            'password': {'write_only': True}
        }


class AllegatoSerializer(serializers.ModelSerializer):
    """Serializer per il modello Allegato"""

    class Meta:
        model = Allegato
        fields = ['id', 'messaggio', 'nome_file', 'tipo_file', 'dimensione']
        # Escludiamo contenuto per non trasferire dati binari


class MessaggioPECSerializer(serializers.ModelSerializer):
    """Serializer per il modello MessaggioPEC"""

    account_pec_info = serializers.StringRelatedField(source='account_pec', read_only=True)
    mittente_info = ContattoSerializer(source='mittente', read_only=True)
    destinatari_info = ContattoSerializer(source='destinatari', many=True, read_only=True)
    cliente_info = ClienteSerializer(source='cliente', read_only=True)
    referente_info = ReferenteSerializer(source='referente', read_only=True)
    allegati_count = serializers.SerializerMethodField()

    class Meta:
        model = MessaggioPEC
        fields = ['id', 'account_pec', 'account_pec_info', 'mittente', 'mittente_info',
                 'destinatari', 'destinatari_info', 'oggetto', 'contenuto', 'data_ricezione',
                 'identificativo_messaggio', 'stato', 'cliente', 'cliente_info',
                 'referente', 'referente_info', 'allegati_count']
        # Escludiamo messaggio_completo per non trasferire dati binari

    def get_allegati_count(self, obj):
        """Restituisce il numero di allegati del messaggio"""
        return obj.allegati.count()


class LogAccessoSerializer(serializers.ModelSerializer):
    """Serializer per il modello LogAccesso"""

    utente_info = serializers.StringRelatedField(source='utente', read_only=True)
    messaggio_info = serializers.StringRelatedField(source='messaggio', read_only=True)

    class Meta:
        model = LogAccesso
        fields = ['id', 'utente', 'utente_info', 'messaggio', 'messaggio_info', 'data_accesso']


class LogAssociazioneSerializer(serializers.ModelSerializer):
    """Serializer per il modello LogAssociazione"""

    utente_info = serializers.StringRelatedField(source='utente', read_only=True)
    messaggio_info = serializers.StringRelatedField(source='messaggio', read_only=True)
    cliente_info = serializers.StringRelatedField(source='cliente', read_only=True)

    class Meta:
        model = LogAssociazione
        fields = ['id', 'utente', 'utente_info', 'messaggio', 'messaggio_info',
                 'cliente', 'cliente_info', 'data_associazione']


class AssegnaClienteSerializer(serializers.Serializer):
    """Serializer per l'assegnazione di un cliente a un messaggio"""

    cliente_id = serializers.IntegerField(required=True)


class AssegnaReferenteSerializer(serializers.Serializer):
    """Serializer per l'assegnazione di un referente a un messaggio"""

    referente_id = serializers.IntegerField(required=True)


class CambiaStatoSerializer(serializers.Serializer):
    """Serializer per il cambio di stato di un messaggio"""

    stato = serializers.ChoiceField(choices=MessaggioPEC.STATO_CHOICES, required=True)


class SchedulazioneSincronizzazioneSerializer(serializers.ModelSerializer):
    """Serializer per il modello SchedulazioneSincronizzazione"""

    account_pec_nome = serializers.StringRelatedField(source='account_pec', read_only=True)
    creata_da_nome = serializers.StringRelatedField(source='creata_da', read_only=True)
    frequenza_display = serializers.CharField(source='get_frequenza_display', read_only=True)
    tipo_sincronizzazione_display = serializers.CharField(source='get_tipo_sincronizzazione_display', read_only=True)
    giorno_settimana_display = serializers.SerializerMethodField()

    class Meta:
        model = SchedulazioneSincronizzazione
        fields = ['id', 'account_pec', 'account_pec_nome', 'attiva', 'frequenza', 'frequenza_display',
                 'tipo_sincronizzazione', 'tipo_sincronizzazione_display', 'ora_inizio', 'giorno_settimana',
                 'giorno_settimana_display', 'creata_da', 'creata_da_nome', 'data_creazione', 'ultima_modifica', 'task_id']

    def get_giorno_settimana_display(self, obj):
        """Restituisce il nome del giorno della settimana"""
        if obj.giorno_settimana is not None:
            giorni = ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica']
            return giorni[obj.giorno_settimana]
        return None