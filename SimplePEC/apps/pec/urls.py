from django.urls import path
from .views import (
    MessageListView, MessageDetailView, AccountListView, AccountDetailView,
    change_message_status, assign_client_to_message, assign_referent_to_message,
    download_attachment, download_message, sync_account, create_account,
    test_account_connection, update_account, toggle_account_status, view_postacert_for_message,
    SchedulazioneListView, SchedulazioneCreateView, SchedulazioneUpdateView, SchedulazioneDeleteView,
    toggle_schedulazione, OutboxListView, send_message, forward_message, retry_send_message
)

urlpatterns = [
    # Messaggi in entrata
    path('messages/', MessageListView.as_view(), name='message_list'),
    path('messages/<int:pk>/', MessageDetailView.as_view(), name='message_detail'),
    path('messages/<int:pk>/change-status/', change_message_status, name='message_change_status'),
    path('messages/<int:pk>/assign-client/', assign_client_to_message, name='message_assign_client'),
    path('messages/<int:pk>/assign-referent/', assign_referent_to_message, name='message_assign_referent'),
    path('messages/<int:pk>/download/', download_message, name='message_download'),
    path('messages/<int:pk>/view-postacert/', view_postacert_for_message, name='message_view_postacert'),
    path('messages/<int:pk>/forward/', forward_message, name='message_forward'),

    # Messaggi in uscita
    path('outbox/', OutboxListView.as_view(), name='outbox_list'),
    path('outbox/send/', send_message, name='send_message'),
    path('messages/<int:pk>/retry-send/', retry_send_message, name='retry_send_message'),

    # Account
    path('accounts/', AccountListView.as_view(), name='account_list'),
    path('accounts/create/', create_account, name='account_create'),
    path('accounts/<int:pk>/', AccountDetailView.as_view(), name='account_detail'),
    path('accounts/<int:pk>/sync/', sync_account, name='account_sync'),
    path('accounts/<int:pk>/test-connection/', test_account_connection, name='account_test_connection'),
    path('accounts/<int:pk>/update/', update_account, name='account_update'),
    path('accounts/<int:pk>/toggle-status/', toggle_account_status, name='account_toggle_status'),

    # Allegati
    path('attachments/<int:pk>/download/', download_attachment, name='attachment_download'),

    # Schedulazioni
    path('schedulazioni/', SchedulazioneListView.as_view(), name='schedulazione_list'),
    path('schedulazioni/create/', SchedulazioneCreateView.as_view(), name='schedulazione_create'),
    path('schedulazioni/<int:pk>/update/', SchedulazioneUpdateView.as_view(), name='schedulazione_update'),
    path('schedulazioni/<int:pk>/delete/', SchedulazioneDeleteView.as_view(), name='schedulazione_delete'),
    path('schedulazioni/<int:pk>/toggle/', toggle_schedulazione, name='schedulazione_toggle'),
]