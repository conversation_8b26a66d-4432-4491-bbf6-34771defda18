from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.http import HttpResponse, FileResponse
from django.utils import timezone
from django.shortcuts import get_object_or_404, render, redirect
import base64
import io
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.core.paginator import Paginator
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.forms import ModelForm, TimeInput, Select, HiddenInput
from django.db import transaction
from django.db.models import Q

from .models import AccountPEC, MessaggioPEC, Allegato, LogAccesso, LogAssociazione, SchedulazioneSincronizzazione
from .serializers import (
    AccountPECSerializer, MessaggioPECSerializer, AllegatoSerializer,
    LogAccessoSerializer, LogAssociazioneSerializer,
    AssegnaClienteSerializer, AssegnaReferenteSerializer, CambiaStatoSerializer,
    SchedulazioneSincronizzazioneSerializer
)
from .permissions import (
    IsAccountAuthorized, IsMessageAuthorized,
    CanUpdateMessageStatus, CanAssignClientToMessage, CanAssignReferentToMessage,
    PermessiPEC
)
from .tasks import sincronizza_account_pec, crea_schedulazione_celery, elimina_schedulazione_celery
from .services import InvioPEC, EmailService
from apps.clienti.models import Cliente, Referente, Contatto
from apps.accounts.models import UtentePersonalizzato
from core.permissions import is_manager


class AccountPECViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sugli account PEC
    """
    queryset = AccountPEC.objects.all()
    serializer_class = AccountPECSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['attivo']
    search_fields = ['nome', 'indirizzo_email']
    ordering_fields = ['nome', 'indirizzo_email', 'ultima_sincronizzazione']

    def get_permissions(self):
        """
        Solo i Manager possono creare/modificare/eliminare account
        """
        from core.permissions import is_manager

        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            # Verifica se l'utente è un Manager
            if self.request.user.is_authenticated and is_manager(self.request.user):
                permission_classes = [permissions.IsAuthenticated]
            else:
                permission_classes = [permissions.IsAdminUser]  # Fallback per admin
        else:
            permission_classes = [permissions.IsAuthenticated, IsAccountAuthorized]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filtra gli account in base ai permessi dell'utente
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutti gli account
        if user.is_superuser:
            return AccountPEC.objects.all()

        # Se l'utente è un Manager, vede tutti gli account della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return AccountPEC.objects.filter(organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo gli account a cui è stato assegnato
        return user.account_pec_autorizzati.all()

    def perform_create(self, serializer):
        """
        Quando un Manager crea un account, lo associa automaticamente alla sua organizzazione
        """
        instance = serializer.save()

        # Se l'utente è un Manager, associa l'account alla sua organizzazione
        if self.request.user.is_manager() and self.request.user.organizzazione:
            instance.organizzazione = self.request.user.organizzazione
            instance.save()

    @action(detail=True, methods=['post'], url_path='test-connessione')
    def test_connessione(self, request, pk=None):
        """
        Testa la connessione all'account PEC
        """
        from .services import SincronizzazionePEC

        account = self.get_object()
        sync = SincronizzazionePEC(account.id)
        result = sync.connetti()

        if result:
            return Response({"status": "success", "message": "Connessione riuscita"})
        return Response(
            {"status": "error", "message": "Errore di connessione"},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'], url_path='sincronizza')
    def sincronizza(self, request, pk=None):
        """
        Avvia la sincronizzazione manuale dell'account PEC
        """
        account = self.get_object()
        task = sincronizza_account_pec.delay(account.id)

        return Response({
            "status": "success",
            "message": "Sincronizzazione avviata",
            "task_id": task.id
        })


class MessaggioPECViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sui messaggi PEC
    """
    queryset = MessaggioPEC.objects.all()
    serializer_class = MessaggioPECSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['account_pec', 'stato', 'cliente', 'referente']
    search_fields = ['oggetto', 'mittente__nome', 'mittente__email']
    ordering_fields = ['data_ricezione', 'oggetto', 'stato']

    def get_permissions(self):
        """
        Nessuno può creare/eliminare messaggi direttamente (lo fa il sistema)
        Solo gli utenti autorizzati possono vedere/modificare i messaggi
        Per le azioni specifiche, verifica i permessi specifici
        """
        if self.action in ['create', 'destroy']:
            permission_classes = [permissions.IsAdminUser]
        elif self.action == 'cambia_stato':
            permission_classes = [permissions.IsAuthenticated, IsMessageAuthorized, CanUpdateMessageStatus]
        elif self.action == 'assegna_cliente':
            permission_classes = [permissions.IsAuthenticated, IsMessageAuthorized, CanAssignClientToMessage]
        elif self.action == 'assegna_referente':
            permission_classes = [permissions.IsAuthenticated, IsMessageAuthorized, CanAssignReferentToMessage]
        else:
            permission_classes = [permissions.IsAuthenticated, IsMessageAuthorized]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filtra i messaggi in base ai permessi dell'utente
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutti i messaggi
        if user.is_superuser:
            return MessaggioPEC.objects.all()

        # Se l'utente è un Manager, vede tutti i messaggi della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return MessaggioPEC.objects.filter(account_pec__organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo i messaggi degli account a cui è stato assegnato
        # o dei clienti a cui è stato assegnato
        if user.is_staff_user():
            account_messages = MessaggioPEC.objects.filter(account_pec__in=user.account_pec_autorizzati.all())
            client_messages = MessaggioPEC.objects.filter(cliente__in=user.clienti_assegnati.all())
            return (account_messages | client_messages).distinct()

        # Retrocompatibilità per altri utenti
        account_messages = MessaggioPEC.objects.filter(account_pec__utenti_autorizzati=user)
        client_messages = MessaggioPEC.objects.filter(cliente__referente__utente=user)
        return (account_messages | client_messages).distinct()

    def retrieve(self, request, *args, **kwargs):
        """
        Override per registrare l'accesso al messaggio
        """
        instance = self.get_object()

        # Registra l'accesso
        LogAccesso.objects.create(
            utente=request.user,
            messaggio=instance
        )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='allegati')
    def allegati(self, request, pk=None):
        """
        Restituisce gli allegati di un messaggio
        """
        messaggio = self.get_object()
        allegati = Allegato.objects.filter(messaggio=messaggio)
        serializer = AllegatoSerializer(allegati, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='download-eml')
    def download_eml(self, request, pk=None):
        """
        Permette il download del messaggio originale in formato EML
        """
        messaggio = self.get_object()
        response = HttpResponse(messaggio.messaggio_completo, content_type='message/rfc822')
        response['Content-Disposition'] = f'attachment; filename="messaggio_{messaggio.id}.eml"'
        return response

    @action(detail=True, methods=['post'], url_path='assegna-cliente')
    def assegna_cliente(self, request, pk=None):
        """
        Assegna un cliente al messaggio
        """
        messaggio = self.get_object()
        serializer = AssegnaClienteSerializer(data=request.data)

        if serializer.is_valid():
            cliente_id = serializer.validated_data['cliente_id']
            cliente = get_object_or_404(Cliente, id=cliente_id)

            # Assegna il cliente
            messaggio.cliente = cliente

            # Se lo stato è "DA_LEGGERE", lo cambia in "DA_ASSEGNARE"
            if messaggio.stato == 'DA_LEGGERE':
                messaggio.stato = 'DA_ASSEGNARE'

            messaggio.save()

            # Registra l'associazione
            LogAssociazione.objects.create(
                utente=request.user,
                messaggio=messaggio,
                cliente=cliente
            )

            return Response(MessaggioPECSerializer(messaggio).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], url_path='assegna-referente')
    def assegna_referente(self, request, pk=None):
        """
        Assegna un referente al messaggio
        """
        messaggio = self.get_object()
        serializer = AssegnaReferenteSerializer(data=request.data)

        if serializer.is_valid():
            referente_id = serializer.validated_data['referente_id']
            referente = get_object_or_404(Referente, id=referente_id)

            # Verifica che il cliente del referente sia lo stesso del messaggio
            if messaggio.cliente and messaggio.cliente != referente.cliente:
                return Response(
                    {"error": "Il referente deve appartenere allo stesso cliente del messaggio"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Assegna il referente e il cliente associato
            messaggio.referente = referente
            messaggio.cliente = referente.cliente

            # Se lo stato è "DA_ASSEGNARE", lo cambia in "ASSEGNATO"
            if messaggio.stato in ['DA_LEGGERE', 'DA_ASSEGNARE']:
                messaggio.stato = 'ASSEGNATO'

            messaggio.save()

            # Registra l'associazione al cliente se non esistente
            if not LogAssociazione.objects.filter(
                messaggio=messaggio, cliente=referente.cliente
            ).exists():
                LogAssociazione.objects.create(
                    utente=request.user,
                    messaggio=messaggio,
                    cliente=referente.cliente
                )

            return Response(MessaggioPECSerializer(messaggio).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], url_path='cambia-stato')
    def cambia_stato(self, request, pk=None):
        """
        Cambia lo stato di un messaggio
        """
        messaggio = self.get_object()
        serializer = CambiaStatoSerializer(data=request.data)

        if serializer.is_valid():
            nuovo_stato = serializer.validated_data['stato']
            messaggio.stato = nuovo_stato
            messaggio.save()

            return Response(MessaggioPECSerializer(messaggio).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AllegatoViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet di sola lettura per gli allegati
    """
    queryset = Allegato.objects.all()
    serializer_class = AllegatoSerializer

    def get_permissions(self):
        permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filtra gli allegati in base ai permessi dell'utente sui messaggi
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutti gli allegati
        if user.is_superuser:
            return Allegato.objects.all()

        # Se l'utente è un Manager, vede tutti gli allegati della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return Allegato.objects.filter(messaggio__account_pec__organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo gli allegati degli account a cui è stato assegnato
        # o dei clienti a cui è stato assegnato
        if user.is_staff_user():
            account_allegati = Allegato.objects.filter(messaggio__account_pec__in=user.account_pec_autorizzati.all())
            client_allegati = Allegato.objects.filter(messaggio__cliente__in=user.clienti_assegnati.all())
            return (account_allegati | client_allegati).distinct()

        # Retrocompatibilità per altri utenti
        account_allegati = Allegato.objects.filter(messaggio__account_pec__utenti_autorizzati=user)
        client_allegati = Allegato.objects.filter(messaggio__cliente__referente__utente=user)
        return (account_allegati | client_allegati).distinct()

    @action(detail=True, methods=['get'], url_path='download')
    def download(self, request, pk=None):
        """
        Permette il download di un allegato
        """
        allegato = self.get_object()
        response = HttpResponse(allegato.contenuto, content_type=allegato.tipo_file)
        response['Content-Disposition'] = f'attachment; filename="{allegato.nome_file}"'
        return response


# ViewSet per le schedulazioni
class SchedulazioneSincronizzazioneViewSet(viewsets.ModelViewSet):
    """
    ViewSet per le operazioni CRUD sulle schedulazioni di sincronizzazione
    """
    queryset = SchedulazioneSincronizzazione.objects.all()
    serializer_class = SchedulazioneSincronizzazioneSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['account_pec', 'attiva', 'frequenza', 'tipo_sincronizzazione']
    search_fields = ['account_pec__nome', 'account_pec__indirizzo_email']
    ordering_fields = ['account_pec__nome', 'frequenza', 'data_creazione']

    def get_permissions(self):
        """
        Solo i superuser e i Manager possono gestire le schedulazioni
        """
        if self.request.user.is_superuser or (self.request.user.is_authenticated and is_manager(self.request.user)):
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]  # Fallback per admin
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filtra le schedulazioni in base ai permessi dell'utente
        """
        user = self.request.user

        # Se l'utente è un superuser, vede tutte le schedulazioni
        if user.is_superuser:
            return SchedulazioneSincronizzazione.objects.all()

        # Se l'utente è un Manager, vede solo le schedulazioni della sua organizzazione
        if is_manager(user) and user.organizzazione:
            return SchedulazioneSincronizzazione.objects.filter(account_pec__organizzazione=user.organizzazione)

        # Altri utenti non vedono nulla
        return SchedulazioneSincronizzazione.objects.none()

    def perform_create(self, serializer):
        """
        Imposta l'utente corrente come creatore della schedulazione
        """
        serializer.save(creata_da=self.request.user)

    @action(detail=True, methods=['post'], url_path='toggle-attiva')
    def toggle_attiva(self, request, pk=None):
        """
        Attiva/disattiva una schedulazione
        """
        schedulazione = self.get_object()
        schedulazione.attiva = not schedulazione.attiva
        schedulazione.save()

        return Response({
            "status": "success",
            "message": f"Schedulazione {'attivata' if schedulazione.attiva else 'disattivata'}",
            "attiva": schedulazione.attiva
        })


# Form per la schedulazione
class SchedulazioneSincronizzazioneForm(ModelForm):
    """Form per la creazione/modifica di una schedulazione"""

    class Meta:
        model = SchedulazioneSincronizzazione
        fields = ['account_pec', 'attiva', 'frequenza', 'tipo_sincronizzazione', 'ora_inizio', 'giorno_settimana']
        widgets = {
            'ora_inizio': TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'frequenza': Select(attrs={'class': 'form-select'}),
            'tipo_sincronizzazione': Select(attrs={'class': 'form-select'}),
            'giorno_settimana': Select(attrs={'class': 'form-select'}),
        }


# Viste per i messaggi in uscita
class OutboxListView(LoginRequiredMixin, ListView):
    """Vista per la lista dei messaggi in uscita"""
    model = MessaggioPEC
    template_name = 'pec/outbox_list.html'
    context_object_name = 'messaggi'
    paginate_by = 20

    def get_queryset(self):
        """Filtra i messaggi in uscita in base ai permessi dell'utente"""
        user = self.request.user
        queryset = MessaggioPEC.objects.filter(direzione='USCITA')

        # Filtri
        account = self.request.GET.get('account')
        data_da = self.request.GET.get('data_da')
        data_a = self.request.GET.get('data_a')
        search = self.request.GET.get('search')

        # Se l'utente è un superuser, vede tutti i messaggi in uscita
        if user.is_superuser:
            pass  # Nessun filtro aggiuntivo
        # Se l'utente è un Manager, vede solo i messaggi della sua organizzazione
        elif user.is_manager() and user.organizzazione:
            queryset = queryset.filter(account_pec__organizzazione=user.organizzazione)
        # Se l'utente è uno Staff, vede solo i messaggi degli account a cui è autorizzato
        else:
            queryset = queryset.filter(account_pec__in=user.account_pec_autorizzati.all())

        # Applica i filtri dalla query string
        if account:
            queryset = queryset.filter(account_pec_id=account)

        if data_da:
            queryset = queryset.filter(data_ricezione__gte=data_da)

        if data_a:
            queryset = queryset.filter(data_ricezione__lte=data_a)

        if search:
            queryset = queryset.filter(
                Q(oggetto__icontains=search) |
                Q(destinatari__nome__icontains=search) |
                Q(destinatari__email__icontains=search)
            )

        # Ordina per data di ricezione (più recenti prima)
        return queryset.order_by('-data_ricezione')

    def get_context_data(self, **kwargs):
        """Aggiunge dati al contesto"""
        context = super().get_context_data(**kwargs)
        user = self.request.user

        # Arricchisce i messaggi con informazioni aggiuntive
        messaggi_arricchiti = []
        for messaggio in context['messaggi']:
            # Prepara le info di base del messaggio
            messaggio_dict = {
                'id': messaggio.id,
                'oggetto': messaggio.oggetto,
                'data_ricezione': messaggio.data_ricezione,
                'stato': 'INVIATO',  # Forza lo stato a INVIATO per i messaggi in uscita
                'get_stato_display': 'Inviato',
            }

            # Account PEC info
            if messaggio.account_pec:
                messaggio_dict['account_pec_info'] = messaggio.account_pec.nome
            else:
                messaggio_dict['account_pec_info'] = "N/D"

            # Destinatari info
            destinatari_info = []
            for destinatario in messaggio.destinatari.all():
                destinatari_info.append({
                    'id': destinatario.id,
                    'nome': destinatario.nome if destinatario.nome else destinatario.email,
                    'email': destinatario.email
                })
            messaggio_dict['destinatari_info'] = destinatari_info

            messaggi_arricchiti.append(messaggio_dict)

        # Sostituisce i messaggi nel contesto
        context['messaggi'] = messaggi_arricchiti

        # Account disponibili per il filtro
        if user.is_superuser:
            accounts = AccountPEC.objects.all()
        elif user.is_manager() and user.organizzazione:
            accounts = AccountPEC.objects.filter(organizzazione=user.organizzazione)
        else:
            accounts = user.account_pec_autorizzati.all()

        # Account disponibili per l'invio
        if user.is_superuser:
            available_accounts = AccountPEC.objects.filter(attivo=True)
        elif user.is_manager() and user.organizzazione:
            available_accounts = AccountPEC.objects.filter(
                organizzazione=user.organizzazione,
                attivo=True
            )
        else:
            available_accounts = user.account_pec_autorizzati.filter(attivo=True)

        context['accounts'] = accounts
        context['available_accounts'] = available_accounts

        return context


@login_required
def send_message(request):
    """Vista per l'invio di un nuovo messaggio PEC"""
    if request.method != 'POST':
        messages.error(request, "Metodo non consentito")
        return redirect('outbox_list')

    # Ottieni i dati dal form
    account_id = request.POST.get('account_pec')
    destinatari = request.POST.get('destinatari', '').strip()
    oggetto = request.POST.get('oggetto', '').strip()
    contenuto = request.POST.get('contenuto', '').strip()
    allegati = request.FILES.getlist('allegati')

    # Validazione
    if not account_id or not destinatari or not oggetto or not contenuto:
        messages.error(request, "Tutti i campi sono obbligatori")
        return redirect('outbox_list')

    # Verifica che l'account esista e che l'utente sia autorizzato
    try:
        account = AccountPEC.objects.get(id=account_id, attivo=True)
    except AccountPEC.DoesNotExist:
        messages.error(request, "Account PEC non trovato o non attivo")
        return redirect('outbox_list')

    # Verifica autorizzazioni
    user = request.user
    if not user.is_superuser:
        if user.is_manager() and user.organizzazione:
            if account.organizzazione != user.organizzazione:
                messages.error(request, "Non sei autorizzato a utilizzare questo account PEC")
                return redirect('outbox_list')
        elif account not in user.account_pec_autorizzati.all():
            messages.error(request, "Non sei autorizzato a utilizzare questo account PEC")
            return redirect('outbox_list')

    # Prepara la lista dei destinatari
    destinatari_list = [d.strip() for d in destinatari.split(',') if d.strip()]

    # Validazione email destinatari
    import re
    email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    for dest in destinatari_list:
        if not email_pattern.match(dest):
            messages.error(request, f"Indirizzo email non valido: {dest}")
            return redirect('outbox_list')

    # Prepara il messaggio
    try:
        # Genera un identificativo univoco per il messaggio
        import uuid
        import email.utils

        # Crea un Message-ID univoco basato su UUID e dominio dell'account
        domain = account.indirizzo_email.split('@')[1]
        identificativo_messaggio = email.utils.make_msgid(idstring=str(uuid.uuid4()), domain=domain)

        # Crea un messaggio PEC in stato "IN_INVIO"
        messaggio = MessaggioPEC.objects.create(
            account_pec=account,
            oggetto=oggetto,
            direzione='USCITA',
            stato='IN_INVIO',
            data_ricezione=timezone.now(),
            messaggio_completo=b'',  # Sarà aggiornato dopo l'invio
            identificativo_messaggio=identificativo_messaggio,
            contenuto=contenuto  # Salva il contenuto del messaggio
        )

        # Salva gli allegati
        allegati_files = []
        for allegato_file in allegati:
            allegato_content = allegato_file.read()
            allegato = Allegato.objects.create(
                messaggio=messaggio,
                nome_file=allegato_file.name,
                tipo_file=allegato_file.content_type,
                dimensione=allegato_file.size,
                contenuto=allegato_content
            )
            allegati_files.append({
                'nome': allegato.nome_file,
                'contenuto': allegato_content,
                'tipo': allegato.tipo_file
            })

        # Invia il messaggio utilizzando la classe InvioPEC
        servizio_invio = InvioPEC(account.id)
        invio_riuscito, messaggio_completo = servizio_invio.invia_messaggio(
            destinatari=destinatari_list,
            oggetto=oggetto,
            contenuto=contenuto,
            allegati=allegati_files if allegati_files else None,
            identificativo_messaggio=identificativo_messaggio
        )

        if invio_riuscito and messaggio_completo:
            # Aggiorna il messaggio con il contenuto completo e lo stato
            messaggio.messaggio_completo = messaggio_completo
            messaggio.stato = 'INVIATO'
            messaggio.save()

            # Crea i contatti per i destinatari se non esistono
            for dest_email in destinatari_list:
                destinatario, _ = Contatto.objects.get_or_create(
                    email=dest_email,
                    defaults={"nome": dest_email}
                )
                messaggio.destinatari.add(destinatario)

            messages.success(request, "Messaggio inviato con successo")
        else:
            # Se l'invio è fallito, aggiorna lo stato
            messaggio.stato = 'IN_INVIO'  # Manteniamo lo stato "in invio" per permettere tentativi futuri
            messaggio.save()
            messages.error(request, "Errore nell'invio del messaggio. Controlla le impostazioni del server SMTP.")

        return redirect('outbox_list')

    except Exception as e:
        messages.error(request, f"Errore durante l'invio del messaggio: {str(e)}")
        return redirect('outbox_list')


# Mixin per verificare se l'utente è un superuser o un Manager
class SuperuserOrManagerRequiredMixin(UserPassesTestMixin):
    """Mixin che verifica se l'utente è un superuser o un Manager"""

    def test_func(self):
        return self.request.user.is_superuser or is_manager(self.request.user)


# Template views per le schedulazioni
class SchedulazioneListView(LoginRequiredMixin, SuperuserOrManagerRequiredMixin, ListView):
    """Lista delle schedulazioni di sincronizzazione"""
    model = SchedulazioneSincronizzazione
    template_name = 'pec/schedulazione_list.html'
    context_object_name = 'schedulazioni'

    def get_queryset(self):
        """Filtra le schedulazioni in base ai permessi dell'utente"""
        user = self.request.user

        # Se l'utente è un superuser, vede tutte le schedulazioni
        if user.is_superuser:
            return SchedulazioneSincronizzazione.objects.all()

        # Se l'utente è un Manager, vede solo le schedulazioni della sua organizzazione
        if is_manager(user) and user.organizzazione:
            return SchedulazioneSincronizzazione.objects.filter(account_pec__organizzazione=user.organizzazione)

        # Altri utenti non vedono nulla
        return SchedulazioneSincronizzazione.objects.none()

    def get_context_data(self, **kwargs):
        """Aggiunge dati al contesto"""
        context = super().get_context_data(**kwargs)
        context['can_add'] = True  # Tutti gli utenti che possono vedere la lista possono anche aggiungere
        return context


class SchedulazioneCreateView(LoginRequiredMixin, SuperuserOrManagerRequiredMixin, CreateView):
    """Creazione di una nuova schedulazione"""
    model = SchedulazioneSincronizzazione
    form_class = SchedulazioneSincronizzazioneForm
    template_name = 'pec/schedulazione_form.html'
    success_url = reverse_lazy('schedulazione_list')

    def get_form(self, form_class=None):
        """Personalizza il form in base all'utente"""
        form = super().get_form(form_class)
        user = self.request.user

        # Limita gli account PEC disponibili in base ai permessi dell'utente
        if not user.is_superuser and is_manager(user) and user.organizzazione:
            form.fields['account_pec'].queryset = AccountPEC.objects.filter(organizzazione=user.organizzazione)

        return form

    def form_valid(self, form):
        """Imposta l'utente corrente come creatore della schedulazione"""
        form.instance.creata_da = self.request.user

        # Gestione della creazione del task Celery
        with transaction.atomic():
            response = super().form_valid(form)

            # Crea il task Celery
            schedulazione = self.object
            task_id = crea_schedulazione_celery(schedulazione)

            # Aggiorna il task_id nella schedulazione
            schedulazione.task_id = task_id
            schedulazione.save(update_fields=['task_id'])

            messages.success(self.request, 'Schedulazione creata con successo')
            return response


class SchedulazioneUpdateView(LoginRequiredMixin, SuperuserOrManagerRequiredMixin, UpdateView):
    """Modifica di una schedulazione esistente"""
    model = SchedulazioneSincronizzazione
    form_class = SchedulazioneSincronizzazioneForm
    template_name = 'pec/schedulazione_form.html'
    success_url = reverse_lazy('schedulazione_list')

    def get_queryset(self):
        """Filtra le schedulazioni in base ai permessi dell'utente"""
        user = self.request.user

        # Se l'utente è un superuser, vede tutte le schedulazioni
        if user.is_superuser:
            return SchedulazioneSincronizzazione.objects.all()

        # Se l'utente è un Manager, vede solo le schedulazioni della sua organizzazione
        if is_manager(user) and user.organizzazione:
            return SchedulazioneSincronizzazione.objects.filter(account_pec__organizzazione=user.organizzazione)

        # Altri utenti non vedono nulla
        return SchedulazioneSincronizzazione.objects.none()

    def get_form(self, form_class=None):
        """Personalizza il form in base all'utente"""
        form = super().get_form(form_class)
        user = self.request.user

        # Limita gli account PEC disponibili in base ai permessi dell'utente
        if not user.is_superuser and is_manager(user) and user.organizzazione:
            form.fields['account_pec'].queryset = AccountPEC.objects.filter(organizzazione=user.organizzazione)

        return form

    def form_valid(self, form):
        """Gestione dell'aggiornamento del task Celery"""
        with transaction.atomic():
            # Salva il vecchio task_id
            old_task_id = self.object.task_id

            # Aggiorna l'oggetto
            response = super().form_valid(form)

            # Elimina il vecchio task Celery se esiste
            if old_task_id:
                elimina_schedulazione_celery(old_task_id)

            # Crea un nuovo task Celery
            schedulazione = self.object
            task_id = crea_schedulazione_celery(schedulazione)

            # Aggiorna il task_id nella schedulazione
            schedulazione.task_id = task_id
            schedulazione.save(update_fields=['task_id'])

            messages.success(self.request, 'Schedulazione aggiornata con successo')
            return response


class SchedulazioneDeleteView(LoginRequiredMixin, SuperuserOrManagerRequiredMixin, DeleteView):
    """Eliminazione di una schedulazione"""
    model = SchedulazioneSincronizzazione
    template_name = 'pec/schedulazione_confirm_delete.html'
    success_url = reverse_lazy('schedulazione_list')

    def get_queryset(self):
        """Filtra le schedulazioni in base ai permessi dell'utente"""
        user = self.request.user

        # Se l'utente è un superuser, vede tutte le schedulazioni
        if user.is_superuser:
            return SchedulazioneSincronizzazione.objects.all()

        # Se l'utente è un Manager, vede solo le schedulazioni della sua organizzazione
        if is_manager(user) and user.organizzazione:
            return SchedulazioneSincronizzazione.objects.filter(account_pec__organizzazione=user.organizzazione)

        # Altri utenti non vedono nulla
        return SchedulazioneSincronizzazione.objects.none()

    def delete(self, request, *args, **kwargs):
        """Gestione dell'eliminazione del task Celery"""
        with transaction.atomic():
            # Ottieni l'oggetto prima di eliminarlo
            self.object = self.get_object()

            # Elimina il task Celery se esiste
            if self.object.task_id:
                elimina_schedulazione_celery(self.object.task_id)

            messages.success(self.request, 'Schedulazione eliminata con successo')
            return super().delete(request, *args, **kwargs)


@login_required
@user_passes_test(lambda u: u.is_superuser or is_manager(u))
def toggle_schedulazione(request, pk):
    """Vista per attivare/disattivare una schedulazione"""
    # Recupera la schedulazione
    schedulazione = get_object_or_404(SchedulazioneSincronizzazione, pk=pk)

    # Verifica i permessi
    user = request.user
    if not user.is_superuser:
        if not is_manager(user) or not user.organizzazione:
            messages.error(request, 'Non hai i permessi per modificare questa schedulazione')
            return redirect('schedulazione_list')

        if schedulazione.account_pec.organizzazione != user.organizzazione:
            messages.error(request, 'Non hai i permessi per modificare questa schedulazione')
            return redirect('schedulazione_list')

    # Cambia lo stato
    schedulazione.attiva = not schedulazione.attiva

    # Gestisci il task Celery
    with transaction.atomic():
        # Se c'è un task esistente, eliminalo
        if schedulazione.task_id:
            elimina_schedulazione_celery(schedulazione.task_id)
            schedulazione.task_id = None

        # Se la schedulazione è attiva, crea un nuovo task
        if schedulazione.attiva:
            task_id = crea_schedulazione_celery(schedulazione)
            schedulazione.task_id = task_id

        # Salva le modifiche
        schedulazione.save()

    messages.success(request, f'Schedulazione {"attivata" if schedulazione.attiva else "disattivata"} con successo')
    return redirect('schedulazione_list')


# Template views per il nuovo frontend Django
class MessageListView(LoginRequiredMixin, ListView):
    """Lista dei messaggi PEC in entrata"""
    model = MessaggioPEC
    template_name = 'pec/message_list.html'
    context_object_name = 'messaggi'
    paginate_by = 20

    def get_queryset(self):
        """Filtra i messaggi in base ai permessi dell'utente e ai filtri impostati"""
        user = self.request.user

        # Base query in base ai permessi - solo messaggi in entrata
        if user.is_superuser:
            # Admin vede tutti i messaggi in entrata
            queryset = MessaggioPEC.objects.filter(direzione='ENTRATA')
        elif user.is_manager():
            # Manager vede tutti i messaggi in entrata della sua organizzazione
            if user.organizzazione:
                queryset = MessaggioPEC.objects.filter(
                    account_pec__organizzazione=user.organizzazione,
                    direzione='ENTRATA'
                )
            else:
                queryset = MessaggioPEC.objects.none()
        elif user.is_staff_user():
            # Staff vede solo i messaggi in entrata degli account a cui è stato assegnato
            # o dei clienti a cui è stato assegnato
            account_messages = MessaggioPEC.objects.filter(
                account_pec__in=user.account_pec_autorizzati.all(),
                direzione='ENTRATA'
            )
            client_messages = MessaggioPEC.objects.filter(
                cliente__in=user.clienti_assegnati.all(),
                direzione='ENTRATA'
            )
            queryset = (account_messages | client_messages).distinct()
        else:
            # Retrocompatibilità per altri utenti
            account_messages = MessaggioPEC.objects.filter(
                account_pec__utenti_autorizzati=user,
                direzione='ENTRATA'
            )
            client_messages = MessaggioPEC.objects.filter(
                cliente__referente__utente=user,
                direzione='ENTRATA'
            )
            queryset = (account_messages | client_messages).distinct()

        # Applica i filtri dalla query string
        filters = {}

        # Filtro per account
        account_id = self.request.GET.get('account')
        if account_id:
            filters['account_pec_id'] = account_id

        # Filtro per stato
        stato = self.request.GET.get('stato')
        if stato:
            filters['stato'] = stato

        # Filtro per cliente
        cliente = self.request.GET.get('cliente')
        if cliente == 'null':
            filters['cliente__isnull'] = True
        elif cliente:
            filters['cliente_id'] = cliente

        # Filtro per data
        data_da = self.request.GET.get('data_da')
        if data_da:
            filters['data_ricezione__gte'] = data_da

        data_a = self.request.GET.get('data_a')
        if data_a:
            filters['data_ricezione__lte'] = data_a + ' 23:59:59'

        # Filtro di ricerca
        search = self.request.GET.get('search')
        if search:
            from django.db.models import Q

            # Filtra per oggetto, mittente nel database o mittente_postacert
            # Utilizziamo icontains per una ricerca case-insensitive
            queryset = queryset.filter(
                Q(oggetto__icontains=search) |
                Q(mittente__nome__icontains=search) |
                Q(mittente__email__icontains=search) |
                Q(mittente_postacert__icontains=search)
            )

            # Debug: stampa i risultati della ricerca
            print(f"Ricerca per '{search}': trovati {queryset.count()} risultati")
            for msg in queryset[:5]:  # Mostra solo i primi 5 per brevità
                print(f"ID: {msg.id}, Oggetto: {msg.oggetto}, Mittente DB: {msg.mittente}, Mittente Postacert: '{msg.mittente_postacert}'")

            # Se non ci sono risultati, potrebbe essere un problema con il campo mittente_postacert
            if queryset.count() == 0:
                # Verifica se ci sono messaggi con mittente_postacert valorizzato
                has_mittente_postacert = MessaggioPEC.objects.exclude(mittente_postacert='').exclude(mittente_postacert__isnull=True).exists()
                print(f"Esistono messaggi con mittente_postacert valorizzato: {has_mittente_postacert}")

                # Verifica se ci sono messaggi che contengono la stringa di ricerca in qualsiasi campo
                test_query = MessaggioPEC.objects.filter(
                    Q(oggetto__icontains=search) |
                    Q(mittente__nome__icontains=search) |
                    Q(mittente__email__icontains=search)
                )
                print(f"Ricerca senza mittente_postacert: trovati {test_query.count()} risultati")

        # Applica i filtri
        queryset = queryset.filter(**filters)

        # Ordina per data di ricezione (più recenti prima)
        queryset = queryset.order_by('-data_ricezione')

        # Per ogni messaggio, aggiunge informazioni utili per il template
        result = []

        import re
        import email
        from email.header import decode_header

        for messaggio in queryset:
            # Prepara le info di base del messaggio
            messaggio_info = {
                'id': messaggio.id,
                'oggetto': messaggio.oggetto,
                'data_ricezione': messaggio.data_ricezione,
                'stato': messaggio.stato,
                'get_stato_display': messaggio.get_stato_display(),
            }

            # Info sul cliente
            if messaggio.cliente:
                messaggio_info['cliente_info'] = {
                    'id': messaggio.cliente.id,
                    'nome': messaggio.cliente.nome,
                }
            else:
                messaggio_info['cliente_info'] = None

            # Account PEC info
            if messaggio.account_pec:
                messaggio_info['account_pec_info'] = messaggio.account_pec.nome
            else:
                messaggio_info['account_pec_info'] = None

            # Mittente info
            mittente_info = None

            # Verifica se il messaggio è una ricevuta
            is_ricevuta = False
            receipt_type = None

            # Pattern comuni negli oggetti delle ricevute
            if re.search(r'ACCETTAZIONE|POSTA CERTIFICATA: accettazione', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'CONSEGNA|POSTA CERTIFICATA: avvenuta consegna', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'MANCATA CONSEGNA|POSTA CERTIFICATA: mancata consegna', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'PRESA IN CARICO', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'ANOMALIA MESSAGGIO|ANOMALIA', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True
            elif re.search(r'ERRORE DI CONSEGNA|MAILER.?DAEMON|DELIVERY.{0,10}(STATUS|FAIL|ERROR)', messaggio.oggetto, re.IGNORECASE):
                is_ricevuta = True

            # Estrai il mittente appropriato in base al tipo di messaggio
            if messaggio.mittente:
                # Se c'è un mittente nel DB, lo usiamo come fallback
                mittente_info = {
                    'id': messaggio.mittente.id,
                    'nome': messaggio.mittente.nome,
                    'email': messaggio.mittente.email,
                }

            if messaggio.messaggio_completo:
                try:
                    # Per le ricevute, estrai info direttamente dal messaggio completo
                    if is_ricevuta:
                        email_message = email.message_from_bytes(messaggio.messaggio_completo)
                        from_header = email_message.get('From', '')
                        if from_header:
                            try:
                                decoded_parts = decode_header(from_header)
                                from_decoded = ' '.join([
                                    part.decode(encoding or 'utf-8', errors='replace')
                                    if isinstance(part, bytes) else part
                                    for part, encoding in decoded_parts
                                ])
                                mittente_info = {'nome': from_decoded, 'email': '', 'id': None}
                            except Exception:
                                mittente_info = {'nome': from_header, 'email': '', 'id': None}

                    # Per i messaggi normali, estrai da postacert.eml
                    else:
                        # Cerca postacert.eml
                        raw_message = messaggio.messaggio_completo.decode('latin-1', errors='replace')
                        postacert_found = False

                        # Cerca "filename="postacert.eml"" nel messaggio
                        postacert_pos = raw_message.find('filename="postacert.eml"')
                        if postacert_pos == -1:
                            postacert_pos = raw_message.find('filename=postacert.eml')

                        if postacert_pos > 0:
                            # Cerca il boundary precedente
                            boundary_start = raw_message.rfind('--', 0, postacert_pos)
                            if boundary_start > 0:
                                # Estrai il boundary
                                boundary_end = raw_message.find('\r\n', boundary_start)
                                if boundary_end > 0:
                                    boundary = raw_message[boundary_start:boundary_end].strip()

                                    # Trova l'inizio del contenuto (dopo i doppi CRLF)
                                    header_end = raw_message.find('\r\n\r\n', postacert_pos)
                                    if header_end > 0:
                                        # Trova il prossimo boundary (fine del contenuto)
                                        next_boundary = raw_message.find(boundary, header_end)
                                        if next_boundary > 0:
                                            # Estrai header e payload
                                            header = raw_message[boundary_start:header_end]
                                            payload_raw = raw_message[header_end+4:next_boundary-2]  # dopo \r\n\r\n, prima di \r\n--boundary

                                            # Determina l'encoding
                                            if 'Content-Transfer-Encoding: base64' in header:
                                                import base64
                                                payload = base64.b64decode(payload_raw)
                                            elif 'Content-Transfer-Encoding: quoted-printable' in header:
                                                import quopri
                                                payload = quopri.decodestring(payload_raw.encode('latin-1'))
                                            else:
                                                payload = payload_raw.encode('latin-1')

                                            # Abbiamo il payload di postacert.eml
                                            postacert_message = email.message_from_bytes(payload)
                                            postacert_found = True

                                            # Estrai il mittente
                                            if postacert_message:
                                                from_header = postacert_message.get('From', '')
                                                if from_header:
                                                    try:
                                                        decoded_parts = decode_header(from_header)
                                                        from_decoded = ' '.join([
                                                            part.decode(encoding or 'utf-8', errors='replace')
                                                            if isinstance(part, bytes) else part
                                                            for part, encoding in decoded_parts
                                                        ])
                                                        mittente_info = {'nome': from_decoded, 'email': '', 'id': None}
                                                    except Exception:
                                                        mittente_info = {'nome': from_header, 'email': '', 'id': None}

                        # Se non abbiamo trovato postacert.eml, proviamo con l'approccio del parser email
                        if not postacert_found:
                            email_message = email.message_from_bytes(messaggio.messaggio_completo)

                            # Cerca l'allegato postacert.eml
                            for part in email_message.walk():
                                filename = part.get_filename()
                                content_disp = part.get('Content-Disposition', '')

                                if ((filename and "postacert.eml" in filename.lower()) or
                                   ("filename=\"postacert.eml\"" in content_disp.lower()) or
                                   part.get_content_type() == "message/rfc822"):
                                    # Estratto postacert.eml
                                    postacert_content = part.get_payload(decode=True)
                                    if postacert_content:
                                        inner_message = email.message_from_bytes(postacert_content)
                                        from_header = inner_message.get('From', '')
                                        if from_header:
                                            try:
                                                decoded_parts = decode_header(from_header)
                                                from_decoded = ' '.join([
                                                    part.decode(encoding or 'utf-8', errors='replace')
                                                    if isinstance(part, bytes) else part
                                                    for part, encoding in decoded_parts
                                                ])
                                                mittente_info = {'nome': from_decoded, 'email': '', 'id': None}
                                                break
                                            except Exception:
                                                mittente_info = {'nome': from_header, 'email': '', 'id': None}
                                                break

                except Exception:
                    # In caso di errore, usiamo il mittente salvato nel DB come fallback
                    pass

            # Se non abbiamo trovato nessun mittente, usa un valore di default
            if not mittente_info:
                mittente_info = {'nome': 'Non disponibile', 'email': '', 'id': None}

            messaggio_info['mittente_info'] = mittente_info

            # Aggiungi il messaggio con le info arricchite al risultato
            result.append(messaggio_info)

        return result

    def get_context_data(self, **kwargs):
        """Aggiunge contesto extra alla vista"""
        context = super().get_context_data(**kwargs)

        # Aggiunge la lista degli account per il filtro
        user = self.request.user

        # Se l'utente è un superuser, vede tutti gli account
        if user.is_superuser:
            context['accounts'] = AccountPEC.objects.all()
        # Se l'utente è un Manager, vede tutti gli account della sua organizzazione
        elif user.is_manager() and user.organizzazione:
            context['accounts'] = AccountPEC.objects.filter(organizzazione=user.organizzazione)
        # Se l'utente è uno Staff, vede solo gli account a cui è stato assegnato
        elif user.is_staff_user():
            context['accounts'] = user.account_pec_autorizzati.all()
        # Retrocompatibilità per altri utenti
        else:
            context['accounts'] = AccountPEC.objects.filter(utenti_autorizzati=user)

        # Aggiunge la lista dei clienti per il filtro
        from apps.clienti.models import Cliente

        # Se l'utente è un superuser, vede tutti i clienti
        if user.is_superuser:
            context['clienti'] = Cliente.objects.filter(attivo=True)
        # Se l'utente è un Manager, vede tutti i clienti della sua organizzazione
        elif user.is_manager() and user.organizzazione:
            context['clienti'] = Cliente.objects.filter(organizzazione=user.organizzazione, attivo=True)
        # Se l'utente è uno Staff, vede solo i clienti a cui è stato assegnato
        elif user.is_staff_user():
            context['clienti'] = user.clienti_assegnati.filter(attivo=True)
        # Retrocompatibilità per altri utenti
        else:
            context['clienti'] = Cliente.objects.filter(referente__utente=user, attivo=True).distinct()

        # Aggiungi i permessi al contesto
        context['is_manager'] = user.is_manager()
        context['is_staff'] = user.is_staff_user()

        return context


class MessageDetailView(LoginRequiredMixin, DetailView):
    """Dettaglio di un messaggio PEC"""
    model = MessaggioPEC
    template_name = 'pec/message_detail.html'
    context_object_name = 'messaggio'

    def get_queryset(self):
        """Filtra i messaggi in base ai permessi dell'utente"""
        user = self.request.user

        # Se l'utente è un superuser, vede tutti i messaggi
        if user.is_superuser:
            return MessaggioPEC.objects.all()

        # Se l'utente è un Manager, vede tutti i messaggi della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return MessaggioPEC.objects.filter(account_pec__organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo i messaggi degli account a cui è stato assegnato
        # o dei clienti a cui è stato assegnato
        if user.is_staff_user():
            account_messages = MessaggioPEC.objects.filter(account_pec__in=user.account_pec_autorizzati.all())
            client_messages = MessaggioPEC.objects.filter(cliente__in=user.clienti_assegnati.all())
            return (account_messages | client_messages).distinct()

        # Retrocompatibilità per altri utenti
        account_messages = MessaggioPEC.objects.filter(account_pec__utenti_autorizzati=user)
        client_messages = MessaggioPEC.objects.filter(cliente__referente__utente=user)
        return (account_messages | client_messages).distinct()

    def get_context_data(self, **kwargs):
        """Aggiunge contesto extra alla vista"""
        context = super().get_context_data(**kwargs)
        messaggio = self.get_object()
        user = self.request.user

        # Registra l'accesso
        LogAccesso.objects.create(
            utente=user,
            messaggio=messaggio
        )

        # Determina se il messaggio è in uscita
        is_outbox = messaggio.direzione == 'USCITA'
        context['is_outbox'] = is_outbox

        # Imposta il link corretto per tornare alla lista messaggi
        context['list_url'] = 'outbox_list' if is_outbox else 'message_list'

        # Per i messaggi in uscita, gestiamo diversamente il contenuto
        if is_outbox:
            # Impostiamo direttamente il contenuto del messaggio
            if messaggio.contenuto:
                context['messaggio_body'] = messaggio.contenuto
                context['is_html'] = '<html' in messaggio.contenuto.lower() or '<body' in messaggio.contenuto.lower()
                context['is_messaggio_originale'] = False
            else:
                # Se il contenuto è vuoto, mostriamo un messaggio informativo specifico per i messaggi in uscita
                context['messaggio_body'] = "<div class='alert alert-info'><h5>Contenuto del messaggio non disponibile</h5><p>Il contenuto di questo messaggio in uscita non è stato salvato nel database.</p><p>Questo può accadere per messaggi inviati con una versione precedente dell'applicazione.</p></div>"
                context['is_html'] = True
                context['is_messaggio_originale'] = False

            # Verifica i permessi dell'utente anche per i messaggi in uscita
            from .permissions import PermessiPEC
            context['puo_aggiornare_stato'] = PermessiPEC.utente_puo_aggiornare_stato(user, messaggio)
            context['puo_assegnare_cliente'] = PermessiPEC.utente_puo_assegnare_cliente(user, messaggio)
            context['puo_assegnare_referente'] = PermessiPEC.utente_puo_assegnare_referente(user, messaggio)

            # Impostiamo il mittente come l'account PEC utilizzato per l'invio
            if messaggio.account_pec:
                context['mittente_postacert'] = f"{messaggio.account_pec.nome} <{messaggio.account_pec.indirizzo_email}>"

            # Aggiunge gli allegati
            allegati = Allegato.objects.filter(messaggio=messaggio)
            context['allegati'] = allegati

            # Aggiungiamo l'utente al contesto per poterlo utilizzare nel template
            context['user'] = user

            # Aggiunge i clienti e referenti per l'assegnazione
            from apps.clienti.models import Cliente, Referente
            context['clienti'] = Cliente.objects.filter(attivo=True)

            if messaggio.cliente:
                context['referenti'] = Referente.objects.filter(cliente=messaggio.cliente)
            else:
                context['referenti'] = []

            # Aggiunge la lista degli utenti per l'inoltro
            if user.is_superuser:
                context['utenti'] = UtentePersonalizzato.objects.filter(is_active=True).exclude(email='')
            elif user.is_manager() and user.organizzazione:
                context['utenti'] = UtentePersonalizzato.objects.filter(
                    is_active=True,
                    organizzazione=user.organizzazione
                ).exclude(email='')
            else:
                # Per gli utenti staff, mostra solo gli utenti della stessa organizzazione
                if user.organizzazione:
                    context['utenti'] = UtentePersonalizzato.objects.filter(
                        is_active=True,
                        organizzazione=user.organizzazione
                    ).exclude(email='')

            # Per i messaggi in uscita, ritorniamo subito il contesto senza ulteriori elaborazioni
            # che potrebbero sovrascrivere il contenuto
            return context

        # Aggiunge gli allegati
        allegati = Allegato.objects.filter(messaggio=messaggio)
        context['allegati'] = allegati

        # Aggiungiamo l'utente al contesto per poterlo utilizzare nel template
        context['user'] = user

        # Aggiunge la lista degli utenti per l'inoltro
        if user.is_superuser:
            context['utenti'] = UtentePersonalizzato.objects.filter(is_active=True).exclude(email='')
        elif user.is_manager() and user.organizzazione:
            context['utenti'] = UtentePersonalizzato.objects.filter(
                is_active=True,
                organizzazione=user.organizzazione
            ).exclude(email='')
        else:
            # Per gli utenti staff, mostra solo gli utenti della stessa organizzazione
            if user.organizzazione:
                context['utenti'] = UtentePersonalizzato.objects.filter(
                    is_active=True,
                    organizzazione=user.organizzazione
                ).exclude(email='')

        # Verifica se il messaggio è una ricevuta PEC (di accettazione o consegna)
        import re

        oggetto = messaggio.oggetto
        receipt_type = None

        # Pattern comuni negli oggetti delle ricevute
        if re.search(r'ACCETTAZIONE|POSTA CERTIFICATA: accettazione', oggetto, re.IGNORECASE):
            receipt_type = "Ricevuta di Accettazione"
        elif re.search(r'CONSEGNA|POSTA CERTIFICATA: avvenuta consegna', oggetto, re.IGNORECASE):
            receipt_type = "Ricevuta di Avvenuta Consegna"
        elif re.search(r'MANCATA CONSEGNA|POSTA CERTIFICATA: mancata consegna', oggetto, re.IGNORECASE):
            receipt_type = "Avviso di Mancata Consegna"
        elif re.search(r'PRESA IN CARICO', oggetto, re.IGNORECASE):
            receipt_type = "Ricevuta di Presa in Carico"
        elif re.search(r'ANOMALIA MESSAGGIO|ANOMALIA|PROBLEMA.{0,10}MESSAGGIO', oggetto, re.IGNORECASE):
            receipt_type = "Avviso di Anomalia"
        elif re.search(r'ERRORE DI CONSEGNA|MAILER.?DAEMON|DELIVERY.{0,10}(STATUS|FAIL|ERROR)', oggetto, re.IGNORECASE):
            receipt_type = "Notifica di Errore"

        # Se non abbiamo trovato un tipo di ricevuta nell'oggetto, verifichiamo anche il contenuto
        if not receipt_type and messaggio.contenuto:
            if re.search(r'ricevuta di accettazione', messaggio.contenuto, re.IGNORECASE):
                receipt_type = "Ricevuta di Accettazione"
            elif re.search(r'ricevuta di avvenuta consegna', messaggio.contenuto, re.IGNORECASE):
                receipt_type = "Ricevuta di Avvenuta Consegna"
            elif re.search(r'avviso di non accettazione', messaggio.contenuto, re.IGNORECASE):
                receipt_type = "Avviso di Non Accettazione"
            elif re.search(r'avviso di mancata consegna', messaggio.contenuto, re.IGNORECASE):
                receipt_type = "Avviso di Mancata Consegna"

        is_ricevuta = receipt_type is not None
        context['is_ricevuta'] = is_ricevuta
        if is_ricevuta:
            context['ricevuta_tipo'] = receipt_type

        # Se è una ricevuta, estraiamo il corpo del messaggio direttamente dal file EML completo
        if is_ricevuta:
            print(f"Messaggio {messaggio.id}: È una ricevuta PEC, estraiamo direttamente dal messaggio completo")

            # La tipologia di ricevuta è già impostata precedentemente
            print(f"Ricevuta identificata: {context['ricevuta_tipo']}")

            # Inizializzazione variabili
            found = False
            content = None
            is_html = False

            # Estraiamo il contenuto direttamente dal messaggio completo
            if messaggio.messaggio_completo:
                try:
                    import email
                    from email.header import decode_header

                    # Parsifichiamo il messaggio completo
                    email_message = email.message_from_bytes(messaggio.messaggio_completo)

                    # Estraiamo il corpo del messaggio
                    html_content = None
                    text_content = None

                    # Debug per vedere la struttura dettagliata del messaggio
                    print(f"Struttura dettagliata del messaggio {messaggio.id}:")
                    message_parts = []
                    for i, part in enumerate(email_message.walk()):
                        content_type = part.get_content_type()
                        filename = part.get_filename()
                        is_multipart = part.is_multipart()
                        content_disp = part.get('Content-Disposition', '')
                        print(f"  Parte {i}: tipo={content_type}, filename={filename}, multipart={is_multipart}, disposition={content_disp}")
                        message_parts.append((i, part))

                    # Approccio speciale per le ricevute di avvenuta consegna
                    if "Ricevuta di Avvenuta Consegna" in context['ricevuta_tipo']:
                        print(f"Elaborazione speciale per ricevuta di avvenuta consegna {messaggio.id}")

                        # Cerca la parte di testo che contiene l'informazione della ricevuta
                        for part_info in message_parts:
                            i, part = part_info
                            content_type = part.get_content_type()

                            # Il testo della ricevuta è spesso nella prima parte di testo
                            if content_type in ['text/plain', 'text/html'] and not part.is_multipart():
                                try:
                                    part_content = part.get_payload(decode=True)
                                    if part_content:
                                        # Prova diversi encoding per le ricevute di consegna
                                        # Il problema è spesso con caratteri accentati come 'è'
                                        encodings_to_try = ['utf-8', 'latin-1', 'iso-8859-1', 'windows-1252']
                                        part_text = None

                                        # Prova diversi encoding finché non troviamo uno che funziona bene
                                        for encoding in encodings_to_try:
                                            try:
                                                decoded_text = part_content.decode(encoding)
                                                # Verifica se i caratteri speciali sono decodificati correttamente
                                                if 'è' in decoded_text and 'à' in decoded_text:
                                                    print(f"Trovato encoding corretto per ricevuta di consegna: {encoding}")
                                                    part_text = decoded_text
                                                    break
                                            except UnicodeDecodeError:
                                                continue

                                        # Se nessun encoding funziona bene, usa replace per gestire i caratteri problematici
                                        if not part_text:
                                            part_text = part_content.decode(errors='replace')
                                            # Correzione manuale per il carattere 'è'
                                            part_text = part_text.replace('�', 'è')

                                        # Verifico se il testo contiene informazioni tipiche di una ricevuta
                                        if 'avvenuta consegna' in part_text.lower() or 'data:' in part_text.lower() or 'il giorno' in part_text.lower():
                                            if content_type == 'text/html':
                                                html_content = part_text
                                                print(f"Trovato contenuto HTML ricevuta di consegna: {len(part_text)} caratteri")
                                                break
                                            elif not html_content:  # Salva testo solo se non abbiamo già HTML
                                                text_content = part_text
                                                print(f"Trovato contenuto testuale ricevuta di consegna: {len(part_text)} caratteri")
                                except Exception as e:
                                    print(f"Errore nell'elaborazione della parte {i}: {str(e)}")
                    else:
                        # Per altri tipi di ricevute, approccio standard
                        # Primo tentativo: estrarre direttamente dalle parti del messaggio
                        for part in email_message.walk():
                            if part.get_content_maintype() == 'text':
                                if part.is_multipart():
                                    continue

                                content_type = part.get_content_type()
                                part_content = part.get_payload(decode=True)

                                if part_content:
                                    # Utilizziamo lo stesso approccio di gestione encoding anche qui
                                    encodings_to_try = ['utf-8', 'latin-1', 'iso-8859-1', 'windows-1252']
                                    part_text = None

                                    # Prova diversi encoding finché non troviamo uno che funziona bene
                                    for encoding in encodings_to_try:
                                        try:
                                            decoded_text = part_content.decode(encoding)
                                            if 'è' in decoded_text or 'à' in decoded_text:
                                                print(f"Trovato encoding corretto: {encoding}")
                                                part_text = decoded_text
                                                break
                                        except UnicodeDecodeError:
                                            continue

                                    # Se nessun encoding funziona bene, usa replace e correggi manualmente
                                    if not part_text:
                                        part_text = part_content.decode(errors='replace')
                                        part_text = part_text.replace('�', 'è')

                                    if content_type == 'text/html' and not html_content:
                                        html_content = part_text
                                        print(f"Ricevuta {messaggio.id}: Trovato contenuto HTML {len(part_text)} caratteri")
                                    elif content_type == 'text/plain' and not text_content:
                                        text_content = part_text
                                        print(f"Ricevuta {messaggio.id}: Trovato contenuto testuale {len(part_text)} caratteri")

                    # Secondo tentativo: se non abbiamo trovato contenuto, cerchiamo nei payload non decodificati
                    if not html_content and not text_content:
                        print(f"Ricevuta {messaggio.id}: Tentativo di recupero dal payload non decodificato")
                        for part in email_message.walk():
                            if part.get_content_type() in ['text/html', 'text/plain']:
                                payload = part.get_payload()
                                if isinstance(payload, str) and len(payload) > 0:
                                    if part.get_content_type() == 'text/html' and not html_content:
                                        html_content = payload
                                    elif part.get_content_type() == 'text/plain' and not text_content:
                                        text_content = payload

                    # Preferisci contenuto HTML se disponibile
                    if html_content:
                        content = html_content
                        is_html = True
                        found = True
                    elif text_content:
                        content = text_content
                        is_html = False
                        found = True

                except Exception as e:
                    import traceback
                    print(f"Errore nell'estrazione della ricevuta: {str(e)}")
                    traceback.print_exc()

            # Se non siamo riusciti a estrarre il contenuto dal messaggio completo, usiamo il campo contenuto
            if not found or not content:
                print(f"Ricevuta {messaggio.id}: Fallback al campo contenuto")
                content = messaggio.contenuto
                is_html = '<html' in messaggio.contenuto.lower() or '<body' in messaggio.contenuto.lower()

            context['is_messaggio_originale'] = False  # Non è contenuto originale sbustato
            context['messaggio_originale_info'] = context['ricevuta_tipo']
            context['messaggio_body'] = content
            context['is_html'] = is_html

            # Aggiunge i clienti e referenti per l'assegnazione
            from apps.clienti.models import Cliente, Referente
            context['clienti'] = Cliente.objects.filter(attivo=True)

            if messaggio.cliente:
                context['referenti'] = Referente.objects.filter(cliente=messaggio.cliente)
            else:
                context['referenti'] = []

            return context

        # Implementazione diretta del metodo di estrazione usato nella vista diagnostica
        # che ha dimostrato di funzionare correttamente
        found = False
        content = None
        is_html = False
        messaggio_originale_info = None
        mittente_estratto = None

        if messaggio.messaggio_completo:
            try:
                # Importazione delle librerie necessarie
                import re
                import email
                import base64
                import quopri
                from email.header import decode_header

                # Decodifica il messaggio completo
                raw_message = messaggio.messaggio_completo.decode('latin-1', errors='replace')

                # APPROCCIO 1: Estrazione diretta con regex
                postacert_payload = None
                postacert_message = None

                if 'filename="postacert.eml"' in raw_message or 'filename=postacert.eml' in raw_message:
                    print(f"Messaggio {messaggio.id}: Trovato riferimento a postacert.eml con regex")

                    # Trova la posizione del riferimento a postacert.eml
                    postacert_pos = raw_message.find('filename="postacert.eml"')
                    if postacert_pos == -1:
                        postacert_pos = raw_message.find('filename=postacert.eml')

                    if postacert_pos > 0:
                        # Cerca il boundary precedente
                        boundary_start = raw_message.rfind('--', 0, postacert_pos)
                        if boundary_start > 0:
                            # Estrai il boundary
                            boundary_end = raw_message.find('\r\n', boundary_start)
                            if boundary_end > 0:
                                boundary = raw_message[boundary_start:boundary_end].strip()

                                # Trova l'inizio del contenuto (dopo i doppi CRLF)
                                header_end = raw_message.find('\r\n\r\n', postacert_pos)
                                if header_end > 0:
                                    # Trova il prossimo boundary (fine del contenuto)
                                    next_boundary = raw_message.find(boundary, header_end)
                                    if next_boundary > 0:
                                        # Estrai header e payload
                                        header = raw_message[boundary_start:header_end]
                                        payload_raw = raw_message[header_end+4:next_boundary-2]  # dopo \r\n\r\n, prima di \r\n--boundary

                                        # Determina l'encoding
                                        if 'Content-Transfer-Encoding: base64' in header:
                                            # Base64
                                            payload = base64.b64decode(payload_raw)
                                        elif 'Content-Transfer-Encoding: quoted-printable' in header:
                                            # Quoted-printable
                                            payload = quopri.decodestring(payload_raw.encode('latin-1'))
                                        else:
                                            # Assume testo
                                            payload = payload_raw.encode('latin-1')

                                        # Abbiamo il payload di postacert.eml
                                        postacert_payload = payload
                                        postacert_message = email.message_from_bytes(payload)

                                        # Estrai il mittente dal postacert.eml
                                        if postacert_message:
                                            from_header = postacert_message.get('From', '')
                                            if from_header:
                                                # Usa la libreria email per decodificare correttamente header con encoding
                                                # <AUTHOR> <EMAIL>"
                                                try:
                                                    decoded_parts = decode_header(from_header)
                                                    from_decoded = ' '.join([
                                                        part.decode(encoding or 'utf-8', errors='replace')
                                                        if isinstance(part, bytes) else part
                                                        for part, encoding in decoded_parts
                                                    ])
                                                    mittente_estratto = from_decoded
                                                    print(f"Mittente estratto da postacert.eml: {mittente_estratto}")
                                                except Exception as e:
                                                    print(f"Errore nella decodifica del mittente: {e}")
                                                    mittente_estratto = from_header

                                        # Cerca il contenuto del messaggio
                                        html_content = None
                                        text_content = None

                                        for part in postacert_message.walk():
                                            if part.get_content_maintype() == 'text':
                                                part_content = part.get_payload(decode=True)
                                                if part_content:
                                                    if part.get_content_type() == 'text/html' and not html_content:
                                                        html_content = part_content.decode(errors='replace')
                                                    elif part.get_content_type() == 'text/plain' and not text_content:
                                                        text_content = part_content.decode(errors='replace')

                                        # Usa HTML se disponibile, altrimenti testo
                                        if html_content:
                                            content = html_content
                                            is_html = True
                                            found = True
                                            messaggio_originale_info = "postacert.eml (estrazione diretta)"
                                        elif text_content:
                                            content = text_content
                                            is_html = False
                                            found = True
                                            messaggio_originale_info = "postacert.eml (estrazione diretta)"

                # APPROCCIO 2: Se non ha funzionato l'approccio diretto, proviamo con il parser email
                if not postacert_message or not found or not content:
                    print(f"Messaggio {messaggio.id}: Tentativo di estrazione con parser email")
                    email_message = email.message_from_bytes(messaggio.messaggio_completo)

                    # Cerca l'allegato postacert.eml
                    for part in email_message.walk():
                        filename = part.get_filename()
                        content_disp = part.get('Content-Disposition', '')

                        if ((filename and "postacert.eml" in filename.lower()) or
                           ("filename=\"postacert.eml\"" in content_disp.lower()) or
                           part.get_content_type() == "message/rfc822"):
                            print(f"Messaggio {messaggio.id}: Trovato postacert.eml o message/rfc822")

                            # Estratto postacert.eml
                            postacert_content = part.get_payload(decode=True)
                            if postacert_content:
                                # Ora parsiamo il contenuto di postacert.eml
                                inner_message = email.message_from_bytes(postacert_content)
                                postacert_message = inner_message

                                # Estrai il mittente se non è stato già estratto
                                if not mittente_estratto and inner_message:
                                    from_header = inner_message.get('From', '')
                                    if from_header:
                                        try:
                                            decoded_parts = decode_header(from_header)
                                            from_decoded = ' '.join([
                                                part.decode(encoding or 'utf-8', errors='replace')
                                                if isinstance(part, bytes) else part
                                                for part, encoding in decoded_parts
                                            ])
                                            mittente_estratto = from_decoded
                                            print(f"Mittente estratto da inner_message: {mittente_estratto}")
                                        except Exception as e:
                                            print(f"Errore nella decodifica del mittente: {e}")
                                            mittente_estratto = from_header

                                # Cerca parti HTML o testo
                                html_content = None
                                text_content = None

                                for inner_part in inner_message.walk():
                                    if inner_part.get_content_maintype() == 'text':
                                        inner_content = inner_part.get_payload(decode=True)
                                        if inner_content:
                                            if inner_part.get_content_type() == 'text/html' and not html_content:
                                                html_content = inner_content.decode(errors='replace')
                                            elif inner_part.get_content_type() == 'text/plain' and not text_content:
                                                text_content = inner_content.decode(errors='replace')

                                # Usa HTML se disponibile, altrimenti testo
                                if html_content:
                                    content = html_content
                                    is_html = True
                                    found = True
                                    messaggio_originale_info = "postacert.eml (parser email)"
                                elif text_content:
                                    content = text_content
                                    is_html = False
                                    found = True
                                    messaggio_originale_info = "postacert.eml (parser email)"

                                if found:
                                    break

                # APPROCCIO 3: Cerchiamo negli allegati esistenti come ultima risorsa
                if not postacert_message or not found or not content:
                    print(f"Messaggio {messaggio.id}: Ricerca negli allegati esistenti")
                    for allegato in allegati:
                        if allegato.nome_file.lower() == "postacert.eml":
                            print(f"Messaggio {messaggio.id}: Trovato postacert.eml negli allegati (ID: {allegato.id})")
                            try:
                                # Parsifichiamo l'allegato EML
                                email_message = email.message_from_bytes(allegato.contenuto)
                                postacert_message = email_message

                                # Estrai il mittente se non è stato già estratto
                                if not mittente_estratto and email_message:
                                    from_header = email_message.get('From', '')
                                    if from_header:
                                        try:
                                            decoded_parts = decode_header(from_header)
                                            from_decoded = ' '.join([
                                                part.decode(encoding or 'utf-8', errors='replace')
                                                if isinstance(part, bytes) else part
                                                for part, encoding in decoded_parts
                                            ])
                                            mittente_estratto = from_decoded
                                            print(f"Mittente estratto da allegato: {mittente_estratto}")
                                        except Exception as e:
                                            print(f"Errore nella decodifica del mittente: {e}")
                                            mittente_estratto = from_header

                                # Estraiamo il contenuto
                                html_content = None
                                text_content = None

                                for part in email_message.walk():
                                    if part.get_content_maintype() == 'text':
                                        if part.is_multipart():
                                            continue

                                        part_content = part.get_payload(decode=True)
                                        if part_content:
                                            part_text = part_content.decode(errors='replace')
                                            if part.get_content_type() == 'text/html' and not html_content:
                                                html_content = part_text
                                            elif part.get_content_type() == 'text/plain' and not text_content:
                                                text_content = part_text

                                # Usa HTML se disponibile, altrimenti testo
                                if html_content:
                                    content = html_content
                                    is_html = True
                                    found = True
                                    messaggio_originale_info = f"postacert.eml (allegato ID: {allegato.id})"
                                elif text_content:
                                    content = text_content
                                    is_html = False
                                    found = True
                                    messaggio_originale_info = f"postacert.eml (allegato ID: {allegato.id})"

                                if found:
                                    break
                            except Exception as e:
                                print(f"Errore nell'estrazione dall'allegato: {str(e)}")

            except Exception as e:
                import traceback
                print(f"Errore nell'estrazione di postacert.eml per il messaggio {messaggio.id}: {str(e)}")
                traceback.print_exc()

        # Se non abbiamo trovato il contenuto, mostra un messaggio di avviso
        if not found or not content:
            postacert_warning = """
            <div class="alert alert-warning">
                <h4><i class="fas fa-exclamation-triangle"></i> Messaggio originale non disponibile</h4>
                <p>Il file <strong>postacert.eml</strong> contenente il messaggio originale non è stato trovato o non è stato possibile estrarne il contenuto.</p>
                <p>Questo potrebbe essere dovuto a:</p>
                <ul>
                    <li>Il messaggio PEC non contiene l'allegato postacert.eml</li>
                    <li>Il messaggio è stato importato con una versione precedente dell'applicazione</li>
                    <li>Il messaggio ha un formato non standard</li>
                </ul>
                <p>Puoi:</p>
                <ul>
                    <li>Scaricare il messaggio EML completo e aprirlo con un client di posta</li>
                    <li>Utilizzare la funzione "Visualizza Postacert" per un'analisi più dettagliata</li>
                    <li>Provare a sincronizzare nuovamente l'account PEC</li>
                </ul>
            </div>
            """
            content = postacert_warning
            is_html = True

        # Aggiungiamo i risultati al contesto
        context['is_messaggio_originale'] = found
        context['messaggio_originale_info'] = messaggio_originale_info
        context['messaggio_body'] = content
        context['is_html'] = is_html

        # Aggiungiamo il mittente estratto al contesto
        if mittente_estratto:
            context['mittente_postacert'] = mittente_estratto

        # Aggiunge i clienti e referenti per l'assegnazione
        from apps.clienti.models import Cliente, Referente

        # Filtra i clienti in base ai permessi dell'utente
        user = self.request.user
        if user.is_superuser:
            context['clienti'] = Cliente.objects.filter(attivo=True)
        elif user.is_manager() and user.organizzazione:
            context['clienti'] = Cliente.objects.filter(organizzazione=user.organizzazione, attivo=True)
        elif user.is_staff_user():
            context['clienti'] = user.clienti_assegnati.filter(attivo=True)
        else:
            context['clienti'] = Cliente.objects.filter(referente__utente=user, attivo=True).distinct()

        # Carica i referenti per il cliente assegnato
        if messaggio.cliente:
            context['referenti'] = Referente.objects.filter(cliente=messaggio.cliente)
        else:
            context['referenti'] = []

        # Aggiungi i permessi specifici per le azioni
        from .permissions import PermessiPEC
        messaggio = self.get_object()
        user = self.request.user

        # Verifica se il messaggio è una ricevuta usando la funzione della classe PermessiPEC
        # Questo è solo per scopi informativi, non influisce sui permessi
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"MessageDetailView: Verificando se il messaggio {messaggio.id} è una ricevuta")
        logger.info(f"MessageDetailView: Oggetto del messaggio: {messaggio.oggetto}")

        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)

        logger.info(f"MessageDetailView: Risultato is_ricevuta: {is_ricevuta}")

        # Se non è già stato impostato nel contesto, impostiamo il flag is_ricevuta
        if 'is_ricevuta' not in context:
            context['is_ricevuta'] = is_ricevuta

        # Debug: stampiamo i permessi dell'utente per questo messaggio
        logger.info(f"MessageDetailView: Utente {user.username} ha permesso di aggiornare stato: {PermessiPEC.utente_puo_aggiornare_stato(user, messaggio)}")
        logger.info(f"MessageDetailView: Utente {user.username} ha permesso di assegnare cliente: {PermessiPEC.utente_puo_assegnare_cliente(user, messaggio)}")
        logger.info(f"MessageDetailView: Utente {user.username} ha permesso di assegnare referente: {PermessiPEC.utente_puo_assegnare_referente(user, messaggio)}")

        # Verifica permessi
        # Per manager e superuser, tutti i permessi sono sempre consentiti
        if user.is_manager() or user.is_superuser:
            context['puo_aggiornare_stato'] = True
            context['puo_assegnare_cliente'] = True
            context['puo_assegnare_referente'] = True
            logger.info(f"MessageDetailView: Utente {user.username} è manager/superuser, tutti i permessi consentiti")
        elif user.is_staff_user():
            # Per gli utenti Staff su messaggi ricevuta, verifichiamo direttamente l'autorizzazione
            if is_ricevuta:
                # Verifichiamo se l'utente ha accesso all'account
                has_account_access = user.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()

                if has_account_access:
                    # Per le ricevute, controlliamo direttamente il permesso nell'autorizzazione
                    from apps.pec.models import UtenteAccountPEC
                    try:
                        autorizzazione = UtenteAccountPEC.objects.get(
                            utente=user,
                            account=messaggio.account_pec
                        )
                        context['puo_aggiornare_stato'] = autorizzazione.puo_aggiornare_stato
                        context['puo_assegnare_cliente'] = autorizzazione.puo_assegnare_cliente
                        context['puo_assegnare_referente'] = autorizzazione.puo_assegnare_referente
                        logger.info(f"MessageDetailView: Utente Staff {user.username} su ricevuta - permessi diretti: "
                                   f"stato={autorizzazione.puo_aggiornare_stato}, "
                                   f"cliente={autorizzazione.puo_assegnare_cliente}, "
                                   f"referente={autorizzazione.puo_assegnare_referente}")
                    except UtenteAccountPEC.DoesNotExist:
                        context['puo_aggiornare_stato'] = False
                        context['puo_assegnare_cliente'] = False
                        context['puo_assegnare_referente'] = False
                        logger.info(f"MessageDetailView: Nessuna autorizzazione trovata per utente Staff {user.username}")
                else:
                    context['puo_aggiornare_stato'] = False
                    context['puo_assegnare_cliente'] = False
                    context['puo_assegnare_referente'] = False
            else:
                # Messaggi normali - usa le funzioni standard
                context['puo_aggiornare_stato'] = PermessiPEC.utente_puo_aggiornare_stato(user, messaggio)
                context['puo_assegnare_cliente'] = PermessiPEC.utente_puo_assegnare_cliente(user, messaggio)
                context['puo_assegnare_referente'] = PermessiPEC.utente_puo_assegnare_referente(user, messaggio)
                logger.info(f"MessageDetailView: Utente Staff {user.username} su messaggio normale - permessi: "
                          f"stato={context['puo_aggiornare_stato']}, "
                          f"cliente={context['puo_assegnare_cliente']}, "
                          f"referente={context['puo_assegnare_referente']}")
        else:
            # Altro tipo di utente - nessun permesso
            context['puo_aggiornare_stato'] = False
            context['puo_assegnare_cliente'] = False
            context['puo_assegnare_referente'] = False

        return context


class AccountListView(LoginRequiredMixin, ListView):
    """Lista degli account PEC"""
    model = AccountPEC
    template_name = 'pec/account_list.html'
    context_object_name = 'accounts'

    def get_queryset(self):
        """Filtra gli account in base ai permessi dell'utente"""
        user = self.request.user

        # Se l'utente è un superuser, vede tutti gli account
        if user.is_superuser:
            return AccountPEC.objects.all()

        # Se l'utente è un Manager, vede tutti gli account della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return AccountPEC.objects.filter(organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo gli account a cui è stato assegnato
        return user.account_pec_autorizzati.all()

    def get_context_data(self, **kwargs):
        """Aggiunge le organizzazioni e i permessi al contesto"""
        context = super().get_context_data(**kwargs)
        from apps.accounts.models import Organizzazione

        # Aggiungi le organizzazioni al contesto (solo per Manager e admin)
        if self.request.user.is_superuser or self.request.user.is_manager():
            context['organizzazioni'] = Organizzazione.objects.all()

        # Aggiungi i permessi al contesto
        context['is_manager'] = self.request.user.is_manager()
        context['is_staff'] = self.request.user.is_staff_user()

        return context


class AccountDetailView(LoginRequiredMixin, DetailView):
    """Dettaglio di un account PEC"""
    model = AccountPEC
    template_name = 'pec/account_detail.html'
    context_object_name = 'account'

    def get_queryset(self):
        """Filtra gli account in base ai permessi dell'utente"""
        user = self.request.user

        # Se l'utente è un superuser, vede tutti gli account
        if user.is_superuser:
            return AccountPEC.objects.all()

        # Se l'utente è un Manager, vede tutti gli account della sua organizzazione
        if user.is_manager() and user.organizzazione:
            return AccountPEC.objects.filter(organizzazione=user.organizzazione)

        # Se l'utente è uno Staff, vede solo gli account a cui è stato assegnato
        return user.account_pec_autorizzati.all()

    def get_context_data(self, **kwargs):
        """Aggiunge le organizzazioni e i permessi al contesto"""
        context = super().get_context_data(**kwargs)
        from apps.accounts.models import Organizzazione

        # Aggiungi le organizzazioni al contesto (solo per Manager e admin)
        if self.request.user.is_superuser or self.request.user.is_manager():
            context['organizzazioni'] = Organizzazione.objects.all()

        # Aggiungi i permessi al contesto
        context['is_manager'] = self.request.user.is_manager()
        context['is_staff'] = self.request.user.is_staff_user()
        context['can_edit'] = self.request.user.is_superuser or (
            self.request.user.is_manager() and
            self.object.organizzazione == self.request.user.organizzazione
        )

        return context


# Viste funzionali per le azioni sui messaggi
def change_message_status(request, pk):
    """Cambia lo stato di un messaggio"""
    if request.method == 'POST':
        user = request.user
        messaggio = get_object_or_404(MessaggioPEC, pk=pk)

        # Verifica i permessi usando la classe di utilità
        from .permissions import PermessiPEC
        import logging
        logger = logging.getLogger(__name__)

        # Verifica se il messaggio è una ricevuta
        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)
        logger.info(f"change_message_status: Messaggio {messaggio.id} è una ricevuta: {is_ricevuta}")

        # Gli utenti Manager possono sempre modificare lo stato, anche per le ricevute
        if user.is_manager() or user.is_superuser:
            can_update = True
            logger.info(f"change_message_status: Utente {user.username} può aggiornare stato perché è manager o superuser")
        elif user.is_staff_user():
            # Per gli utenti Staff su messaggi ricevuta, usiamo direttamente l'autorizzazione
            if is_ricevuta:
                # Verifichiamo se l'utente ha accesso all'account
                has_account_access = user.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()
                logger.info(f"change_message_status: Utente Staff {user.username} ha accesso all'account: {has_account_access}")

                if has_account_access:
                    # Per le ricevute, controlliamo direttamente il permesso nell'autorizzazione
                    from apps.pec.models import UtenteAccountPEC
                    try:
                        autorizzazione = UtenteAccountPEC.objects.get(
                            utente=user,
                            account=messaggio.account_pec
                        )
                        can_update = autorizzazione.puo_aggiornare_stato
                        logger.info(f"change_message_status: Utente Staff {user.username} su ricevuta - permesso diretto: {can_update}")
                    except UtenteAccountPEC.DoesNotExist:
                        can_update = False
                        logger.info(f"change_message_status: Nessuna autorizzazione trovata per utente Staff {user.username}")
                else:
                    can_update = False
            else:
                # Messaggi normali - usa la funzione standard
                can_update = PermessiPEC.utente_puo_aggiornare_stato(user, messaggio)
                logger.info(f"change_message_status: Utente {user.username} può aggiornare stato: {can_update}")
        else:
            can_update = False
            logger.info(f"change_message_status: Utente {user.username} non è né manager né staff, non può aggiornare stato")

        if not can_update:
            messages.error(request, 'Non hai il permesso di aggiornare lo stato di questo messaggio')
            return redirect('message_detail', pk=pk)

        # Cambia lo stato del messaggio
        stato = request.POST.get('stato')
        # Verifica se il messaggio è in uscita
        is_outbox = messaggio.direzione == 'USCITA'

        # Per i messaggi in uscita, accetta solo lo stato "INVIATO"
        if is_outbox and stato == 'INVIATO':
            messaggio.stato = stato
            messaggio.save()
            messages.success(request, 'Stato del messaggio aggiornato')
        # Per i messaggi in entrata, accetta gli stati standard
        elif not is_outbox and stato in ['DA_LEGGERE', 'DA_ASSEGNARE', 'ASSEGNATO', 'LAVORATO']:
            messaggio.stato = stato
            messaggio.save()
            messages.success(request, 'Stato del messaggio aggiornato')
        else:
            messages.error(request, 'Stato non valido per questo tipo di messaggio')

        return redirect('message_detail', pk=pk)

    return redirect('message_list')


def assign_client_to_message(request, pk):
    """Assegna un cliente a un messaggio"""
    if request.method == 'POST':
        user = request.user
        messaggio = get_object_or_404(MessaggioPEC, pk=pk)

        # Verifica i permessi usando la classe di utilità
        from .permissions import PermessiPEC
        import logging
        logger = logging.getLogger(__name__)

        # Verifica se il messaggio è una ricevuta
        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)
        logger.info(f"assign_client_to_message: Messaggio {messaggio.id} è una ricevuta: {is_ricevuta}")

        # Gli utenti Manager possono sempre assegnare clienti, anche per le ricevute
        if user.is_manager() or user.is_superuser:
            can_assign = True
            logger.info(f"assign_client_to_message: Utente {user.username} può assegnare cliente perché è manager o superuser")
        elif user.is_staff_user():
            # Per gli utenti Staff su messaggi ricevuta, usiamo direttamente l'autorizzazione
            if is_ricevuta:
                # Verifichiamo se l'utente ha accesso all'account
                has_account_access = user.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()
                logger.info(f"assign_client_to_message: Utente Staff {user.username} ha accesso all'account: {has_account_access}")

                if has_account_access:
                    # Per le ricevute, controlliamo direttamente il permesso nell'autorizzazione
                    from apps.pec.models import UtenteAccountPEC
                    try:
                        autorizzazione = UtenteAccountPEC.objects.get(
                            utente=user,
                            account=messaggio.account_pec
                        )
                        can_assign = autorizzazione.puo_assegnare_cliente
                        logger.info(f"assign_client_to_message: Utente Staff {user.username} su ricevuta - permesso diretto: {can_assign}")
                    except UtenteAccountPEC.DoesNotExist:
                        can_assign = False
                        logger.info(f"assign_client_to_message: Nessuna autorizzazione trovata per utente Staff {user.username}")
                else:
                    can_assign = False
            else:
                # Messaggi normali - usa la funzione standard
                can_assign = PermessiPEC.utente_puo_assegnare_cliente(user, messaggio)
                logger.info(f"assign_client_to_message: Utente {user.username} può assegnare cliente: {can_assign}")
        else:
            can_assign = False
            logger.info(f"assign_client_to_message: Utente {user.username} non è né manager né staff, non può assegnare cliente")

        if not can_assign:
            messages.error(request, 'Non hai il permesso di assegnare un cliente a questo messaggio')
            return redirect('message_detail', pk=pk)

        # Assegna il cliente
        cliente_id = request.POST.get('cliente_id')
        if cliente_id and cliente_id.strip():  # Verifica che cliente_id non sia vuoto o solo spazi
            try:
                cliente = get_object_or_404(Cliente, pk=cliente_id)

                # Assegna il cliente al messaggio
                messaggio.cliente = cliente

                # Se lo stato è "DA_LEGGERE", lo cambia in "DA_ASSEGNARE"
                if messaggio.stato == 'DA_LEGGERE':
                    messaggio.stato = 'DA_ASSEGNARE'

                # Salva le modifiche
                messaggio.save()

                # Registra l'associazione
                LogAssociazione.objects.create(
                    utente=request.user,
                    messaggio=messaggio,
                    cliente=cliente
                )

                messages.success(request, f'Cliente {cliente.nome} assegnato al messaggio')

                # Carica i referenti per il cliente appena assegnato
                from apps.clienti.models import Referente
                referenti = Referente.objects.filter(cliente=cliente)

                # Redirect con parametro per indicare che è necessario mostrare i referenti
                return redirect('message_detail', pk=pk)
            except Cliente.DoesNotExist:
                messages.error(request, f'Cliente con ID {cliente_id} non trovato')
            except Exception as e:
                messages.error(request, f'Errore durante l\'assegnazione del cliente: {str(e)}')
        else:
            # Rimuovi l'assegnazione del cliente
            messaggio.cliente = None
            messaggio.referente = None
            messaggio.save()
            messages.success(request, 'Assegnazione del cliente rimossa')

        return redirect('message_detail', pk=pk)

    return redirect('message_list')


def assign_referent_to_message(request, pk):
    """Assegna un referente a un messaggio"""
    if request.method == 'POST':
        user = request.user
        messaggio = get_object_or_404(MessaggioPEC, pk=pk)

        # Verifica i permessi usando la classe di utilità
        from .permissions import PermessiPEC
        import logging
        logger = logging.getLogger(__name__)

        # Verifica se il messaggio è una ricevuta
        is_ricevuta = PermessiPEC.is_ricevuta(messaggio)
        logger.info(f"assign_referent_to_message: Messaggio {messaggio.id} è una ricevuta: {is_ricevuta}")

        # Gli utenti Manager possono sempre assegnare referenti, anche per le ricevute
        if user.is_manager() or user.is_superuser:
            can_assign = True
            logger.info(f"assign_referent_to_message: Utente {user.username} può assegnare referente perché è manager o superuser")
        elif user.is_staff_user():
            # Per gli utenti Staff su messaggi ricevuta, usiamo direttamente l'autorizzazione
            if is_ricevuta:
                # Verifichiamo se l'utente ha accesso all'account
                has_account_access = user.account_pec_autorizzati.filter(id=messaggio.account_pec.id).exists()
                logger.info(f"assign_referent_to_message: Utente Staff {user.username} ha accesso all'account: {has_account_access}")

                if has_account_access:
                    # Per le ricevute, controlliamo direttamente il permesso nell'autorizzazione
                    from apps.pec.models import UtenteAccountPEC
                    try:
                        autorizzazione = UtenteAccountPEC.objects.get(
                            utente=user,
                            account=messaggio.account_pec
                        )
                        can_assign = autorizzazione.puo_assegnare_referente
                        logger.info(f"assign_referent_to_message: Utente Staff {user.username} su ricevuta - permesso diretto: {can_assign}")
                    except UtenteAccountPEC.DoesNotExist:
                        can_assign = False
                        logger.info(f"assign_referent_to_message: Nessuna autorizzazione trovata per utente Staff {user.username}")
                else:
                    can_assign = False
            else:
                # Messaggi normali - usa la funzione standard
                can_assign = PermessiPEC.utente_puo_assegnare_referente(user, messaggio)
                logger.info(f"assign_referent_to_message: Utente {user.username} può assegnare referente: {can_assign}")
        else:
            can_assign = False
            logger.info(f"assign_referent_to_message: Utente {user.username} non è né manager né staff, non può assegnare referente")

        if not can_assign:
            messages.error(request, 'Non hai il permesso di assegnare un referente a questo messaggio')
            return redirect('message_detail', pk=pk)

        # Assegna il referente
        referente_id = request.POST.get('referente_id')
        if referente_id:
            referente = get_object_or_404(Referente, pk=referente_id)

            # Verifica che il cliente del referente sia lo stesso del messaggio
            if messaggio.cliente and messaggio.cliente != referente.cliente:
                messages.error(request, 'Il referente deve appartenere allo stesso cliente del messaggio')
                return redirect('message_detail', pk=pk)

            # Assegna il referente e il cliente associato
            messaggio.referente = referente
            messaggio.cliente = referente.cliente

            # Se lo stato è "DA_ASSEGNARE", lo cambia in "ASSEGNATO"
            if messaggio.stato in ['DA_LEGGERE', 'DA_ASSEGNARE']:
                messaggio.stato = 'ASSEGNATO'

            messaggio.save()

            # Registra l'associazione al cliente se non esistente
            if not LogAssociazione.objects.filter(
                messaggio=messaggio, cliente=referente.cliente
            ).exists():
                LogAssociazione.objects.create(
                    utente=request.user,
                    messaggio=messaggio,
                    cliente=referente.cliente
                )

            messages.success(request, f'Referente {referente.utente.first_name} {referente.utente.last_name} assegnato al messaggio')
        else:
            # Rimuovi l'assegnazione del referente
            messaggio.referente = None
            messaggio.save()
            messages.success(request, 'Assegnazione del referente rimossa')

        return redirect('message_detail', pk=pk)

    return redirect('message_list')


def download_attachment(request, pk):
    """Scarica un allegato"""
    allegato = get_object_or_404(Allegato, pk=pk)

    # Verifica i permessi
    user = request.user
    if not (user.is_superuser or user.is_staff or user.is_manager()):
        account_auth = allegato.messaggio.account_pec.utenti_autorizzati.filter(id=user.id).exists()
        client_auth = allegato.messaggio.cliente and allegato.messaggio.cliente.referente_set.filter(utente=user).exists()
        if not (account_auth or client_auth):
            messages.error(request, 'Non hai il permesso di scaricare questo allegato')
            return redirect('message_list')

    response = HttpResponse(allegato.contenuto, content_type=allegato.tipo_file)
    response['Content-Disposition'] = f'attachment; filename="{allegato.nome_file}"'
    return response


def download_message(request, pk):
    """Scarica un messaggio in formato EML"""
    messaggio = get_object_or_404(MessaggioPEC, pk=pk)

    # Verifica i permessi
    user = request.user
    if not (user.is_superuser or user.is_staff or user.is_manager()):
        account_auth = messaggio.account_pec.utenti_autorizzati.filter(id=user.id).exists()
        client_auth = messaggio.cliente and messaggio.cliente.referente_set.filter(utente=user).exists()
        if not (account_auth or client_auth):
            messages.error(request, 'Non hai il permesso di scaricare questo messaggio')
            return redirect('message_list')

    response = HttpResponse(messaggio.messaggio_completo, content_type='message/rfc822')
    response['Content-Disposition'] = f'attachment; filename="messaggio_{messaggio.id}.eml"'
    return response


@login_required
def forward_message(request, pk):
    """Inoltra un messaggio PEC via email tradizionale"""
    import logging
    logger = logging.getLogger('django')
    logger.info(f"Inizio funzione forward_message per messaggio ID: {pk}")
    
    messaggio = get_object_or_404(MessaggioPEC, pk=pk)
    logger.info(f"Messaggio trovato: {messaggio.oggetto}")

    # Verifica i permessi
    user = request.user
    if not (user.is_superuser or user.is_staff_user() or user.is_manager()):
        account_auth = messaggio.account_pec.utenti_autorizzati.filter(id=user.id).exists()
        client_auth = messaggio.cliente and messaggio.cliente.referente_set.filter(utente=user).exists()
        if not (account_auth or client_auth):
            messages.error(request, 'Non hai il permesso di inoltrare questo messaggio')
            logger.warning(f"Utente {user.username} non ha permesso di inoltrare il messaggio {pk}")
            return redirect('message_detail', pk=pk)

    if request.method == 'POST':
        utente_id = request.POST.get('utente_id')
        logger.info(f"Richiesta di inoltro a utente ID: {utente_id}")
        
        if not utente_id:
            messages.error(request, 'Seleziona un utente a cui inoltrare il messaggio')
            logger.warning("Nessun utente selezionato per l'inoltro")
            return redirect('message_detail', pk=pk)

        try:
            utente_destinatario = UtentePersonalizzato.objects.get(id=utente_id)
            logger.info(f"Utente destinatario: {utente_destinatario.username} ({utente_destinatario.email})")

            # Verifica che l'utente abbia un'email
            if not utente_destinatario.email:
                messages.error(request, f"L'utente {utente_destinatario} non ha un indirizzo email")
                logger.warning(f"Utente {utente_destinatario.username} non ha un indirizzo email")
                return redirect('message_detail', pk=pk)

            # Controlla numero allegati
            num_allegati = messaggio.allegati.count()
            logger.info(f"Messaggio ha {num_allegati} allegati")
            
            # Verifica contenuto
            logger.info(f"Lunghezza contenuto: {len(messaggio.contenuto) if messaggio.contenuto else 0} caratteri")
            
            # Inoltra il messaggio
            logger.info(f"Tentativo di inoltro a {utente_destinatario.email}")
            try:
                inoltro_riuscito = EmailService.invia_inoltro_pec(messaggio, utente_destinatario)
                logger.info(f"Risultato inoltro: {inoltro_riuscito}")
                
                if inoltro_riuscito:
                    messages.success(request, f'Messaggio inoltrato con successo a {utente_destinatario.email}')
                    logger.info(f"Inoltro riuscito a {utente_destinatario.email}")
                else:
                    messages.error(request, f"Errore nell'inoltro del messaggio a {utente_destinatario.email}")
                    logger.error(f"Inoltro fallito a {utente_destinatario.email}")
            except Exception as email_err:
                logger.error(f"Eccezione durante l'invio email: {str(email_err)}", exc_info=True)
                messages.error(request, f"Errore specifico nell'invio email: {str(email_err)}")
                
        except UtentePersonalizzato.DoesNotExist:
            messages.error(request, 'Utente non trovato')
            logger.warning(f"Utente ID {utente_id} non trovato")
        except Exception as e:
            logger.error(f"Errore generale durante l'inoltro: {str(e)}", exc_info=True)
            messages.error(request, f"Errore durante l'inoltro: {str(e)}")

    logger.info(f"Fine funzione forward_message, redirect a message_detail per messaggio {pk}")
    return redirect('message_detail', pk=pk)


def view_postacert_for_message(request, pk):
    """Visualizza direttamente il contenuto di postacert.eml per un messaggio specifico"""
    # Questa funzione è utile principalmente per debug
    messaggio = get_object_or_404(MessaggioPEC, pk=pk)

    # Verifica i permessi
    user = request.user
    if not (user.is_superuser or user.is_staff or user.is_manager()):
        messages.error(request, 'Non hai il permesso di visualizzare questo contenuto')
        return redirect('message_list')

    # Cerca postacert.eml nel messaggio completo
    found = False
    content = None
    error = None

    try:
        # Approccio diretto usando regex sul messaggio completo
        import re
        import email
        import base64
        import quopri

        # Decodifica il messaggio completo
        raw_message = messaggio.messaggio_completo.decode('latin-1', errors='replace')

        # Cerca direttamente "postacert.eml" nel messaggio
        if 'filename="postacert.eml"' in raw_message or 'filename=postacert.eml' in raw_message:
            # Trova la posizione del riferimento a postacert.eml
            postacert_pos = raw_message.find('filename="postacert.eml"')
            if postacert_pos == -1:
                postacert_pos = raw_message.find('filename=postacert.eml')

            # Cerca il boundary precedente
            boundary_start = raw_message.rfind('--', 0, postacert_pos)
            if boundary_start > 0:
                # Estrai il boundary
                boundary_end = raw_message.find('\r\n', boundary_start)
                if boundary_end > 0:
                    boundary = raw_message[boundary_start:boundary_end].strip()

                    # Trova l'inizio del contenuto (dopo i doppi CRLF)
                    header_end = raw_message.find('\r\n\r\n', postacert_pos)
                    if header_end > 0:
                        # Trova il prossimo boundary (fine del contenuto)
                        next_boundary = raw_message.find(boundary, header_end)
                        if next_boundary > 0:
                            # Estrai header e payload
                            header = raw_message[boundary_start:header_end]
                            payload_raw = raw_message[header_end+4:next_boundary-2]  # dopo \r\n\r\n, prima di \r\n--boundary

                            # Determina l'encoding
                            if 'Content-Transfer-Encoding: base64' in header:
                                # Base64
                                payload = base64.b64decode(payload_raw)
                            elif 'Content-Transfer-Encoding: quoted-printable' in header:
                                # Quoted-printable
                                payload = quopri.decodestring(payload_raw.encode('latin-1'))
                            else:
                                # Assume testo
                                payload = payload_raw.encode('latin-1')

                            # Abbiamo il payload di postacert.eml, ora estraiamo il contenuto
                            postacert_message = email.message_from_bytes(payload)

                            # Cerca il contenuto del messaggio
                            html_content = None
                            text_content = None

                            for part in postacert_message.walk():
                                if part.get_content_maintype() == 'text':
                                    part_content = part.get_payload(decode=True)
                                    if part_content:
                                        if part.get_content_type() == 'text/html' and not html_content:
                                            html_content = part_content.decode(errors='replace')
                                        elif part.get_content_type() == 'text/plain' and not text_content:
                                            text_content = part_content.decode(errors='replace')

                            # Usa HTML se disponibile, altrimenti testo
                            content = html_content or text_content
                            if content:
                                found = True

        if not found or not content:
            # Approccio con il parser di email
            email_message = email.message_from_bytes(messaggio.messaggio_completo)

            # Cerca l'allegato postacert.eml
            for part in email_message.walk():
                filename = part.get_filename()
                content_disp = part.get('Content-Disposition', '')

                if ((filename and "postacert.eml" in filename.lower()) or
                   ("filename=\"postacert.eml\"" in content_disp.lower()) or
                   part.get_content_type() == "message/rfc822"):
                    # Estratto postacert.eml
                    postacert_content = part.get_payload(decode=True)
                    if postacert_content:
                        # Ora parsiamo il contenuto di postacert.eml
                        inner_message = email.message_from_bytes(postacert_content)

                        # Cerca parti HTML o testo
                        html_content = None
                        text_content = None

                        for inner_part in inner_message.walk():
                            if inner_part.get_content_maintype() == 'text':
                                inner_content = inner_part.get_payload(decode=True)
                                if inner_content:
                                    if inner_part.get_content_type() == 'text/html' and not html_content:
                                        html_content = inner_content.decode(errors='replace')
                                    elif inner_part.get_content_type() == 'text/plain' and not text_content:
                                        text_content = inner_content.decode(errors='replace')

                        # Usa HTML se disponibile, altrimenti testo
                        content = html_content or text_content
                        if content:
                            found = True
                            break

    except Exception as e:
        import traceback
        error = f"Errore nell'estrazione: {str(e)}\n{traceback.format_exc()}"

    # Prepara la risposta
    output = []
    output.append("<html><body>")
    output.append("<h1>Risultato Estrazione Postacert.eml</h1>")
    output.append(f"<p><strong>Messaggio ID:</strong> {pk}</p>")
    output.append(f"<p><strong>Trovato postacert.eml:</strong> {'Sì' if found else 'No'}</p>")

    if error:
        output.append(f"<h2>Errore</h2><pre>{error}</pre>")

    if found and content:
        output.append("<h2>Contenuto Estratto</h2>")
        if '<html' in content.lower():
            # Mostra come iframe se è HTML
            escaped_content = content.replace('"', '&quot;')
            output.append(f'<iframe srcdoc="{escaped_content}" style="width:100%; height:600px; border:1px solid #ccc;"></iframe>')
        else:
            # Altrimenti mostra come testo preformattato
            output.append(f"<pre>{content}</pre>")
    else:
        output.append("<p>Nessun contenuto estratto</p>")

    output.append("</body></html>")

    return HttpResponse("".join(output))


def sync_account(request, pk):
    """Avvia la sincronizzazione manuale di un account PEC"""
    if request.method == 'POST':
        user = request.user
        account = get_object_or_404(AccountPEC, pk=pk)

        # Verifica i permessi
        if user.is_superuser:
            # Admin può sincronizzare qualsiasi account
            pass
        elif user.is_manager():
            # Manager può sincronizzare solo gli account della sua organizzazione
            if account.organizzazione != user.organizzazione:
                messages.error(request, 'Non puoi sincronizzare account di altre organizzazioni')
                return redirect('account_list')
        elif user.is_staff_user():
            # Staff può sincronizzare solo gli account a cui è stato assegnato
            if not user.account_pec_autorizzati.filter(id=account.id).exists():
                messages.error(request, 'Non hai il permesso di sincronizzare questo account')
                return redirect('account_list')
        else:
            # Altri utenti possono sincronizzare solo gli account a cui sono stati autorizzati
            account_auth = account.utenti_autorizzati.filter(id=user.id).exists()
            if not account_auth:
                messages.error(request, 'Non hai il permesso di sincronizzare questo account')
                return redirect('account_list')

        # Controlla se è richiesta la sincronizzazione forzata (tutti i messaggi)
        force_all = 'force_all' in request.POST
        mode_text = "completa (tutti i messaggi)" if force_all else "normale (messaggi recenti)"

        # Prova ad avviare la sincronizzazione asincrona (con Celery)
        try:
            # Avvia la sincronizzazione asincrona
            # task = sincronizza_account_pec.delay(account.id)
            # Temporaneamente disattivata la versione asincrona per debugging
            raise Exception("Esecuzione sincrona forzata per debug")
        except Exception as e:
            # In caso di errore con Celery/Redis, esegui la sincronizzazione sincrona
            try:
                from .services import SincronizzazionePEC

                print(f"Avvio sincronizzazione {mode_text} per l'account {account.id} ({account.nome})")
                sync = SincronizzazionePEC(account.id)
                if not sync.connetti():
                    messages.error(request, 'Errore di connessione al server. Verifica le credenziali e le impostazioni del server.')
                    return redirect('account_detail', pk=pk)

                # Esegui la sincronizzazione con l'opzione forzata o meno
                result = sync.recupera_nuovi_messaggi(forza_tutti=force_all)

                if result:
                    account.ultima_sincronizzazione = timezone.now()
                    account.save()
                    messages.success(request, f'Sincronizzazione {mode_text} completata: {result} messaggi elaborati')
                else:
                    if result == 0:
                        messages.info(request, 'Sincronizzazione completata. Nessun nuovo messaggio trovato.')
                    else:
                        messages.error(request, 'Errore durante la sincronizzazione. Controlla i log per maggiori dettagli.')
            except Exception as sync_error:
                messages.error(request, f'Errore durante la sincronizzazione: {str(sync_error)}')
                print(f"Errore dettagliato: {str(sync_error)}")
                import traceback
                traceback.print_exc()

        return redirect('account_detail', pk=pk)

    return redirect('account_list')


def test_account_connection(request, pk):
    """Testa la connessione all'account PEC"""
    if request.method == 'POST':
        user = request.user
        account = get_object_or_404(AccountPEC, pk=pk)

        # Verifica i permessi
        if user.is_superuser:
            # Admin può testare qualsiasi account
            pass
        elif user.is_manager():
            # Manager può testare solo gli account della sua organizzazione
            if account.organizzazione != user.organizzazione:
                messages.error(request, 'Non puoi testare account di altre organizzazioni')
                return redirect('account_list')
        elif user.is_staff_user():
            # Staff può testare solo gli account a cui è stato assegnato
            if not user.account_pec_autorizzati.filter(id=account.id).exists():
                messages.error(request, 'Non hai il permesso di testare questo account')
                return redirect('account_list')
        else:
            # Altri utenti possono testare solo gli account a cui sono stati autorizzati
            account_auth = account.utenti_autorizzati.filter(id=user.id).exists()
            if not account_auth:
                messages.error(request, 'Non hai il permesso di testare questo account')
                return redirect('account_list')

        # Testa la connessione
        from .services import SincronizzazionePEC

        try:
            sync = SincronizzazionePEC(account.id)
            result = sync.connetti()

            if result:
                messages.success(request, 'Connessione riuscita! Il server risponde correttamente.')
            else:
                messages.error(request, 'Errore di connessione. Verifica le impostazioni del server.')
        except Exception as e:
            messages.error(request, f'Errore durante il test: {str(e)}')

        return redirect('account_detail', pk=pk)

    return redirect('account_list')


def update_account(request, pk):
    """Aggiorna i dati di un account PEC esistente"""
    if request.method == 'POST':
        # Verifica i permessi (solo Manager possono modificare account)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo i Manager possono modificare account PEC')
            return redirect('account_list')

        # Ottieni l'account
        account = get_object_or_404(AccountPEC, pk=pk)

        # Verifica che l'account appartenga all'organizzazione del Manager
        if request.user.is_manager() and account.organizzazione != request.user.organizzazione:
            messages.error(request, 'Non puoi modificare account di altre organizzazioni')
            return redirect('account_list')

        account = get_object_or_404(AccountPEC, pk=pk)

        # Estrai i dati dal form
        nome = request.POST.get('nome')
        indirizzo_email = request.POST.get('indirizzo_email')
        attivo = 'attivo' in request.POST
        server_imap = request.POST.get('server_imap')
        porta_imap = request.POST.get('porta_imap')
        ssl_imap = 'ssl_imap' in request.POST
        server_smtp = request.POST.get('server_smtp')
        porta_smtp = request.POST.get('porta_smtp')
        ssl_smtp = 'ssl_smtp' in request.POST
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Valida i dati
        if not all([nome, indirizzo_email, server_imap, porta_imap, server_smtp, porta_smtp, username]):
            messages.error(request, 'Tutti i campi obbligatori devono essere compilati')
            return redirect('account_detail', pk=pk)

        try:
            # Gestione dell'organizzazione
            organizzazione_id = request.POST.get('organizzazione')
            organizzazione = None
            if organizzazione_id:
                from apps.accounts.models import Organizzazione
                organizzazione = Organizzazione.objects.get(id=organizzazione_id)

            # Aggiorna i dati dell'account
            account.nome = nome
            account.indirizzo_email = indirizzo_email
            account.attivo = attivo
            account.server_imap = server_imap
            account.porta_imap = int(porta_imap)
            account.server_smtp = server_smtp
            account.porta_smtp = int(porta_smtp)
            account.username = username
            account.organizzazione = organizzazione

            # Aggiorna la password solo se è stata fornita
            if password:
                account.password = password

            account.save()

            messages.success(request, f'Account PEC "{nome}" aggiornato con successo')
        except Exception as e:
            messages.error(request, f'Errore nell\'aggiornamento dell\'account: {str(e)}')

        return redirect('account_detail', pk=pk)

    return redirect('account_list')


def toggle_account_status(request, pk):
    """Attiva o disattiva un account PEC"""
    if request.method == 'POST':
        # Verifica i permessi (solo Manager possono modificare lo stato)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo i Manager possono modificare lo stato degli account PEC')
            return redirect('account_list')

        account = get_object_or_404(AccountPEC, pk=pk)

        # Verifica che l'account appartenga all'organizzazione del Manager
        if request.user.is_manager() and account.organizzazione != request.user.organizzazione:
            messages.error(request, 'Non puoi modificare account di altre organizzazioni')
            return redirect('account_list')

        # Inverte lo stato corrente
        account.attivo = not account.attivo
        account.save()

        stato = "attivato" if account.attivo else "disattivato"
        messages.success(request, f'Account PEC "{account.nome}" {stato} con successo')

        return redirect('account_detail', pk=pk)

    return redirect('account_list')


def create_account(request):
    """Crea un nuovo account PEC"""
    if request.method == 'POST':
        # Verifica i permessi (solo Manager possono creare account)
        if not (request.user.is_superuser or request.user.is_manager()):
            messages.error(request, 'Solo i Manager possono creare account PEC')
            return redirect('account_list')

        # Estrai i dati dal form
        nome = request.POST.get('nome')
        indirizzo_email = request.POST.get('indirizzo_email')
        attivo = 'attivo' in request.POST
        server_imap = request.POST.get('server_imap')
        porta_imap = request.POST.get('porta_imap')
        ssl_imap = 'ssl_imap' in request.POST
        server_smtp = request.POST.get('server_smtp')
        porta_smtp = request.POST.get('porta_smtp')
        ssl_smtp = 'ssl_smtp' in request.POST
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Valida i dati
        if not all([nome, indirizzo_email, server_imap, porta_imap, server_smtp, porta_smtp, username, password]):
            messages.error(request, 'Tutti i campi obbligatori devono essere compilati')
            return redirect('account_list')

        try:
            # Gestione dell'organizzazione
            organizzazione = None

            # Per i Manager, l'organizzazione è automaticamente la loro
            if request.user.is_manager() and request.user.organizzazione:
                organizzazione = request.user.organizzazione
            # Per gli admin, usa l'organizzazione specificata
            elif request.user.is_superuser:
                organizzazione_id = request.POST.get('organizzazione')
                if organizzazione_id:
                    from apps.accounts.models import Organizzazione
                    organizzazione = Organizzazione.objects.get(id=organizzazione_id)

            # Crea un nuovo account
            account = AccountPEC.objects.create(
                nome=nome,
                indirizzo_email=indirizzo_email,
                attivo=attivo,
                server_imap=server_imap,
                porta_imap=int(porta_imap),
                server_smtp=server_smtp,
                porta_smtp=int(porta_smtp),
                username=username,
                password=password,
                organizzazione=organizzazione
            )

            # Aggiungi l'utente creatore agli utenti autorizzati
            account.utenti_autorizzati.add(request.user)

            messages.success(request, f'Account PEC "{nome}" creato con successo')
            return redirect('account_detail', pk=account.id)
        except Exception as e:
            messages.error(request, f'Errore nella creazione dell\'account: {str(e)}')
            return redirect('account_list')

    # GET: mostra il form
    from apps.accounts.models import Organizzazione
    context = {
        'is_manager': request.user.is_manager(),
        'is_staff': request.user.is_staff_user()
    }

    # Solo gli admin possono vedere tutte le organizzazioni
    if request.user.is_superuser:
        context['organizzazioni'] = Organizzazione.objects.all()
    elif request.user.is_manager() and request.user.organizzazione:
        context['organizzazione'] = request.user.organizzazione

    return render(request, 'pec/account_create.html', context)
