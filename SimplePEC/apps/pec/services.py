import imaplib
import email
import smtplib
import datetime
import os
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.base import MIMEBase
from email import encoders
from django.utils import timezone
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from .models import AccountPEC, MessaggioPEC, Allegato
from apps.clienti.models import Contatto
from apps.accounts.models import UtentePersonalizzato


class EmailService:
    """Classe per l'invio di email tradizionali"""

    @staticmethod
    def invia_inoltro_pec(messaggio_pec, utente_destinatario):
        """
        Inoltra un messaggio PEC a un utente tramite email tradizionale

        Args:
            messaggio_pec: Istanza di MessaggioPEC da inoltrare
            utente_destinatario: Istanza di UtentePersonalizzato a cui inoltrare il messaggio

        Returns:
            bool: True se l'invio è riuscito, False altrimenti
        """
        import logging
        logger = logging.getLogger('django')
        logger.info(f"Inizio invia_inoltro_pec - Messaggio ID: {messaggio_pec.id}")

        try:
            # Verifica che l'utente abbia un'email
            if not utente_destinatario.email:
                logger.error(f"L'utente {utente_destinatario} non ha un indirizzo email")
                return False

            # Prepara l'oggetto dell'email - elimina caratteri di fine riga
            oggetto_pulito = messaggio_pec.oggetto.replace('\r', ' ').replace('\n', ' ')
            oggetto = f"Inoltro PEC: {oggetto_pulito}"
            logger.info(f"Oggetto email originale: {messaggio_pec.oggetto}")
            logger.info(f"Oggetto email pulito: {oggetto}")

            # Prepara il contenuto dell'email
            mittente_info = messaggio_pec.mittente_postacert if messaggio_pec.mittente_postacert else (
                messaggio_pec.mittente.nome if messaggio_pec.mittente else "Non disponibile")
            logger.info(f"Mittente info: {mittente_info}")

            is_contenuto_html = False
            if messaggio_pec.contenuto:
                is_contenuto_html = '<html' in messaggio_pec.contenuto.lower() or '<body' in messaggio_pec.contenuto.lower()
            logger.info(f"Contenuto è HTML: {is_contenuto_html}")

            # Verifica il contenuto
            if not messaggio_pec.contenuto:
                logger.warning("Messaggio con contenuto vuoto")
                contenuto_messaggio = "(Contenuto del messaggio vuoto)"
            else:
                contenuto_messaggio = messaggio_pec.contenuto
                logger.info(f"Lunghezza contenuto: {len(contenuto_messaggio)} caratteri")

                # Se il contenuto è troppo grande, potrebbe causare problemi
                if len(contenuto_messaggio) > 500000:  # Limita a circa 500KB
                    logger.warning(f"Contenuto troppo grande ({len(contenuto_messaggio)} caratteri), troncato")
                    contenuto_messaggio = contenuto_messaggio[:100000] + "\n\n[...]\n\nContenuto troncato. Il messaggio originale è troppo grande. Accedi alla piattaforma SimplePEC per visualizzare il contenuto completo."

            # Crea il corpo dell'email in formato HTML
            html_content = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .header {{ background-color: #f5f5f5; padding: 15px; border-bottom: 1px solid #ddd; }}
                    .content {{ padding: 20px; }}
                    .footer {{ background-color: #f5f5f5; padding: 15px; border-top: 1px solid #ddd; font-size: 12px; color: #777; }}
                    .message-info {{ margin-bottom: 20px; }}
                    .message-info p {{ margin: 5px 0; }}
                    .message-content {{ border: 1px solid #ddd; padding: 15px; background-color: #fff; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>Inoltro Messaggio PEC</h2>
                </div>
                <div class="content">
                    <div class="message-info">
                        <p><strong>Da:</strong> {mittente_info}</p>
                        <p><strong>A:</strong> {messaggio_pec.account_pec.indirizzo_email}</p>
                        <p><strong>Data:</strong> {messaggio_pec.data_ricezione.strftime('%d/%m/%Y %H:%M')}</p>
                        <p><strong>Oggetto:</strong> {messaggio_pec.oggetto}</p>
                    </div>
                    <div class="message-content">
                        {contenuto_messaggio if is_contenuto_html else f"<pre>{contenuto_messaggio}</pre>"}
                    </div>
                </div>
                <div class="footer">
                    <p>Questo messaggio è stato inoltrato automaticamente dal sistema SimplePEC.</p>
                    <p>Per visualizzare il messaggio originale, accedi alla piattaforma SimplePEC.</p>
                </div>
            </body>
            </html>
            """

            # Crea il corpo dell'email in formato testo
            text_content = f"""
            Inoltro Messaggio PEC

            Da: {mittente_info}
            A: {messaggio_pec.account_pec.indirizzo_email}
            Data: {messaggio_pec.data_ricezione.strftime('%d/%m/%Y %H:%M')}
            Oggetto: {messaggio_pec.oggetto}

            {contenuto_messaggio}

            Questo messaggio è stato inoltrato automaticamente dal sistema SimplePEC.
            Per visualizzare il messaggio originale, accedi alla piattaforma SimplePEC.
            """

            # Verifica che l'indirizzo email del destinatario sia valido
            import re
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            email_destinatario = utente_destinatario.email.strip()

            if not re.match(email_regex, email_destinatario):
                logger.error(f"Indirizzo email non valido: {email_destinatario}")
                return False

            # Crea l'email
            logger.info(f"Creazione email da {settings.DEFAULT_FROM_EMAIL} a {email_destinatario}")
            email = EmailMultiAlternatives(
                oggetto,
                text_content,
                settings.DEFAULT_FROM_EMAIL,
                [email_destinatario]
            )

            # Aggiungi il contenuto HTML
            email.attach_alternative(html_content, "text/html")
            logger.info("Aggiunto contenuto HTML all'email")

            # Aggiungi gli allegati
            allegati = messaggio_pec.allegati.all()
            num_allegati = allegati.count()
            logger.info(f"Trovati {num_allegati} allegati da aggiungere all'email")

            allegati_aggiunti = 0

            # Se ci sono troppi allegati, potremmo dover saltare
            if num_allegati > 20:
                logger.warning(f"Troppi allegati ({num_allegati}), potrebbero esserci problemi di invio")

            for allegato in allegati:
                try:
                    # Verifica se l'allegato ha contenuto
                    if not allegato.contenuto:
                        logger.warning(f"Allegato {allegato.nome_file} ha contenuto vuoto, viene saltato")
                        continue

                    # Verifica nome file
                    if not allegato.nome_file:
                        nome_file = f"allegato_{allegato.id}"
                        logger.warning(f"Allegato senza nome, generato nome: {nome_file}")
                    else:
                        nome_file = allegato.nome_file

                    # Verifica tipo file
                    if not allegato.tipo_file:
                        tipo_file = "application/octet-stream"
                        logger.warning(f"Allegato senza tipo file, usato tipo default: {tipo_file}")
                    else:
                        tipo_file = allegato.tipo_file

                    # Log dimensione allegato
                    dimensione = len(allegato.contenuto)
                    logger.info(f"Allegando file: {nome_file}, tipo: {tipo_file}, dimensione: {dimensione} bytes")

                    # Il BinaryField è già in formato bytes, non serve convertirlo
                    email.attach(nome_file, allegato.contenuto, tipo_file)
                    allegati_aggiunti += 1

                except Exception as attach_error:
                    logger.error(f"Errore nell'allegare il file {allegato.nome_file}: {str(attach_error)}", exc_info=True)
                    # Continua con gli altri allegati anche se uno fallisce

            logger.info(f"Aggiunti {allegati_aggiunti} allegati su {num_allegati} totali")

            # Invia l'email
            try:
                logger.info("Tentativo di invio email...")
                num_sent = email.send()
                logger.info(f"email.send() ha restituito: {num_sent}")

                if num_sent > 0:
                    logger.info(f"Email inviata con successo: {num_sent} messaggi inviati")
                    # Aggiorna il messaggio PEC per indicare che è stato inoltrato
                    messaggio_pec.inoltrato_a = utente_destinatario
                    messaggio_pec.save()
                    logger.info(f"Messaggio PEC (ID: {messaggio_pec.id}) aggiornato con riferimento a utente inoltro")
                    return True
                else:
                    logger.error(f"Nessuna email inviata. email.send() ha restituito {num_sent}")
                    return False
            except Exception as send_error:
                logger.error(f"Errore nell'invio dell'email: {str(send_error)}", exc_info=True)
                return False

        except Exception as e:
            logger.error(f"Errore generale nella funzione invia_inoltro_pec: {str(e)}", exc_info=True)
            # Log più dettagliato per il debug
            logger.error(f"Dettagli messaggio: ID={messaggio_pec.id}, Oggetto={messaggio_pec.oggetto}")
            if utente_destinatario:
                logger.error(f"Destinatario: ID={utente_destinatario.id}, Email={utente_destinatario.email}")
            else:
                logger.error("Destinatario non specificato o non valido")
            return False


class SincronizzazionePEC:
    """Classe per la sincronizzazione dei messaggi PEC da server IMAP"""

    def __init__(self, account_pec_id):
        """Inizializza la classe con l'account PEC da sincronizzare"""
        self.account = AccountPEC.objects.get(id=account_pec_id)
        self.imap_conn = None

    def connetti(self):
        """Stabilisce una connessione al server IMAP"""
        try:
            # Connessione al server IMAP
            self.imap_conn = imaplib.IMAP4_SSL(self.account.server_imap, self.account.porta_imap)
            self.imap_conn.login(self.account.username, self.account.password)
            return True
        except Exception as e:
            # Log dell'errore
            print(f"Errore di connessione al server IMAP: {str(e)}")
            return False

    def sincronizza(self):
        """Esegue la sincronizzazione completa"""
        if not self.connetti():
            return False

        return self.recupera_nuovi_messaggi()

    def recupera_nuovi_messaggi(self, forza_tutti=False):
        """Recupera i nuovi messaggi dal server IMAP

        Args:
            forza_tutti: Se True, recupera tutti i messaggi nella casella indipendentemente dallo stato
        """
        if not self.imap_conn:
            return False

        try:
            # Seleziona la cartella INBOX
            self.imap_conn.select("INBOX")

            # Ottieni messaggi in base al criterio di ricerca
            if forza_tutti:
                # Cerca tutti i messaggi
                status, message_ids = self.imap_conn.search(None, "ALL")
                print(f"Cercando TUTTI i messaggi")
            else:
                # Cerca messaggi recenti (ultimi 7 giorni) o non letti
                data_7_giorni_fa = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%d-%b-%Y")
                search_criteria = f'(OR UNSEEN SINCE {data_7_giorni_fa})'
                status, message_ids = self.imap_conn.search(None, search_criteria)
                print(f"Cercando messaggi non letti o recenti dal {data_7_giorni_fa}")

            print(f"Status: {status}, Trovati: {len(message_ids[0].split()) if message_ids[0] else 0} messaggi")

            if status != "OK":
                print(f"Errore nel recupero dei messaggi: status {status}")
                return False

            # Processa i messaggi trovati
            messaggi_processati = 0
            messaggi_totali = len(message_ids[0].split()) if message_ids[0] else 0

            if messaggi_totali == 0:
                print("Nessun messaggio trovato da processare")
            else:
                print(f"Trovati {messaggi_totali} messaggi da processare")

            for message_id in message_ids[0].split():
                print(f"Processando messaggio ID: {message_id.decode() if isinstance(message_id, bytes) else message_id}")
                if self.processa_messaggio(message_id):
                    messaggi_processati += 1

            # Aggiorna il timestamp dell'ultima sincronizzazione
            self.account.ultima_sincronizzazione = timezone.now()
            self.account.save()

            # Chiude la connessione
            self.imap_conn.close()
            self.imap_conn.logout()

            print(f"Sincronizzazione completata: {messaggi_processati} messaggi processati su {messaggi_totali} trovati")
            return messaggi_processati
        except Exception as e:
            # Log dell'errore
            print(f"Errore nel recupero dei messaggi: {str(e)}")
            if self.imap_conn:
                try:
                    self.imap_conn.close()
                    self.imap_conn.logout()
                except:
                    pass
            return False

    def processa_messaggio(self, message_id):
        """Processa un singolo messaggio e lo salva nel database"""
        try:
            print(f"Recuperando messaggio ID: {message_id}")
            status, message_data = self.imap_conn.fetch(message_id, "(RFC822)")

            if status != "OK":
                print(f"Errore nel fetch del messaggio: status {status}")
                return False

            # Ottiene il messaggio completo
            raw_email = message_data[0][1]
            email_message = email.message_from_bytes(raw_email)

            # Estrae le informazioni principali
            mittente_email = email.utils.parseaddr(email_message.get("From"))[1]
            data_ricezione = email.utils.parsedate_to_datetime(email_message.get("Date"))
            oggetto = email_message.get("Subject", "")
            identificativo = email_message.get("Message-ID", "")

            print(f"Processando messaggio: '{oggetto}' da {mittente_email}, ID: {identificativo}")

            # Se non c'è un ID messaggio (caso raro), usiamo una combinazione di altri campi
            if not identificativo:
                identificativo = f"{mittente_email}-{oggetto}-{data_ricezione.isoformat()}"
                print(f"ID messaggio non trovato, generato ID alternativo: {identificativo}")

            # Controlla se il messaggio esiste già
            if MessaggioPEC.objects.filter(identificativo_messaggio=identificativo).exists():
                print(f"Il messaggio con ID {identificativo} esiste già, saltato")
                return False

            # Ottiene o crea il mittente
            nome_mittente = email.utils.parseaddr(email_message.get("From"))[0] or mittente_email
            print(f"Mittente: {nome_mittente} <{mittente_email}>")

            mittente, _ = Contatto.objects.get_or_create(
                email=mittente_email,
                defaults={"nome": nome_mittente}
            )

            # Variabili per tracciare se abbiamo trovato postacert.eml
            postacert_trovato = False
            postacert_contenuto = None

            # Inizializza il mittente_postacert con una stringa vuota invece di None
            # per assicurarsi che venga sempre salvato nel database
            mittente_postacert = ""

            # Estrae il contenuto
            contenuto = ""
            contenuto_html = None

            # Lista di parti per processare gli allegati dopo
            all_parts = []

            if email_message.is_multipart():
                print("Messaggio multipart, analisi delle parti...")
                # Primo passaggio: cerca specificamente postacert.eml
                for part in email_message.walk():
                    all_parts.append(part)  # Salva tutte le parti per processarle dopo

                    # Cerca postacert.eml come allegato
                    filename = part.get_filename()
                    content_disp = part.get('Content-Disposition', '')

                    # Debug di questa parte
                    print(f"Parte: Tipo={part.get_content_type()}, Filename={filename}, Disposition={content_disp}")

                    # Verifica il Content-Disposition per postacert.eml
                    is_postacert = False
                    if filename and "postacert.eml" in filename.lower():
                        is_postacert = True
                        print("Trovato postacert.eml nel filename")
                    elif "filename=\"postacert.eml\"" in content_disp.lower():
                        is_postacert = True
                        filename = "postacert.eml"  # Forza il nome file
                        print("Trovato postacert.eml nel Content-Disposition")
                    elif "filename=postacert.eml" in content_disp.lower():
                        is_postacert = True
                        filename = "postacert.eml"  # Forza il nome file
                        print("Trovato postacert.eml nel Content-Disposition (senza virgolette)")
                    elif part.get_content_type() == "message/rfc822" and not filename:
                        # Se è un messaggio email embedded senza nome, potrebbe essere postacert.eml
                        is_postacert = True
                        filename = "postacert.eml"  # Assegna un nome logico
                        print("Trovato probabile postacert.eml (message/rfc822 senza nome)")

                    if is_postacert:
                        postacert_trovato = True
                        print(f"Trovato postacert.eml come allegato! Disposition: {content_disp}")
                        try:
                            # Estrai il contenuto di postacert.eml
                            postacert_bytes = part.get_payload(decode=True)
                            if postacert_bytes:
                                # Salva il file completo come allegato per un uso futuro
                                postacert_contenuto = postacert_bytes

                                # Prova a interpretare il contenuto
                                inner_email = email.message_from_bytes(postacert_bytes)

                                # Estrai il mittente da postacert.eml
                                print("Tentativo di estrazione del mittente da postacert.eml...")

                                # Stampa tutti gli header per debug
                                print("Headers di postacert.eml:")
                                for header in inner_email.keys():
                                    print(f"  {header}: {inner_email[header]}")

                                from_header = inner_email.get('From', '')
                                print(f"From header trovato: '{from_header}'")

                                if from_header:
                                    try:
                                        from email.header import decode_header
                                        decoded_parts = decode_header(from_header)
                                        print(f"Decoded parts: {decoded_parts}")

                                        from_decoded = ' '.join([
                                            part.decode(encoding or 'utf-8', errors='replace')
                                            if isinstance(part, bytes) else part
                                            for part, encoding in decoded_parts
                                        ])
                                        mittente_postacert = from_decoded
                                        print(f"Mittente estratto da postacert.eml: '{mittente_postacert}'")
                                    except Exception as e:
                                        print(f"Errore nella decodifica del mittente: {e}")
                                        mittente_postacert = from_header
                                        print(f"Usando from_header non decodificato: '{mittente_postacert}'")
                                else:
                                    print("ATTENZIONE: Nessun header 'From' trovato in postacert.eml!")

                                # Assicuriamoci che il mittente sia stato estratto correttamente
                                if mittente_postacert:
                                    print(f"MITTENTE POSTACERT ESTRATTO: '{mittente_postacert}'")
                                else:
                                    print("ERRORE: mittente_postacert è vuoto o None!")

                                # Estrai il contenuto dal messaggio interno
                                if inner_email.is_multipart():
                                    for inner_part in inner_email.walk():
                                        if inner_part.get_content_maintype() == "text":
                                            content_type = inner_part.get_content_type()
                                            try:
                                                inner_content = inner_part.get_payload(decode=True)
                                                if inner_content:
                                                    inner_text = inner_content.decode(errors='replace')
                                                    if content_type == "text/html":
                                                        contenuto_html = inner_text
                                                        print("Trovato contenuto HTML in postacert.eml")
                                                        break
                                                    elif content_type == "text/plain" and not contenuto:
                                                        contenuto = inner_text
                                                        print("Trovato contenuto testuale in postacert.eml")
                                            except Exception as e:
                                                print(f"Errore nell'estrazione del contenuto da postacert.eml: {str(e)}")
                                else:
                                    try:
                                        payload = inner_email.get_payload(decode=True)
                                        if payload:
                                            content_type = inner_email.get_content_type()
                                            text = payload.decode(errors='replace')
                                            if content_type == "text/html":
                                                contenuto_html = text
                                            else:
                                                contenuto = text
                                            print(f"Estratto contenuto da postacert.eml non multipart")
                                    except Exception as e:
                                        print(f"Errore nell'estrazione del payload da postacert.eml: {str(e)}")
                        except Exception as e:
                            print(f"Errore nel processare postacert.eml: {str(e)}")
                            import traceback
                            traceback.print_exc()

                # Secondo passaggio: processa le parti normali del messaggio solo se non abbiamo trovato contenuto in postacert.eml
                if not contenuto_html and not contenuto:
                    for part in all_parts:
                        if part.get_content_maintype() == "text":
                            content_type = part.get_content_type()
                            try:
                                part_content = part.get_payload(decode=True)
                                if part_content:
                                    part_text = part_content.decode(errors='replace')
                                    if content_type == "text/html" and not contenuto_html:
                                        contenuto_html = part_text
                                        print("Trovato contenuto HTML nella busta")
                                    elif content_type == "text/plain" and not contenuto:
                                        contenuto = part_text
                                        print("Trovato contenuto testuale nella busta")
                            except Exception as decode_err:
                                print(f"Errore decodifica parte {content_type}: {str(decode_err)}")
            else:
                print("Messaggio semplice, estrazione contenuto...")
                try:
                    payload = email_message.get_payload(decode=True)
                    if payload:
                        contenuto = payload.decode(errors='replace')
                except Exception as decode_err:
                    print(f"Errore decodifica contenuto: {str(decode_err)}")

            # Usa il contenuto HTML se disponibile, altrimenti il testo
            contenuto_finale = contenuto_html if contenuto_html else contenuto
            if not contenuto_finale:
                contenuto_finale = "(Contenuto vuoto o non leggibile)"
                print("Nessun contenuto leggibile trovato")

            # Crea il messaggio nel database
            print(f"Creazione messaggio nel database: {oggetto}")
            print(f"Mittente postacert che verrà salvato: '{mittente_postacert}'")
            print(f"Tipo di mittente_postacert: {type(mittente_postacert)}")

            # Assicuriamoci che mittente_postacert non sia None
            if mittente_postacert is None:
                print("ATTENZIONE: mittente_postacert è None, impostato a stringa vuota")
                mittente_postacert = ""

            # Verifica se mittente_postacert è una stringa vuota
            if mittente_postacert == "":
                print("ATTENZIONE: mittente_postacert è una stringa vuota!")

            # Se non abbiamo trovato il mittente in postacert.eml, proviamo a estrarlo direttamente dal messaggio completo
            if not mittente_postacert:
                print("Tentativo di estrazione del mittente direttamente dal messaggio completo...")
                try:
                    # Verifica se il messaggio è una ricevuta
                    import re
                    is_ricevuta = False
                    if re.search(r'ACCETTAZIONE|POSTA CERTIFICATA: accettazione', oggetto, re.IGNORECASE):
                        is_ricevuta = True
                    elif re.search(r'CONSEGNA|POSTA CERTIFICATA: avvenuta consegna', oggetto, re.IGNORECASE):
                        is_ricevuta = True
                    elif re.search(r'MANCATA CONSEGNA|POSTA CERTIFICATA: mancata consegna', oggetto, re.IGNORECASE):
                        is_ricevuta = True
                    elif re.search(r'PRESA IN CARICO', oggetto, re.IGNORECASE):
                        is_ricevuta = True
                    elif re.search(r'ANOMALIA MESSAGGIO|ANOMALIA', oggetto, re.IGNORECASE):
                        is_ricevuta = True

                    # Per le ricevute, estrai info direttamente dal messaggio completo
                    if is_ricevuta:
                        from_header = email_message.get('From', '')
                        if from_header:
                            try:
                                from email.header import decode_header
                                decoded_parts = decode_header(from_header)
                                from_decoded = ' '.join([
                                    part.decode(encoding or 'utf-8', errors='replace')
                                    if isinstance(part, bytes) else part
                                    for part, encoding in decoded_parts
                                ])
                                mittente_postacert = from_decoded
                                print(f"Mittente estratto dal messaggio completo (ricevuta): '{mittente_postacert}'")
                            except Exception as e:
                                print(f"Errore nella decodifica del mittente dal messaggio completo: {e}")
                                mittente_postacert = from_header
                    else:
                        # Per messaggi normali, cerca postacert.eml con un approccio alternativo
                        raw_message = raw_email.decode('latin-1', errors='replace')

                        # Cerca "filename="postacert.eml"" nel messaggio
                        postacert_pos = raw_message.find('filename="postacert.eml"')
                        if postacert_pos == -1:
                            postacert_pos = raw_message.find('filename=postacert.eml')

                        if postacert_pos > 0:
                            print("Trovato riferimento a postacert.eml nel messaggio completo")
                            # Cerca il boundary precedente
                            boundary_start = raw_message.rfind('--', 0, postacert_pos)
                            if boundary_start > 0:
                                # Estrai il boundary
                                boundary_end = raw_message.find('\r\n', boundary_start)
                                if boundary_end > 0:
                                    boundary = raw_message[boundary_start:boundary_end].strip()

                                    # Trova l'inizio del contenuto (dopo i doppi CRLF)
                                    header_end = raw_message.find('\r\n\r\n', postacert_pos)
                                    if header_end > 0:
                                        # Trova il prossimo boundary (fine del contenuto)
                                        next_boundary = raw_message.find(boundary, header_end)
                                        if next_boundary > 0:
                                            # Estrai header e payload
                                            header = raw_message[boundary_start:header_end]
                                            payload_raw = raw_message[header_end+4:next_boundary-2]  # dopo \r\n\r\n, prima di \r\n--boundary

                                            # Determina l'encoding
                                            if 'Content-Transfer-Encoding: base64' in header:
                                                import base64
                                                payload = base64.b64decode(payload_raw)
                                            elif 'Content-Transfer-Encoding: quoted-printable' in header:
                                                import quopri
                                                payload = quopri.decodestring(payload_raw.encode('latin-1'))
                                            else:
                                                payload = payload_raw.encode('latin-1')

                                            # Abbiamo il payload di postacert.eml
                                            postacert_message = email.message_from_bytes(payload)

                                            # Estrai il mittente
                                            if postacert_message:
                                                from_header = postacert_message.get('From', '')
                                                if from_header:
                                                    try:
                                                        from email.header import decode_header
                                                        decoded_parts = decode_header(from_header)
                                                        from_decoded = ' '.join([
                                                            part.decode(encoding or 'utf-8', errors='replace')
                                                            if isinstance(part, bytes) else part
                                                            for part, encoding in decoded_parts
                                                        ])
                                                        mittente_postacert = from_decoded
                                                        print(f"Mittente estratto da postacert.eml (approccio alternativo): '{mittente_postacert}'")
                                                    except Exception as e:
                                                        print(f"Errore nella decodifica del mittente (approccio alternativo): {e}")
                                                        mittente_postacert = from_header

                        # Se non abbiamo ancora trovato il mittente, proviamo con l'approccio del parser email
                        if not mittente_postacert:
                            print("Tentativo con l'approccio del parser email...")
                            for part in email_message.walk():
                                filename = part.get_filename()
                                content_disp = part.get('Content-Disposition', '')

                                if ((filename and "postacert.eml" in filename.lower()) or
                                   ("filename=\"postacert.eml\"" in content_disp.lower()) or
                                   part.get_content_type() == "message/rfc822"):
                                    # Estratto postacert.eml
                                    postacert_content = part.get_payload(decode=True)
                                    if postacert_content:
                                        inner_message = email.message_from_bytes(postacert_content)
                                        from_header = inner_message.get('From', '')
                                        if from_header:
                                            try:
                                                from email.header import decode_header
                                                decoded_parts = decode_header(from_header)
                                                from_decoded = ' '.join([
                                                    part.decode(encoding or 'utf-8', errors='replace')
                                                    if isinstance(part, bytes) else part
                                                    for part, encoding in decoded_parts
                                                ])
                                                mittente_postacert = from_decoded
                                                print(f"Mittente estratto da postacert.eml (parser email): '{mittente_postacert}'")
                                                break
                                            except Exception as e:
                                                print(f"Errore nella decodifica del mittente (parser email): {e}")
                                                mittente_postacert = from_header
                                                break
                except Exception as e:
                    print(f"Errore nell'estrazione alternativa del mittente: {e}")
                    import traceback
                    traceback.print_exc()

            messaggio = MessaggioPEC.objects.create(
                account_pec=self.account,
                mittente=mittente,
                mittente_postacert=mittente_postacert,
                oggetto=oggetto,
                contenuto=contenuto_finale,
                data_ricezione=data_ricezione,
                messaggio_completo=raw_email,
                identificativo_messaggio=identificativo,
                stato="DA_LEGGERE"
            )

            # Verifica che il messaggio sia stato creato correttamente con il mittente_postacert
            print(f"Messaggio creato con ID: {messaggio.id}, mittente_postacert: '{messaggio.mittente_postacert}'")

            # Verifica aggiuntiva dal database
            try:
                messaggio_db = MessaggioPEC.objects.get(id=messaggio.id)
                print(f"Verifica dal DB - messaggio ID: {messaggio_db.id}, mittente_postacert: '{messaggio_db.mittente_postacert}'")
            except Exception as e:
                print(f"Errore nella verifica dal DB: {e}")

            # Aggiunge i destinatari
            print("Aggiunta destinatari...")
            for dest in email_message.get_all("To", []) + email_message.get_all("Cc", []):
                email_dest = email.utils.parseaddr(dest)[1]
                nome_dest = email.utils.parseaddr(dest)[0] or email_dest

                if email_dest:
                    print(f"Destinatario: {nome_dest} <{email_dest}>")
                    destinatario, _ = Contatto.objects.get_or_create(
                        email=email_dest,
                        defaults={"nome": nome_dest}
                    )
                    messaggio.destinatari.add(destinatario)

            # Processa gli allegati
            allegati_trovati = 0

            # Se abbiamo trovato postacert.eml, assicuriamoci di salvarlo come allegato
            if postacert_trovato and postacert_contenuto:
                allegati_trovati += 1
                print(f"Salvando postacert.eml come allegato")
                Allegato.objects.create(
                    messaggio=messaggio,
                    nome_file="postacert.eml",
                    tipo_file="message/rfc822",
                    dimensione=len(postacert_contenuto),
                    contenuto=postacert_contenuto
                )

            # Processa altri allegati
            if email_message.is_multipart():
                print("Ricerca allegati aggiuntivi...")
                for part in all_parts:
                    if part.get_content_maintype() == "multipart":
                        continue

                    # Verifica se la parte ha un nome file specificato direttamente
                    filename = part.get_filename()

                    # Se non ha un nome file ma è un allegato potenziale, cerca nel Content-Disposition o genera un nome
                    if not filename and part.get_content_type() not in ['text/plain', 'text/html']:
                        # Cerca nel Content-Disposition
                        content_disp = part.get('Content-Disposition', '')
                        import re
                        filename_match = re.search(r'filename="?([^";]*)"?', content_disp)
                        if filename_match:
                            filename = filename_match.group(1)
                        else:
                            # Genera nome basato sul content-type
                            ext = part.get_content_type().split('/')[1] if '/' in part.get_content_type() else 'bin'
                            filename = f"attachment_{allegati_trovati + 1}.{ext}"

                    # Se si tratta di un'applicazione o di un content-type che non è testo/HTML puro
                    is_body_content = part.get_content_type() in ['text/plain', 'text/html'] and not filename

                    # Salva gli allegati che hanno un nome file o che non sono testo/HTML
                    if filename or (part.get_content_maintype() != 'text' and part.get_content_maintype() != 'multipart'):
                        # Se l'allegato è postacert.eml e l'abbiamo già salvato, salta
                        if filename and "postacert.eml" in filename.lower() and postacert_trovato:
                            print(f"Saltando duplicato di postacert.eml")
                            continue

                        allegati_trovati += 1
                        print(f"Trovato allegato: {filename or 'Senza nome'}, tipo: {part.get_content_type()}")

                        # Estrae i dati dell'allegato
                        content_type = part.get_content_type()
                        payload = part.get_payload(decode=True)

                        # Se non c'è payload, salta questo allegato
                        if not payload:
                            print(f"Allegato senza contenuto, saltato: {filename}")
                            continue

                        # Usa un nome file predefinito se non presente
                        if not filename:
                            ext = content_type.split('/')[1] if '/' in content_type else 'bin'
                            filename = f"attachment_{allegati_trovati}.{ext}"

                        # Salva l'allegato
                        allegato = Allegato.objects.create(
                            messaggio=messaggio,
                            nome_file=filename,
                            tipo_file=content_type,
                            dimensione=len(payload),
                            contenuto=payload
                        )

            print(f"Messaggio '{oggetto}' processato con successo con {allegati_trovati} allegati")
            return True
        except Exception as e:
            # Log dell'errore
            print(f"Errore nella processazione del messaggio {message_id}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


class InvioPEC:
    """Classe per l'invio di messaggi PEC tramite SMTP"""

    def __init__(self, account_pec_id):
        """Inizializza la classe con l'account PEC da utilizzare per l'invio"""
        self.account = AccountPEC.objects.get(id=account_pec_id)
        self.smtp_conn = None

    def connetti(self):
        """Stabilisce una connessione al server SMTP"""
        try:
            self.smtp_conn = smtplib.SMTP_SSL(self.account.server_smtp, self.account.porta_smtp)
            self.smtp_conn.login(self.account.username, self.account.password)
            return True
        except Exception as e:
            # Log dell'errore
            print(f"Errore di connessione al server SMTP: {str(e)}")
            return False

    def invia_messaggio(self, destinatari, oggetto, contenuto, allegati=None, identificativo_messaggio=None):
        """
        Invia un messaggio PEC ai destinatari specificati

        Returns:
            tuple: (successo: bool, messaggio_completo: bytes, errore_dettaglio: str)
        """
        if not self.smtp_conn:
            if not self.connetti():
                errore = f"Impossibile connettersi al server SMTP: {self.account.server_smtp}:{self.account.porta_smtp}"
                print(errore)
                return False, None, errore

        try:
            # Crea il messaggio
            msg = MIMEMultipart()
            msg["From"] = self.account.indirizzo_email
            msg["To"] = ", ".join(destinatari)
            msg["Subject"] = oggetto
            msg["Date"] = email.utils.formatdate(localtime=True)
            # Usa l'identificativo fornito o ne genera uno nuovo
            msg["Message-ID"] = identificativo_messaggio if 'identificativo_messaggio' in locals() else email.utils.make_msgid(domain=self.account.indirizzo_email.split('@')[1])

            # Aggiunge il corpo del messaggio
            msg.attach(MIMEText(contenuto, "html"))

            # Aggiunge gli allegati se presenti
            if allegati:
                from email.mime.application import MIMEApplication
                from email.mime.base import MIMEBase

                for allegato in allegati:
                    nome_file = allegato.get('nome')
                    contenuto_file = allegato.get('contenuto')
                    tipo_file = allegato.get('tipo')

                    if not nome_file or not contenuto_file:
                        continue

                    # Determina il tipo MIME principale e sottotipo
                    main_type, sub_type = tipo_file.split('/', 1) if '/' in tipo_file else ('application', 'octet-stream')

                    # Crea la parte dell'allegato
                    if main_type == 'application':
                        part = MIMEApplication(contenuto_file, _subtype=sub_type)
                    else:
                        part = MIMEBase(main_type, sub_type)
                        part.set_payload(contenuto_file)
                        email.encoders.encode_base64(part)

                    # Aggiunge l'header con il nome del file
                    part.add_header('Content-Disposition', 'attachment', filename=nome_file)
                    msg.attach(part)

                    print(f"Allegato aggiunto: {nome_file} ({tipo_file})")

            # Invia il messaggio
            print(f"Invio messaggio a {destinatari} tramite {self.account.indirizzo_email}")
            self.smtp_conn.send_message(msg)
            self.smtp_conn.quit()
            print("Messaggio inviato con successo")

            return True, msg.as_bytes(), None
        except Exception as e:
            # Log dell'errore dettagliato
            import traceback
            errore_dettaglio = f"Errore nell'invio del messaggio: {str(e)}\n\nDettagli tecnici:\n{traceback.format_exc()}"
            print(errore_dettaglio)

            if self.smtp_conn:
                try:
                    self.smtp_conn.quit()
                except:
                    pass
            return False, None, errore_dettaglio