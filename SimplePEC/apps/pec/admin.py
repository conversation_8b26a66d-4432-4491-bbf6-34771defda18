from django.contrib import admin
from .models import AccountPEC, MessaggioPEC, Allegato, LogAccesso, LogAssociazione, UtenteAccountPEC

class AllegatoInline(admin.TabularInline):
    model = Allegato
    extra = 0
    fields = ('nome_file', 'tipo_file', 'dimensione')
    readonly_fields = ('nome_file', 'tipo_file', 'dimensione')
    can_delete = False

class UtenteAccountPECInline(admin.TabularInline):
    model = UtenteAccountPEC
    extra = 1
    fields = ('utente', 'puo_aggiornare_stato', 'puo_assegnare_cliente', 'puo_assegnare_referente')

class AccountPECAdmin(admin.ModelAdmin):
    list_display = ('nome', 'indirizzo_email', 'attivo', 'ultima_sincronizzazione')
    list_filter = ('attivo',)
    search_fields = ('nome', 'indirizzo_email')
    inlines = [UtenteAccountPECInline]
    fieldsets = (
        (None, {
            'fields': ('nome', 'indirizzo_email', 'attivo')
        }),
        ('Configurazione server', {
            'fields': ('server_imap', 'porta_imap', 'server_smtp', 'porta_smtp')
        }),
        ('Credenziali', {
            'fields': ('username', 'password')
        }),
        ('Organizzazione', {
            'fields': ('organizzazione',)
        }),
        ('Stato', {
            'fields': ('ultima_sincronizzazione',)
        }),
    )

class MessaggioPECAdmin(admin.ModelAdmin):
    list_display = ('oggetto', 'account_pec', 'mittente', 'mittente_postacert', 'data_ricezione', 'stato', 'cliente')
    list_filter = ('account_pec', 'stato', 'data_ricezione')
    search_fields = ('oggetto', 'mittente__nome', 'mittente__email', 'mittente_postacert')
    readonly_fields = ('account_pec', 'mittente', 'mittente_postacert', 'destinatari', 'oggetto', 'contenuto',
                      'data_ricezione', 'identificativo_messaggio')
    inlines = [AllegatoInline]
    fieldsets = (
        (None, {
            'fields': ('account_pec', 'mittente', 'mittente_postacert', 'destinatari', 'oggetto', 'data_ricezione')
        }),
        ('Contenuto', {
            'fields': ('contenuto',)
        }),
        ('Metadati', {
            'fields': ('identificativo_messaggio',)
        }),
        ('Gestione', {
            'fields': ('stato', 'cliente', 'referente')
        }),
    )

class LogAccessoAdmin(admin.ModelAdmin):
    list_display = ('utente', 'messaggio', 'data_accesso')
    list_filter = ('data_accesso', 'utente')
    search_fields = ('utente__username', 'messaggio__oggetto')
    readonly_fields = ('utente', 'messaggio', 'data_accesso')

class LogAssociazioneAdmin(admin.ModelAdmin):
    list_display = ('utente', 'messaggio', 'cliente', 'data_associazione')
    list_filter = ('data_associazione', 'utente', 'cliente')
    search_fields = ('utente__username', 'messaggio__oggetto', 'cliente__nome')
    readonly_fields = ('utente', 'messaggio', 'cliente', 'data_associazione')

class UtenteAccountPECAdmin(admin.ModelAdmin):
    list_display = ('utente', 'account', 'puo_aggiornare_stato', 'puo_assegnare_cliente', 'puo_assegnare_referente', 'data_assegnazione')
    list_filter = ('puo_aggiornare_stato', 'puo_assegnare_cliente', 'puo_assegnare_referente', 'data_assegnazione')
    search_fields = ('utente__username', 'utente__first_name', 'utente__last_name', 'account__nome', 'account__indirizzo_email')

admin.site.register(AccountPEC, AccountPECAdmin)
admin.site.register(MessaggioPEC, MessaggioPECAdmin)
admin.site.register(LogAccesso, LogAccessoAdmin)
admin.site.register(LogAssociazione, LogAssociazioneAdmin)
admin.site.register(UtenteAccountPEC, UtenteAccountPECAdmin)
