from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.accounts.models import UtentePersonalizzato, Organizzazione
from apps.clienti.models import Cliente, Referente, Contatto

class AccountPEC(models.Model):
    nome = models.CharField(max_length=100)
    indirizzo_email = models.EmailField(unique=True)
    server_imap = models.CharField(max_length=100)
    porta_imap = models.IntegerField()
    server_smtp = models.CharField(max_length=100)
    porta_smtp = models.IntegerField()
    username = models.CharField(max_length=100)
    password = models.CharField(max_length=100)  # In produzione usare crittografia appropriata
    attivo = models.BooleanField(default=True)
    ultima_sincronizzazione = models.DateTimeField(null=True, blank=True)
    organizzazione = models.ForeignKey(Organizzazione, on_delete=models.SET_NULL, null=True, blank=True, related_name='account_pec')
    utenti_autorizzati = models.ManyToManyField(UtentePersonalizzato, blank=True, related_name='account_pec_autorizzati')

    def __str__(self):
        return self.nome

    class Meta:
        verbose_name = "Account PEC"
        verbose_name_plural = "Account PEC"

class UtenteAccountPEC(models.Model):
    """Modello per la gestione dei permessi specifici tra utenti e account PEC"""
    utente = models.ForeignKey(UtentePersonalizzato, on_delete=models.CASCADE, related_name='autorizzazioni_account')
    account = models.ForeignKey(AccountPEC, on_delete=models.CASCADE, related_name='autorizzazioni_utente')
    puo_aggiornare_stato = models.BooleanField(default=True, help_text="L'utente può aggiornare lo stato dei messaggi")
    puo_assegnare_cliente = models.BooleanField(default=True, help_text="L'utente può assegnare clienti ai messaggi")
    puo_assegnare_referente = models.BooleanField(default=True, help_text="L'utente può assegnare referenti ai messaggi")
    data_assegnazione = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Autorizzazione Account PEC"
        verbose_name_plural = "Autorizzazioni Account PEC"
        unique_together = ('utente', 'account')

    def __str__(self):
        return f"{self.utente} - {self.account}"

class MessaggioPEC(models.Model):
    STATO_CHOICES = (
        ('DA_LEGGERE', 'Da leggere'),
        ('DA_ASSEGNARE', 'Da assegnare'),
        ('ASSEGNATO', 'Assegnato'),
        ('LAVORATO', 'Lavorato'),
        ('IN_INVIO', 'In invio'),
        ('INVIATO', 'Inviato'),
        ('ERRORE_INVIO', 'Errore invio'),
    )

    DIREZIONE_CHOICES = (
        ('ENTRATA', 'Entrata'),
        ('USCITA', 'Uscita'),
    )

    account_pec = models.ForeignKey(AccountPEC, on_delete=models.CASCADE)
    mittente = models.ForeignKey(Contatto, on_delete=models.SET_NULL, null=True, related_name='messaggi_inviati')
    mittente_postacert = models.CharField(max_length=500, blank=True, null=True, help_text="Mittente estratto da postacert.eml")
    destinatari = models.ManyToManyField(Contatto, related_name='messaggi_ricevuti')
    oggetto = models.CharField(max_length=500)
    contenuto = models.TextField()
    data_ricezione = models.DateTimeField()
    messaggio_completo = models.BinaryField()  # Memorizza il messaggio EML completo
    identificativo_messaggio = models.CharField(max_length=200, unique=True)
    stato = models.CharField(max_length=20, choices=STATO_CHOICES, default='DA_LEGGERE')
    direzione = models.CharField(max_length=10, choices=DIREZIONE_CHOICES, default='ENTRATA')
    cliente = models.ForeignKey(Cliente, on_delete=models.SET_NULL, null=True, blank=True)
    referente = models.ForeignKey(Referente, on_delete=models.SET_NULL, null=True, blank=True)
    inoltrato_a = models.ForeignKey(UtentePersonalizzato, on_delete=models.SET_NULL, null=True, blank=True, related_name='messaggi_inoltrati', help_text="Utente a cui è stato inoltrato il messaggio via email")

    # Campi per la gestione degli errori di invio
    errore_invio = models.TextField(blank=True, null=True, help_text="Dettagli dell'errore durante l'invio")
    data_ultimo_tentativo = models.DateTimeField(blank=True, null=True, help_text="Data e ora dell'ultimo tentativo di invio")
    numero_tentativi = models.PositiveIntegerField(default=0, help_text="Numero di tentativi di invio effettuati")

    def __str__(self):
        return self.oggetto

    class Meta:
        verbose_name = "Messaggio PEC"
        verbose_name_plural = "Messaggi PEC"

class Allegato(models.Model):
    messaggio = models.ForeignKey(MessaggioPEC, on_delete=models.CASCADE, related_name='allegati')
    nome_file = models.CharField(max_length=255)
    tipo_file = models.CharField(max_length=100)
    dimensione = models.IntegerField()  # in bytes
    contenuto = models.BinaryField()

    def __str__(self):
        return self.nome_file

    class Meta:
        verbose_name = "Allegato"
        verbose_name_plural = "Allegati"

class LogAccesso(models.Model):
    utente = models.ForeignKey(UtentePersonalizzato, on_delete=models.CASCADE)
    messaggio = models.ForeignKey(MessaggioPEC, on_delete=models.CASCADE)
    data_accesso = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.utente.username} - {self.messaggio.oggetto} - {self.data_accesso}"

    class Meta:
        verbose_name = "Log Accesso"
        verbose_name_plural = "Log Accessi"

class LogAssociazione(models.Model):
    utente = models.ForeignKey(UtentePersonalizzato, on_delete=models.CASCADE)
    messaggio = models.ForeignKey(MessaggioPEC, on_delete=models.CASCADE)
    cliente = models.ForeignKey(Cliente, on_delete=models.CASCADE)
    data_associazione = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.utente.username} - {self.messaggio.oggetto} - {self.cliente.nome}"

    class Meta:
        verbose_name = "Log Associazione"
        verbose_name_plural = "Log Associazioni"


class SchedulazioneSincronizzazione(models.Model):
    """Modello per la gestione delle schedulazioni di sincronizzazione degli account PEC"""
    FREQUENZA_CHOICES = (
        ('OGNI_5_MINUTI', 'Ogni 5 minuti'),
        ('OGNI_10_MINUTI', 'Ogni 10 minuti'),
        ('OGNI_15_MINUTI', 'Ogni 15 minuti'),
        ('OGNI_30_MINUTI', 'Ogni 30 minuti'),
        ('OGNI_ORA', 'Ogni ora'),
        ('OGNI_3_ORE', 'Ogni 3 ore'),
        ('OGNI_6_ORE', 'Ogni 6 ore'),
        ('OGNI_12_ORE', 'Ogni 12 ore'),
        ('GIORNALIERA', 'Giornaliera'),
        ('SETTIMANALE', 'Settimanale'),
    )

    TIPO_SINCRONIZZAZIONE_CHOICES = (
        ('RECENTI', 'Solo messaggi recenti'),
        ('COMPLETA', 'Sincronizzazione completa'),
    )

    account_pec = models.ForeignKey(AccountPEC, on_delete=models.CASCADE, related_name='schedulazioni')
    attiva = models.BooleanField(default=True, help_text=_("Se la schedulazione è attiva"))
    frequenza = models.CharField(max_length=20, choices=FREQUENZA_CHOICES, default='GIORNALIERA')
    tipo_sincronizzazione = models.CharField(max_length=20, choices=TIPO_SINCRONIZZAZIONE_CHOICES, default='RECENTI')
    ora_inizio = models.TimeField(help_text=_("Ora di inizio della sincronizzazione (per frequenze giornaliere o settimanali)"), null=True, blank=True)
    giorno_settimana = models.IntegerField(choices=[(0, 'Lunedì'), (1, 'Martedì'), (2, 'Mercoledì'), (3, 'Giovedì'), (4, 'Venerdì'), (5, 'Sabato'), (6, 'Domenica')], null=True, blank=True, help_text=_("Giorno della settimana (solo per frequenza settimanale)"))
    creata_da = models.ForeignKey(UtentePersonalizzato, on_delete=models.SET_NULL, null=True, related_name='schedulazioni_create')
    data_creazione = models.DateTimeField(auto_now_add=True)
    ultima_modifica = models.DateTimeField(auto_now=True)
    ultima_esecuzione = models.DateTimeField(null=True, blank=True, help_text=_("Data e ora dell'ultima esecuzione della schedulazione"))
    task_id = models.CharField(max_length=50, blank=True, null=True, help_text=_("ID del task Celery associato"))

    def __str__(self):
        return f"Schedulazione {self.get_frequenza_display()} per {self.account_pec.nome}"

    class Meta:
        verbose_name = "Schedulazione Sincronizzazione"
        verbose_name_plural = "Schedulazioni Sincronizzazione"
        ordering = ['account_pec__nome', 'frequenza']
