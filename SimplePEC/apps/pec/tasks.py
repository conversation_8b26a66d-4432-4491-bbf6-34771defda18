from celery import shared_task
from django.utils import timezone
from django_celery_beat.models import PeriodicTask, CrontabSchedule, IntervalSchedule
import json
from datetime import timedel<PERSON>

from .models import AccountPEC, SchedulazioneSincronizzazione
from .services import SincronizzazionePEC

@shared_task
def sincronizza_account_pec(account_id):
    """Task per sincronizzare un singolo account PEC"""
    try:
        # Aggiorna l'ultima esecuzione per tutte le schedulazioni associate a questo account
        now = timezone.now()
        schedulazioni = SchedulazioneSincronizzazione.objects.filter(
            account_pec_id=account_id,
            attiva=True
        )
        for schedulazione in schedulazioni:
            schedulazione.ultima_esecuzione = now
            schedulazione.save(update_fields=['ultima_esecuzione'])

        # Esegui la sincronizzazione
        sync = SincronizzazionePEC(account_id)
        sync.connetti()
        return sync.recupera_nuovi_messaggi()
    except Exception as e:
        # Log dell'errore
        return f"Errore durante la sincronizzazione dell'account {account_id}: {str(e)}"

@shared_task
def sincronizza_tutti_account():
    """Task per sincronizzare tutti gli account PEC attivi"""
    accounts = AccountPEC.objects.filter(attivo=True)
    results = []
    for account in accounts:
        result = sincronizza_account_pec.delay(account.id)
        results.append(result.id)
    return results


@shared_task
def sincronizza_account_pec_completa(account_id):
    """Task per sincronizzare un singolo account PEC (sincronizzazione completa)"""
    try:
        # Aggiorna l'ultima esecuzione per tutte le schedulazioni associate a questo account
        now = timezone.now()
        schedulazioni = SchedulazioneSincronizzazione.objects.filter(
            account_pec_id=account_id,
            attiva=True,
            tipo_sincronizzazione='COMPLETA'
        )
        for schedulazione in schedulazioni:
            schedulazione.ultima_esecuzione = now
            schedulazione.save(update_fields=['ultima_esecuzione'])

        # Esegui la sincronizzazione
        sync = SincronizzazionePEC(account_id)
        sync.connetti()
        return sync.recupera_nuovi_messaggi(forza_tutti=True)
    except Exception as e:
        # Log dell'errore
        return f"Errore durante la sincronizzazione completa dell'account {account_id}: {str(e)}"


def crea_schedulazione_celery(schedulazione):
    """
    Crea un task periodico in Celery Beat in base alla schedulazione specificata

    Args:
        schedulazione: Istanza di SchedulazioneSincronizzazione

    Returns:
        task_id: ID del task Celery creato
    """
    # Determina quale funzione di sincronizzazione utilizzare
    task_name = 'apps.pec.tasks.sincronizza_account_pec'
    if schedulazione.tipo_sincronizzazione == 'COMPLETA':
        task_name = 'apps.pec.tasks.sincronizza_account_pec_completa'

    # Crea un nome univoco per il task
    task_name_unique = f'sync-{schedulazione.account_pec.id}-{schedulazione.id}'

    # Prepara gli argomenti per il task
    kwargs = {
        'name': f'Sincronizzazione {schedulazione.get_frequenza_display()} per {schedulazione.account_pec.nome}',
        'task': task_name,
        'args': json.dumps([schedulazione.account_pec.id]),
        'enabled': schedulazione.attiva,
        'description': f'Schedulazione {schedulazione.id} - {schedulazione.get_frequenza_display()} - {schedulazione.get_tipo_sincronizzazione_display()}'
    }

    # Gestisci la frequenza
    if schedulazione.frequenza == 'OGNI_5_MINUTI':
        # Crea o ottieni un intervallo di 5 minuti
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=5,
            period=IntervalSchedule.MINUTES
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_10_MINUTI':
        # Crea o ottieni un intervallo di 10 minuti
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=10,
            period=IntervalSchedule.MINUTES
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_15_MINUTI':
        # Crea o ottieni un intervallo di 15 minuti
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=15,
            period=IntervalSchedule.MINUTES
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_30_MINUTI':
        # Crea o ottieni un intervallo di 30 minuti
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=30,
            period=IntervalSchedule.MINUTES
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_ORA':
        # Crea o ottieni un intervallo orario
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=1,
            period=IntervalSchedule.HOURS
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_3_ORE':
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=3,
            period=IntervalSchedule.HOURS
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_6_ORE':
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=6,
            period=IntervalSchedule.HOURS
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'OGNI_12_ORE':
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=12,
            period=IntervalSchedule.HOURS
        )
        kwargs['interval'] = schedule

    elif schedulazione.frequenza == 'GIORNALIERA':
        # Per la frequenza giornaliera, usa un crontab
        ora = schedulazione.ora_inizio or timezone.now().time()
        schedule, _ = CrontabSchedule.objects.get_or_create(
            minute=ora.minute,
            hour=ora.hour,
            day_of_week='*',
            day_of_month='*',
            month_of_year='*'
        )
        kwargs['crontab'] = schedule

    elif schedulazione.frequenza == 'SETTIMANALE':
        # Per la frequenza settimanale, usa un crontab con il giorno specifico
        ora = schedulazione.ora_inizio or timezone.now().time()
        giorno = schedulazione.giorno_settimana if schedulazione.giorno_settimana is not None else 0  # Default: lunedì

        schedule, _ = CrontabSchedule.objects.get_or_create(
            minute=ora.minute,
            hour=ora.hour,
            day_of_week=str(giorno),
            day_of_month='*',
            month_of_year='*'
        )
        kwargs['crontab'] = schedule

    # Crea o aggiorna il task periodico
    task, created = PeriodicTask.objects.update_or_create(
        name=task_name_unique,
        defaults=kwargs
    )

    return task.id


def elimina_schedulazione_celery(task_id):
    """
    Elimina un task periodico da Celery Beat

    Args:
        task_id: ID del task Celery da eliminare
    """
    try:
        task = PeriodicTask.objects.get(id=task_id)
        task.delete()
        return True
    except PeriodicTask.DoesNotExist:
        return False


@shared_task
def aggiorna_schedulazioni():
    """
    Task per aggiornare tutte le schedulazioni attive
    Questo task dovrebbe essere eseguito periodicamente per assicurarsi che tutte le schedulazioni siano sincronizzate con Celery Beat
    """
    schedulazioni = SchedulazioneSincronizzazione.objects.filter(attiva=True)
    results = []

    for schedulazione in schedulazioni:
        # Se la schedulazione ha già un task_id, aggiornalo
        if schedulazione.task_id:
            # Elimina il task esistente
            elimina_schedulazione_celery(schedulazione.task_id)

        # Crea un nuovo task
        task_id = crea_schedulazione_celery(schedulazione)

        # Aggiorna il task_id nella schedulazione
        schedulazione.task_id = task_id
        schedulazione.save(update_fields=['task_id'])

        results.append({
            'schedulazione_id': schedulazione.id,
            'task_id': task_id
        })

    return results