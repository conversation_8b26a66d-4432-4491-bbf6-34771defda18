from django.shortcuts import redirect
from django.urls import reverse, resolve
from django.contrib import messages

class PasswordChangeMiddleware:
    """Middleware che verifica se l'utente deve cambiare la password al primo accesso"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Verifica se l'utente è autenticato
        if request.user.is_authenticated:
            # Verifica se l'utente deve cambiare la password
            if request.user.richiedi_cambio_password:
                # Ottieni il nome della vista corrente
                current_url = request.path_info
                resolver_match = resolve(current_url)
                current_view_name = resolver_match.url_name
                
                # Lista di URL consentiti anche se l'utente deve cambiare la password
                allowed_urls = ['password_change', 'logout']
                
                # Se l'utente sta cercando di accedere a una pagina non consentita, reindirizzalo alla pagina di cambio password
                if current_view_name not in allowed_urls:
                    messages.warning(request, 'È necessario cambiare la password per continuare a utilizzare il sistema.')
                    return redirect('password_change')
        
        # Procedi con la richiesta normalmente
        response = self.get_response(request)
        return response