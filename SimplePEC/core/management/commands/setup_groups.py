from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from apps.accounts.models import UtentePersonalizzato
from apps.pec.models import AccountPEC, MessaggioPEC
from apps.clienti.models import Cliente, Referente
from core.permissions import MANAGER_GROUP, STAFF_GROUP

class Command(BaseCommand):
    help = 'Crea i gruppi Manager e Staff con i relativi permessi'

    def handle(self, *args, **options):
        # Crea i gruppi
        manager_group, created_manager = Group.objects.get_or_create(name=MANAGER_GROUP)
        if created_manager:
            self.stdout.write(self.style.SUCCESS(f'Gruppo {MANAGER_GROUP} creato con successo'))
        else:
            self.stdout.write(self.style.WARNING(f'Il gruppo {MANAGER_GROUP} esiste già'))
            
        staff_group, created_staff = Group.objects.get_or_create(name=STAFF_GROUP)
        if created_staff:
            self.stdout.write(self.style.SUCCESS(f'Gruppo {STAFF_GROUP} creato con successo'))
        else:
            self.stdout.write(self.style.WARNING(f'Il gruppo {STAFF_GROUP} esiste già'))
        
        # Ottieni i content type per i modelli
        account_pec_ct = ContentType.objects.get_for_model(AccountPEC)
        messaggio_pec_ct = ContentType.objects.get_for_model(MessaggioPEC)
        cliente_ct = ContentType.objects.get_for_model(Cliente)
        referente_ct = ContentType.objects.get_for_model(Referente)
        utente_ct = ContentType.objects.get_for_model(UtentePersonalizzato)
        
        # Permessi per i Manager
        manager_permissions = [
            # Account PEC
            Permission.objects.get(content_type=account_pec_ct, codename='add_accountpec'),
            Permission.objects.get(content_type=account_pec_ct, codename='change_accountpec'),
            Permission.objects.get(content_type=account_pec_ct, codename='delete_accountpec'),
            Permission.objects.get(content_type=account_pec_ct, codename='view_accountpec'),
            
            # Messaggi PEC
            Permission.objects.get(content_type=messaggio_pec_ct, codename='view_messaggiopec'),
            Permission.objects.get(content_type=messaggio_pec_ct, codename='change_messaggiopec'),
            
            # Clienti
            Permission.objects.get(content_type=cliente_ct, codename='add_cliente'),
            Permission.objects.get(content_type=cliente_ct, codename='change_cliente'),
            Permission.objects.get(content_type=cliente_ct, codename='delete_cliente'),
            Permission.objects.get(content_type=cliente_ct, codename='view_cliente'),
            
            # Referenti
            Permission.objects.get(content_type=referente_ct, codename='add_referente'),
            Permission.objects.get(content_type=referente_ct, codename='change_referente'),
            Permission.objects.get(content_type=referente_ct, codename='delete_referente'),
            Permission.objects.get(content_type=referente_ct, codename='view_referente'),
            
            # Utenti
            Permission.objects.get(content_type=utente_ct, codename='add_utentepersonalizzato'),
            Permission.objects.get(content_type=utente_ct, codename='change_utentepersonalizzato'),
            Permission.objects.get(content_type=utente_ct, codename='delete_utentepersonalizzato'),
            Permission.objects.get(content_type=utente_ct, codename='view_utentepersonalizzato'),
        ]
        
        # Assegna i permessi al gruppo Manager
        manager_group.permissions.set(manager_permissions)
        self.stdout.write(self.style.SUCCESS(f'Permessi assegnati al gruppo {MANAGER_GROUP}'))
        
        # Permessi per gli Staff
        staff_permissions = [
            # Account PEC (solo visualizzazione)
            Permission.objects.get(content_type=account_pec_ct, codename='view_accountpec'),
            
            # Messaggi PEC (visualizzazione e modifica)
            Permission.objects.get(content_type=messaggio_pec_ct, codename='view_messaggiopec'),
            Permission.objects.get(content_type=messaggio_pec_ct, codename='change_messaggiopec'),
            
            # Clienti (solo visualizzazione e modifica)
            Permission.objects.get(content_type=cliente_ct, codename='view_cliente'),
            Permission.objects.get(content_type=cliente_ct, codename='change_cliente'),
            
            # Referenti (solo visualizzazione)
            Permission.objects.get(content_type=referente_ct, codename='view_referente'),
        ]
        
        # Assegna i permessi al gruppo Staff
        staff_group.permissions.set(staff_permissions)
        self.stdout.write(self.style.SUCCESS(f'Permessi assegnati al gruppo {STAFF_GROUP}'))
