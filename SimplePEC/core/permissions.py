from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect
from django.http import HttpResponseForbidden
from django.urls import reverse
from functools import wraps

# Nomi dei gruppi
MANAGER_GROUP = 'Manager'
STAFF_GROUP = 'Staff'

def create_user_groups():
    """
    Crea i gruppi Manager e Staff se non esistono già.
    """
    # Crea il gruppo Manager
    manager_group, created = Group.objects.get_or_create(name=MANAGER_GROUP)

    # Crea il gruppo Staff
    staff_group, created = Group.objects.get_or_create(name=STAFF_GROUP)

    return manager_group, staff_group

def is_manager(user):
    """
    Verifica se l'utente appartiene al gruppo Manager.
    """
    if not user.is_authenticated:
        return False
    return user.groups.filter(name=MANAGER_GROUP).exists()

def is_staff(user):
    """
    Verifica se l'utente appartiene al gruppo Staff.
    """
    if not user.is_authenticated:
        return False
    return user.groups.filter(name=STAFF_GROUP).exists()

def get_user_role(user):
    """
    Restituisce il ruolo dell'utente (Manager, Staff o None).
    """
    if is_manager(user):
        return MANAGER_GROUP
    elif is_staff(user):
        return STAFF_GROUP
    return None

def add_user_to_group(user, group_name):
    """
    Aggiunge un utente a un gruppo.
    """
    group = get_object_or_404(Group, name=group_name)
    user.groups.add(group)
    user.save()

def remove_user_from_group(user, group_name):
    """
    Rimuove un utente da un gruppo.
    """
    group = get_object_or_404(Group, name=group_name)
    user.groups.remove(group)
    user.save()

def set_user_as_manager(user):
    """
    Imposta l'utente come Manager.
    """
    # Rimuovi l'utente dal gruppo Staff se presente
    if is_staff(user):
        remove_user_from_group(user, STAFF_GROUP)

    # Aggiungi l'utente al gruppo Manager
    add_user_to_group(user, MANAGER_GROUP)

def set_user_as_staff(user):
    """
    Imposta l'utente come Staff.
    """
    # Rimuovi l'utente dal gruppo Manager se presente
    if is_manager(user):
        remove_user_from_group(user, MANAGER_GROUP)

    # Aggiungi l'utente al gruppo Staff
    add_user_to_group(user, STAFF_GROUP)


# Decoratori per i permessi
def manager_required(view_func):
    """
    Decoratore che verifica se l'utente è un Manager.
    Se non lo è, reindirizza alla pagina di login o mostra un errore di permesso.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        if not is_manager(request.user):
            return HttpResponseForbidden("Non hai i permessi per accedere a questa pagina.")
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def staff_or_manager_required(view_func):
    """
    Decoratore che verifica se l'utente è uno Staff o un Manager.
    Se non lo è, reindirizza alla pagina di login o mostra un errore di permesso.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        if not (is_staff(request.user) or is_manager(request.user)):
            return HttpResponseForbidden("Non hai i permessi per accedere a questa pagina.")
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def can_manage_account(view_func):
    """
    Decoratore che verifica se l'utente può gestire un account PEC.
    Solo i Manager possono gestire gli account.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        if not is_manager(request.user):
            return HttpResponseForbidden("Solo i Manager possono gestire gli account PEC.")
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def can_manage_client(view_func):
    """
    Decoratore che verifica se l'utente può gestire un cliente.
    I Manager possono gestire tutti i clienti, gli Staff solo quelli assegnati.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')

        # I Manager possono gestire tutti i clienti
        if is_manager(request.user):
            return view_func(request, *args, **kwargs)

        # Gli Staff possono gestire solo i clienti assegnati
        if is_staff(request.user) and 'pk' in kwargs:
            from apps.clienti.models import Cliente
            try:
                cliente = Cliente.objects.get(pk=kwargs['pk'])
                if cliente.utenti_assegnati.filter(id=request.user.id).exists():
                    return view_func(request, *args, **kwargs)
            except Cliente.DoesNotExist:
                pass

        return HttpResponseForbidden("Non hai i permessi per gestire questo cliente.")
    return _wrapped_view

def can_manage_users(view_func):
    """
    Decoratore che verifica se l'utente può gestire altri utenti.
    Solo i Manager possono gestire gli utenti.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        if not is_manager(request.user):
            return HttpResponseForbidden("Solo i Manager possono gestire gli utenti.")
        return view_func(request, *args, **kwargs)
    return _wrapped_view
