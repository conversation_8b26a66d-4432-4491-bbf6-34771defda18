#!/bin/bash

# Termina eventuali processi precedenti
echo "Killing any existing servers..."
pkill -f "python manage.py runserver" || true
pkill -f "celery" || true
sleep 1

# Imposta il percorso del progetto
PROJECT_PATH="/home/<USER>/projects/SimplePEC"
DJANGO_PATH="$PROJECT_PATH/SimplePEC"
LOG_DIR="$DJANGO_PATH/logs"

# Crea la directory dei log se non esiste
mkdir -p $LOG_DIR

# Attiva l'ambiente virtuale
cd $DJANGO_PATH
source venv/bin/activate

# Verifica che Redis sia in esecuzione
echo "Checking Redis server..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "Starting Redis server..."
    redis-server --daemonize yes
    sleep 2
    if ! redis-cli ping > /dev/null 2>&1; then
        echo "❌ Failed to start Redis server. Please install and start it manually."
        exit 1
    fi
fi
echo "✅ Redis server is running"

# Avvia Celery worker
echo "Starting Celery worker..."
PYTHONPATH=$DJANGO_PATH celery -A config worker -l info > $LOG_DIR/celery_worker.log 2>&1 &
CELERY_WORKER_PID=$!
echo "✅ Celery worker started with PID: $CELERY_WORKER_PID"

# Avvia Celery beat
echo "Starting Celery beat..."
PYTHONPATH=$DJANGO_PATH celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler > $LOG_DIR/celery_beat.log 2>&1 &
CELERY_BEAT_PID=$!
echo "✅ Celery beat started with PID: $CELERY_BEAT_PID"

# Avvia il server Django
echo "Starting Django server..."
python manage.py runserver 0.0.0.0:8000 > $LOG_DIR/django_server.log 2>&1 &
DJANGO_PID=$!
echo "✅ Django server started with PID: $DJANGO_PID"

echo -e "\n✅ All services are running!"
echo "- Django: http://localhost:8000"
echo "- Celery worker and beat are running in background"
echo -e "\nLogs are being saved to:"
echo "- Django: $LOG_DIR/django_server.log"
echo "- Celery worker: $LOG_DIR/celery_worker.log"
echo "- Celery beat: $LOG_DIR/celery_beat.log"
echo -e "\nPress Ctrl+C to stop all services\n"

# Funzione per terminare tutti i processi
cleanup() {
    echo "Stopping all services..."
    kill $DJANGO_PID $CELERY_WORKER_PID $CELERY_BEAT_PID 2>/dev/null
    echo "All services stopped"
    exit
}

# Registra la funzione di cleanup per l'interruzione
trap cleanup INT TERM

# Attendi che l'utente prema Ctrl+C
wait $DJANGO_PID