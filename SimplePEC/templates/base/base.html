<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SimplePEC{% endblock %}</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            /* Colori principali */
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --primary-light: #eef2ff;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;

            /* Dimensioni layout */
            --sidebar-width: 280px;
            --header-height: 64px;

            /* Colori tema chiaro (default) */
            --body-bg: #f9fafb;
            --card-bg: #ffffff;
            --sidebar-bg: #ffffff;
            --header-bg: #ffffff;
            --text-primary: #111827;
            --text-secondary: #4b5563;
            --text-muted: #6b7280;
            --text-light: #9ca3af;
            --border-color: rgba(0, 0, 0, 0.08);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --nav-icon-bg: #ffffff;

            /* Border radius */
            --border-radius-sm: 0.25rem;
            --border-radius: 0.375rem;
            --border-radius-lg: 0.5rem;
            --border-radius-xl: 0.75rem;
            --border-radius-2xl: 1rem;
            --border-radius-full: 9999px;
        }

        /* Tema scuro */
        .dark-mode {
            --body-bg: #111827;
            --card-bg: #1f2937;
            --sidebar-bg: #1f2937;
            --header-bg: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #e5e7eb;
            --text-muted: #9ca3af;
            --text-light: #6b7280;
            --border-color: rgba(255, 255, 255, 0.08);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.25);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.26);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.25);
            --nav-icon-bg: #374151;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--body-bg);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* ===== HEADER STYLES ===== */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background-color: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
            padding: 0 24px;
            display: flex;
            align-items: center;
            z-index: 50;
            box-shadow: var(--shadow-sm);
        }

        .header-left {
            display: flex;
            align-items: center;
            width: var(--sidebar-width);
        }

        .logo {
            font-weight: 700;
            font-size: 20px;
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logo-icon {
            font-size: 24px;
            margin-right: 10px;
        }

        .logo-text {
            font-weight: 800;
            letter-spacing: -0.5px;
        }

        .sidebar-toggle {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            border: none;
            background-color: transparent;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-right: 16px;
            transition: all 0.2s;
        }

        .sidebar-toggle:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .header-center {
            flex-grow: 1;
            display: flex;
            align-items: center;
        }

        .header-nav {
            display: flex;
            align-items: center;
        }

        .header-nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            margin-right: 8px;
            transition: all 0.2s;
            font-weight: 500;
        }

        .header-nav-item i {
            margin-right: 8px;
        }

        .header-nav-item:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .header-nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Quick Search Styles */
        .quick-search-container {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }

        .search-btn {
            background: transparent;
            border: none;
            color: var(--text-muted);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .search-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        /* Search Overlay */
        .search-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: var(--header-height);
            background-color: var(--header-bg);
            z-index: 200;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .search-overlay.active {
            transform: translateY(0);
        }

        .search-overlay-content {
            width: 100%;
            max-width: 600px;
            padding: 0 20px;
        }

        .search-form-container {
            position: relative;
            width: 100%;
        }

        .search-input {
            width: 100%;
            height: 44px;
            border: 1px solid var(--border-color);
            border-radius: 22px;
            padding: 0 80px 0 20px;
            outline: none;
            background-color: var(--body-bg);
            transition: all 0.2s;
            font-size: 16px;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .search-submit-btn {
            position: absolute;
            right: 44px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: var(--primary-color);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .search-close-btn {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: var(--text-muted);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .search-submit-btn:hover,
        .search-close-btn:hover {
            background-color: var(--primary-light);
        }

        .header-action-btn {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            border: none;
            background-color: transparent;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-left: 8px;
            transition: all 0.2s;
        }

        .header-action-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .notification-btn {
            position: relative;
        }

        .notification-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--danger-color);
            border: 2px solid var(--header-bg);
        }

        .notification-dropdown {
            width: 320px;
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            text-decoration: none;
            color: var(--text-primary);
            padding: 10px;
            border-radius: var(--border-radius);
            transition: all 0.2s;
            display: block;
        }

        .notification-item:hover {
            background-color: var(--primary-light);
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-title {
            font-weight: 600;
            font-size: 14px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            padding: 6px 8px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.2s;
        }

        .user-menu:hover {
            background-color: var(--primary-light);
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 12px;
            overflow: hidden;
        }

        .avatar-md {
            width: 48px;
            height: 48px;
        }

        .user-info {
            margin-right: 8px;
        }

        .user-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }

        .user-role {
            font-size: 12px;
            color: var(--text-muted);
        }

        .dropdown-user-details {
            background-color: var(--primary-light);
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--sidebar-bg);
            box-shadow: var(--shadow-sm);
            z-index: 100;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
        }

        .sidebar-collapsed .sidebar {
            transform: translateX(-100%);
        }

        .sidebar-header {
            height: var(--header-height);
            padding: 0 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .app-brand {
            display: flex;
            align-items: center;
        }

        .app-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--primary-color);
            font-weight: 700;
            font-size: 20px;
        }

        .app-logo-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        /* Stili per l'organizzazione nella sidebar */
        .sidebar-organization-info {
            display: flex;
            align-items: center;
        }

        .sidebar-organization-logo {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-organization-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .organization-logo-placeholder {
            width: 100%;
            height: 100%;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .sidebar-organization-details {
            flex: 1;
        }

        .sidebar-organization-name {
            font-weight: 600;
            font-size: 16px;
            color: var(--text-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Box vuoto per mantenere lo spazio quando non c'è organizzazione */
        .sidebar-empty-box {
            height: 72px; /* Stessa altezza del box con info organizzazione */
        }

        .sidebar-user {
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-user-info {
            display: flex;
            align-items: center;
        }

        .sidebar-user-avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 16px;
            overflow: hidden;
        }

        .sidebar-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .sidebar-user-initials {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
        }

        .sidebar-user-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .sidebar-user-status {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--text-muted);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: var(--border-radius-full);
            margin-right: 6px;
        }

        .status-indicator.online {
            background-color: var(--success-color);
        }

        .sidebar-user-organization {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--text-secondary);
            padding-top: 6px;
            border-top: 1px solid var(--border-color);
        }

        .org-logo {
            margin-right: 6px;
        }

        .org-name {
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-menu-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px 0;
        }

        .sidebar-heading {
            padding: 16px 24px 8px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: var(--text-muted);
            margin: 0;
        }

        .sidebar-nav {
            padding: 0 12px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 10px 12px;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            margin-bottom: 4px;
            transition: all 0.2s;
            position: relative;
        }

        .nav-link:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .nav-link.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 500;
        }

        .nav-link-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius);
            background-color: var(--nav-icon-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s;
        }

        .nav-link.active .nav-link-icon {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-link-text {
            flex: 1;
        }

        .nav-link-badge {
            font-size: 10px;
            padding: 2px 8px;
        }

        .sidebar-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color);
        }

        .sidebar-footer-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .app-version {
            font-size: 12px;
            color: var(--text-muted);
        }

        .sidebar-actions {
            display: flex;
            align-items: center;
        }

        .sidebar-action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-full);
            border: none;
            background-color: transparent;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-left: 8px;
            transition: all 0.2s;
        }

        .sidebar-action-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        /* ===== MAIN CONTENT AREA ===== */
        .main-content {
            margin-left: var(--sidebar-width);
            padding-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
            transition: margin-left 0.3s;
        }

        .sidebar-collapsed .main-content {
            margin-left: 0;
        }

        .container-fluid {
            padding: 24px;
        }

        /* ===== CARD STYLES ===== */
        .card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            background-color: transparent;
            border-bottom: 1px solid var(--border-color);
            padding: 16px 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* ===== RESPONSIVE STYLES ===== */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar-expanded .sidebar {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-expanded .main-content {
                margin-left: 0;
            }

            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 90;
            }

            .sidebar-expanded .sidebar-overlay {
                display: block;
            }

            .header-left {
                width: auto;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="{% if request.session.sidebar_collapsed %}sidebar-collapsed{% else %}sidebar-expanded{% endif %} {% if request.session.dark_mode %}dark-mode{% endif %}">
    <!-- Header -->
    {% include 'includes/header.html' %}

    <!-- Sidebar -->
    {% include 'includes/sidebar.html' %}

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="container-fluid">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Indicatore di modalità demo rimosso -->

    <!-- Sidebar Overlay (for mobile) -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Modal Impostazioni -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">Impostazioni</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="mb-3">Aspetto</h6>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="themeMode" id="lightMode" value="light" {% if not request.session.dark_mode %}checked{% endif %}>
                                <label class="form-check-label" for="lightMode">
                                    <i class="fas fa-sun me-2"></i> Tema chiaro
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="themeMode" id="darkMode" value="dark" {% if request.session.dark_mode %}checked{% endif %}>
                                <label class="form-check-label" for="darkMode">
                                    <i class="fas fa-moon me-2"></i> Tema scuro
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h6 class="mb-3">Layout</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sidebarCollapsed" {% if request.session.sidebar_collapsed %}checked{% endif %}>
                            <label class="form-check-label" for="sidebarCollapsed">Sidebar collassata</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- HTMX for dynamic content loading -->
    <script src="https://unpkg.com/htmx.org@1.9.4"></script>
    <!-- Main Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar toggle functionality
            const toggleButtons = document.querySelectorAll('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const isCollapsed = document.body.classList.contains('sidebar-collapsed');

                    if (isCollapsed) {
                        // Se è già collassata, espandila
                        document.body.classList.remove('sidebar-collapsed');
                        document.body.classList.add('sidebar-expanded');
                        saveSidebarPreference(false);
                    } else {
                        // Se è espansa, collassala
                        document.body.classList.add('sidebar-collapsed');
                        document.body.classList.remove('sidebar-expanded');
                        saveSidebarPreference(true);
                    }
                });
            });

            // Close sidebar on overlay click (mobile)
            if (overlay) {
                overlay.addEventListener('click', function() {
                    document.body.classList.remove('sidebar-expanded');
                });
            }

            // Quick Search functionality
            const searchBtn = document.getElementById('searchBtn');
            const searchOverlay = document.getElementById('searchOverlay');
            const searchCloseBtn = document.getElementById('searchCloseBtn');
            const quickSearchInput = document.getElementById('quickSearchInput');

            if (searchBtn && searchOverlay && quickSearchInput) {
                // Open search overlay
                searchBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    searchOverlay.classList.add('active');
                    setTimeout(() => {
                        quickSearchInput.focus();
                    }, 300); // Delay focus until animation completes
                });

                // Close search overlay with close button
                if (searchCloseBtn) {
                    searchCloseBtn.addEventListener('click', function() {
                        searchOverlay.classList.remove('active');
                    });
                }

                // Close search on escape key
                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Escape' && searchOverlay.classList.contains('active')) {
                        searchOverlay.classList.remove('active');
                    }
                });
            }

            // Settings modal functionality
            const settingsBtn = document.querySelector('.sidebar-action-btn[title="Impostazioni"]');
            const settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));

            if (settingsBtn) {
                settingsBtn.addEventListener('click', function() {
                    settingsModal.show();
                });
            }

            // Theme toggle functionality
            const lightModeRadio = document.getElementById('lightMode');
            const darkModeRadio = document.getElementById('darkMode');

            if (lightModeRadio && darkModeRadio) {
                // Assicuriamoci che lo stato iniziale dei radio button corrisponda alla classe del body
                const isDarkMode = document.body.classList.contains('dark-mode');
                lightModeRadio.checked = !isDarkMode;
                darkModeRadio.checked = isDarkMode;

                lightModeRadio.addEventListener('change', function() {
                    if (this.checked) {
                        document.body.classList.remove('dark-mode');
                        saveThemePreference(false);
                    }
                });

                darkModeRadio.addEventListener('change', function() {
                    if (this.checked) {
                        document.body.classList.add('dark-mode');
                        saveThemePreference(true);
                    }
                });
            }

            // Sidebar collapsed toggle in settings
            const sidebarCollapsedCheckbox = document.getElementById('sidebarCollapsed');

            if (sidebarCollapsedCheckbox) {
                // Assicuriamoci che lo stato iniziale del checkbox corrisponda alla classe del body
                sidebarCollapsedCheckbox.checked = document.body.classList.contains('sidebar-collapsed');

                sidebarCollapsedCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        document.body.classList.add('sidebar-collapsed');
                        document.body.classList.remove('sidebar-expanded');
                        saveSidebarPreference(true);
                    } else {
                        document.body.classList.remove('sidebar-collapsed');
                        document.body.classList.add('sidebar-expanded');
                        saveSidebarPreference(false);
                    }
                });
            }

            // Function to save theme preference
            function saveThemePreference(darkMode) {
                fetch('/api/preferences/theme/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({ dark_mode: darkMode })
                });
            }

            // Function to save sidebar preference
            function saveSidebarPreference(collapsed) {
                fetch('/api/preferences/sidebar/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({ collapsed: collapsed })
                });
            }

            // CSRF token for AJAX requests
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>