{% extends 'base/base.html' %}

{% block title %}Accedi - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #f5f7fb;
    }
    
    .auth-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .auth-card {
        width: 100%;
        max-width: 400px;
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
        padding: 30px;
    }
    
    .auth-logo {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .auth-logo h1 {
        font-size: 28px;
        font-weight: 700;
        color: #3b82f6;
        margin: 0;
    }
    
    .auth-logo p {
        color: #6b7280;
        margin-top: 5px;
    }
    
    .form-control {
        padding: 12px 16px;
        border-radius: 8px;
        height: auto;
        font-size: 15px;
    }
    
    .btn-primary {
        padding: 12px 16px;
        border-radius: 8px;
        background-color: #3b82f6;
        border-color: #3b82f6;
        font-weight: 500;
    }
    
    .btn-primary:hover {
        background-color: #2563eb;
        border-color: #2563eb;
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 8px;
    }
    
    .form-demo-note {
        padding: 12px;
        background-color: #fef3c7;
        border-radius: 8px;
        font-size: 14px;
        color: #92400e;
        margin-top: 25px;
    }
    
    .form-demo-note code {
        background-color: rgba(146, 64, 14, 0.1);
        padding: 2px 4px;
        border-radius: 4px;
        font-family: monospace;
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-logo">
            <h1>SimplePEC</h1>
            <p>Accedi al tuo account</p>
        </div>
        
        {% if form.errors %}
        <div class="alert alert-danger" role="alert">
            Username o password non validi. Riprova.
        </div>
        {% endif %}
        
        <form method="post" action="{% url 'login' %}">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_username" class="form-label">Username</label>
                <input type="text" name="username" id="id_username" class="form-control" required autofocus>
            </div>
            
            <div class="mb-3">
                <label for="id_password" class="form-label">Password</label>
                <input type="password" name="password" id="id_password" class="form-control" required>
            </div>
            
            <div class="mb-3 form-check">
                <input type="checkbox" name="remember" id="id_remember" class="form-check-input">
                <label for="id_remember" class="form-check-label">Ricordami</label>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Accedi</button>
            </div>
            
            <!-- Rimosso il box informativo per le credenziali demo -->
            
            <input type="hidden" name="next" value="{{ next }}">
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Remove header and sidebar for login page
    document.addEventListener('DOMContentLoaded', function() {
        const header = document.querySelector('.header');
        const sidebar = document.querySelector('.sidebar');
        
        if (header) header.style.display = 'none';
        if (sidebar) sidebar.style.display = 'none';
        
        // Adjust main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.style.margin = '0';
            mainContent.style.paddingTop = '0';
        }
    });
</script>
{% endblock %}