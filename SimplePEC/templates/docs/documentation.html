{% extends 'base/base.html' %}

{% block title %}Documentazione Utente - SimplePEC{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Indice</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="#introduzione" class="list-group-item list-group-item-action">Introduzione</a>
                    <a href="#dashboard" class="list-group-item list-group-item-action">Dashboard</a>
                    <a href="#messaggi" class="list-group-item list-group-item-action">Messaggi PEC</a>
                    <a href="#account-pec" class="list-group-item list-group-item-action">Account PEC</a>
                    <a href="#clienti" class="list-group-item list-group-item-action"><PERSON><PERSON><PERSON></a>
                    
                    {% if request.user.is_manager or request.user.is_superuser %}
                    <a href="#utenti" class="list-group-item list-group-item-action">Gestione Utenti</a>
                    <a href="#organizzazioni" class="list-group-item list-group-item-action">Organizzazioni</a>
                    <a href="#schedulazioni" class="list-group-item list-group-item-action">Schedulazioni</a>
                    {% endif %}
                    
                    {% if request.user.is_superuser %}
                    <a href="#configurazione" class="list-group-item list-group-item-action">Configurazione</a>
                    {% endif %}
                    
                    <a href="#impostazioni" class="list-group-item list-group-item-action">Impostazioni</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Documentazione Utente</h4>
                <span class="badge bg-primary">{{ user_role }}</span>
            </div>
            <div class="card-body">
                <section id="introduzione" class="mb-5">
                    <h2>Introduzione a SimplePEC</h2>
                    <p>Benvenuto nella documentazione utente di SimplePEC, il sistema per la gestione delle caselle di Posta Elettronica Certificata (PEC).</p>
                    
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>Il tuo ruolo: {{ user_role }}</h5>
                        <p class="mb-0">
                            {% if request.user.is_superuser %}
                            Come <strong>Superuser</strong>, hai accesso completo a tutte le funzionalità del sistema, inclusa la configurazione avanzata.
                            {% elif request.user.is_manager %}
                            Come <strong>Manager</strong>, hai accesso completo ai dati della tua organizzazione e puoi gestire account PEC, clienti e utenti.
                            {% elif request.user.is_staff_user %}
                            Come <strong>Staff</strong>, hai accesso limitato agli account PEC e ai clienti a cui sei stato assegnato, con permessi specifici per ciascuna operazione.
                            {% else %}
                            Il tuo account non ha un ruolo specifico assegnato. Contatta l'amministratore del sistema per maggiori informazioni.
                            {% endif %}
                        </p>
                    </div>
                </section>
                
                <section id="dashboard" class="mb-5">
                    <h2>Dashboard</h2>
                    <p>La dashboard fornisce una panoramica delle attività e delle statistiche principali:</p>
                    <ul>
                        <li><strong>Conteggi messaggi</strong>: Visualizza il numero totale di messaggi e la loro distribuzione per stato.</li>
                        <li><strong>Statistiche per account</strong>: Mostra i dati relativi agli account PEC a cui hai accesso.</li>
                        <li><strong>Messaggi recenti</strong>: Elenca gli ultimi messaggi ricevuti.</li>
                    </ul>
                    <div class="text-center mb-3">
                        <img src="https://via.placeholder.com/800x400?text=Dashboard+Screenshot" alt="Dashboard" class="img-fluid rounded border">
                    </div>
                </section>
                
                <section id="messaggi" class="mb-5">
                    <h2>Messaggi PEC</h2>
                    <p>La sezione Messaggi ti permette di visualizzare e gestire tutti i messaggi PEC ricevuti:</p>
                    <ul>
                        <li><strong>Visualizzazione messaggi</strong>: Puoi vedere tutti i messaggi degli account PEC a cui hai accesso.</li>
                        <li><strong>Filtri e ricerca</strong>: Puoi filtrare i messaggi per stato, account, data e cercare per oggetto o mittente.</li>
                        <li><strong>Dettaglio messaggio</strong>: Visualizza il contenuto completo del messaggio, gli allegati e le informazioni di invio.</li>
                        <li><strong>Gestione stato</strong>: Puoi aggiornare lo stato dei messaggi (Da leggere, Da assegnare, Assegnato, Lavorato).</li>
                        <li><strong>Assegnazione cliente</strong>: Puoi associare un messaggio a un cliente specifico.</li>
                        <li><strong>Assegnazione referente</strong>: Puoi assegnare un messaggio a un referente specifico.</li>
                    </ul>
                    
                    {% if request.user.is_staff_user %}
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Nota per utenti Staff</h5>
                        <p class="mb-0">Come utente Staff, puoi vedere solo i messaggi degli account PEC a cui sei stato assegnato. Le operazioni che puoi eseguire sui messaggi dipendono dai permessi specifici che ti sono stati concessi.</p>
                    </div>
                    {% endif %}
                </section>
                
                <section id="account-pec" class="mb-5">
                    <h2>Account PEC</h2>
                    <p>La sezione Account PEC mostra tutti gli account di posta elettronica certificata configurati nel sistema:</p>
                    <ul>
                        <li><strong>Elenco account</strong>: Visualizza tutti gli account PEC a cui hai accesso.</li>
                        <li><strong>Dettaglio account</strong>: Mostra le informazioni dettagliate dell'account, inclusi i parametri di connessione.</li>
                        {% if request.user.is_manager or request.user.is_superuser %}
                        <li><strong>Creazione account</strong>: Puoi aggiungere nuovi account PEC inserendo i parametri di connessione.</li>
                        <li><strong>Modifica account</strong>: Puoi modificare i parametri di connessione degli account esistenti.</li>
                        <li><strong>Eliminazione account</strong>: Puoi rimuovere account PEC non più necessari.</li>
                        <li><strong>Assegnazione utenti</strong>: Puoi assegnare utenti agli account PEC e definire i loro permessi specifici.</li>
                        {% endif %}
                        <li><strong>Sincronizzazione manuale</strong>: Puoi avviare manualmente la sincronizzazione dei messaggi.</li>
                    </ul>
                    
                    {% if request.user.is_staff_user %}
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Nota per utenti Staff</h5>
                        <p class="mb-0">Come utente Staff, puoi solo visualizzare gli account PEC a cui sei stato assegnato, senza possibilità di modificarli o crearne di nuovi.</p>
                    </div>
                    {% endif %}
                </section>
                
                <section id="clienti" class="mb-5">
                    <h2>Clienti</h2>
                    <p>La sezione Clienti permette di gestire l'anagrafica dei clienti e dei loro referenti:</p>
                    <ul>
                        <li><strong>Elenco clienti</strong>: Visualizza tutti i clienti a cui hai accesso.</li>
                        <li><strong>Dettaglio cliente</strong>: Mostra le informazioni dettagliate del cliente e i suoi referenti.</li>
                        <li><strong>Ricerca clienti</strong>: Puoi cercare clienti per nome, codice o altri campi.</li>
                        {% if request.user.is_manager or request.user.is_superuser %}
                        <li><strong>Creazione cliente</strong>: Puoi aggiungere nuovi clienti inserendo i loro dati anagrafici.</li>
                        <li><strong>Modifica cliente</strong>: Puoi aggiornare i dati dei clienti esistenti.</li>
                        <li><strong>Eliminazione cliente</strong>: Puoi rimuovere clienti non più necessari.</li>
                        <li><strong>Gestione referenti</strong>: Puoi aggiungere, modificare ed eliminare i referenti dei clienti.</li>
                        {% endif %}
                    </ul>
                    
                    {% if request.user.is_staff_user %}
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Nota per utenti Staff</h5>
                        <p class="mb-0">Come utente Staff, puoi vedere e modificare solo i clienti a cui sei stato assegnato. Non puoi creare nuovi clienti o eliminare quelli esistenti.</p>
                    </div>
                    {% endif %}
                </section>
                
                {% if request.user.is_manager or request.user.is_superuser %}
                <section id="utenti" class="mb-5">
                    <h2>Gestione Utenti</h2>
                    <p>La sezione Utenti permette di gestire gli utenti del sistema:</p>
                    <ul>
                        <li><strong>Elenco utenti</strong>: Visualizza tutti gli utenti della tua organizzazione.</li>
                        <li><strong>Dettaglio utente</strong>: Mostra le informazioni dettagliate dell'utente e i suoi permessi.</li>
                        <li><strong>Creazione utente</strong>: Puoi aggiungere nuovi utenti inserendo i loro dati e assegnando un ruolo.</li>
                        <li><strong>Modifica utente</strong>: Puoi aggiornare i dati degli utenti esistenti e modificare il loro ruolo.</li>
                        <li><strong>Assegnazione account</strong>: Puoi assegnare account PEC agli utenti e definire i loro permessi specifici.</li>
                    </ul>
                </section>
                
                <section id="organizzazioni" class="mb-5">
                    <h2>Organizzazioni</h2>
                    <p>La sezione Organizzazioni permette di gestire le informazioni relative alla tua organizzazione:</p>
                    <ul>
                        <li><strong>Dettaglio organizzazione</strong>: Visualizza e modifica i dati della tua organizzazione (ragione sociale, indirizzo, contatti, logo).</li>
                        <li><strong>Associazione utenti</strong>: Puoi associare utenti alla tua organizzazione.</li>
                        <li><strong>Associazione account PEC</strong>: Puoi associare account PEC alla tua organizzazione.</li>
                    </ul>
                </section>
                
                <section id="schedulazioni" class="mb-5">
                    <h2>Schedulazioni</h2>
                    <p>La sezione Schedulazioni permette di configurare la sincronizzazione automatica degli account PEC:</p>
                    <ul>
                        <li><strong>Elenco schedulazioni</strong>: Visualizza tutte le schedulazioni configurate.</li>
                        <li><strong>Creazione schedulazione</strong>: Puoi creare nuove schedulazioni per la sincronizzazione automatica degli account PEC.</li>
                        <li><strong>Modifica schedulazione</strong>: Puoi modificare i parametri delle schedulazioni esistenti.</li>
                        <li><strong>Eliminazione schedulazione</strong>: Puoi rimuovere schedulazioni non più necessarie.</li>
                    </ul>
                </section>
                {% endif %}
                
                {% if request.user.is_superuser %}
                <section id="configurazione" class="mb-5">
                    <h2>Configurazione</h2>
                    <p>La sezione Configurazione (accessibile solo ai Superuser) permette di gestire le impostazioni avanzate del sistema:</p>
                    <ul>
                        <li><strong>Interfaccia Admin</strong>: Accesso all'interfaccia di amministrazione di Django per la gestione avanzata del sistema.</li>
                        <li><strong>Gestione gruppi</strong>: Configurazione dei gruppi di utenti e dei loro permessi.</li>
                        <li><strong>Impostazioni sistema</strong>: Configurazione delle impostazioni globali del sistema.</li>
                    </ul>
                </section>
                {% endif %}
                
                <section id="impostazioni" class="mb-5">
                    <h2>Impostazioni</h2>
                    <p>Le impostazioni dell'applicazione sono accessibili cliccando sull'icona dell'ingranaggio nella parte inferiore della sidebar:</p>
                    <ul>
                        <li><strong>Tema</strong>: Puoi scegliere tra tema chiaro e tema scuro per l'interfaccia.</li>
                        <li><strong>Layout</strong>: Puoi decidere se mantenere la sidebar espansa o collassata.</li>
                    </ul>
                    <div class="text-center">
                        <img src="https://via.placeholder.com/400x300?text=Impostazioni+Screenshot" alt="Impostazioni" class="img-fluid rounded border">
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
{% endblock %}
