{% extends 'base/base.html' %}

{% block title %}Elimina Schedulazione - SimplePEC{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'schedulazione_list' %}">Schedulazioni</a></li>
            <li class="breadcrumb-item active" aria-current="page">Elimina Schedulazione</li>
        </ol>
    </nav>
</div>

<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">Conferma eliminazione</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Stai per eliminare definitivamente questa schedulazione. Questa azione non può essere annullata.
        </div>
        
        <div class="mb-4">
            <h6>Dettagli schedulazione:</h6>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>Account PEC:</span>
                    <span class="fw-semibold">{{ object.account_pec.nome }}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>Indirizzo email:</span>
                    <span class="fw-semibold">{{ object.account_pec.indirizzo_email }}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>Frequenza:</span>
                    <span class="fw-semibold">{{ object.get_frequenza_display }}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>Tipo sincronizzazione:</span>
                    <span class="fw-semibold">{{ object.get_tipo_sincronizzazione_display }}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>Stato:</span>
                    <span class="fw-semibold">
                        {% if object.attiva %}
                        <span class="badge bg-success">Attiva</span>
                        {% else %}
                        <span class="badge bg-secondary">Disattivata</span>
                        {% endif %}
                    </span>
                </li>
            </ul>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'schedulazione_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Annulla
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> Elimina definitivamente
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
