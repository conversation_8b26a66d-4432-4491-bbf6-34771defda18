{% extends 'base/base.html' %}

{% block title %}Messaggi in Uscita - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .messages-table tbody tr {
        cursor: pointer;
        transition: background-color 0.15s;
    }

    .messages-table tbody tr:hover {
        background-color: #f9fafb;
    }

    .filter-card {
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .btn-filter {
        border-radius: 6px;
    }

    .badge-da_leggere {
        background-color: #ef4444;
        color: white;
    }

    .badge-da_assegnare {
        background-color: #f59e0b;
        color: white;
    }

    .badge-assegnato {
        background-color: #3b82f6;
        color: white;
    }

    .badge-lavorato {
        background-color: #10b981;
        color: white;
    }



    .pagination .page-item.active .page-link {
        background-color: #3b82f6;
        border-color: #3b82f6;
    }

    .pagination .page-link {
        color: #3b82f6;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Me<PERSON><PERSON><PERSON> in Uscita</h1>
    <div>
        <button class="btn btn-sm btn-outline-secondary me-2" id="toggleFilters">
            <i class="fas fa-filter me-1"></i> Filtri
        </button>
        <button class="btn btn-sm btn-primary" id="newMessageBtn" data-bs-toggle="modal" data-bs-target="#newMessageModal">
            <i class="fas fa-paper-plane me-1"></i> Nuovo Messaggio
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm filter-card" id="filtersCard" style="display: none;">
    <div class="card-body">
        <form method="get" action="{% url 'outbox_list' %}" class="row g-3">
            <div class="col-md-4">
                <label for="account" class="form-label">Account PEC</label>
                <select name="account" id="account" class="form-select">
                    <option value="">Tutti gli account</option>
                    {% for account in accounts %}
                    <option value="{{ account.id }}" {% if request.GET.account == account.id|stringformat:"s" %}selected{% endif %}>
                        {{ account.nome }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-4">
                <label for="data_da" class="form-label">Data da</label>
                <input type="date" name="data_da" id="data_da" class="form-control" value="{{ request.GET.data_da }}">
            </div>

            <div class="col-md-4">
                <label for="data_a" class="form-label">Data a</label>
                <input type="date" name="data_a" id="data_a" class="form-control" value="{{ request.GET.data_a }}">
            </div>

            <div class="col-md-4">
                <label for="search" class="form-label">Ricerca</label>
                <input type="text" name="search" id="search" class="form-control" placeholder="Oggetto, destinatario..." value="{{ request.GET.search }}">
            </div>

            <div class="col-12 d-flex justify-content-end">
                <a href="{% url 'outbox_list' %}" class="btn btn-sm btn-outline-secondary me-2">Reset</a>
                <button type="submit" class="btn btn-sm btn-primary btn-filter">Applica filtri</button>
            </div>
        </form>
    </div>
</div>

<!-- Messages table -->
<div class="card border-0 shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover messages-table mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Account PEC</th>
                        <th>Destinatario</th>
                        <th>Oggetto</th>
                        <th>Data</th>
                        <th>Stato</th>
                    </tr>
                </thead>
                <tbody>
                    {% for messaggio in messaggi %}
                    <tr data-href="{% url 'message_detail' messaggio.id %}" class="{% if messaggio.stato == 'DA_LEGGERE' %}message-unread{% endif %}">
                        <td>{{ messaggio.account_pec_info }}</td>
                        <td>
                            {% for destinatario in messaggio.destinatari_info %}
                                {{ destinatario.nome }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </td>
                        <td>{{ messaggio.oggetto|truncatechars:60 }}</td>
                        <td>{{ messaggio.data_ricezione|date:"d/m/Y H:i" }}</td>
                        <td>
                            <span class="text-muted">Inviato</span>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-paper-plane fa-3x mb-3 opacity-50"></i>
                                <p class="mb-1">Nessun messaggio inviato trovato</p>
                                <p class="small">Clicca su "Nuovo Messaggio" per inviare una PEC</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if paginator.num_pages > 1 %}
<div class="mt-4 d-flex justify-content-between align-items-center">
    <div class="small text-muted">
        Mostrando {{ page_obj.start_index }}-{{ page_obj.end_index }} di {{ paginator.count }} messaggi
    </div>

    <nav aria-label="Page navigation">
        <ul class="pagination pagination-sm mb-0">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% for i in paginator.page_range %}
                {% if page_obj.number == i %}
                    <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ i }}">{{ i }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ paginator.num_pages }}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

<!-- Modal per nuovo messaggio -->
<div class="modal fade" id="newMessageModal" tabindex="-1" aria-labelledby="newMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newMessageModalLabel">Nuovo Messaggio PEC</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'send_message' %}" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="account_pec" class="form-label">Account PEC mittente</label>
                        <select name="account_pec" id="account_pec" class="form-select" required>
                            <option value="">Seleziona un account PEC</option>
                            {% for account in available_accounts %}
                            <option value="{{ account.id }}">{{ account.nome }} ({{ account.indirizzo_email }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="destinatari" class="form-label">Destinatari</label>
                        <input type="text" name="destinatari" id="destinatari" class="form-control" placeholder="<EMAIL>, <EMAIL>" required>
                        <div class="form-text">Inserisci gli indirizzi PEC separati da virgola</div>
                    </div>

                    <div class="mb-3">
                        <label for="oggetto" class="form-label">Oggetto</label>
                        <input type="text" name="oggetto" id="oggetto" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label for="contenuto" class="form-label">Contenuto</label>
                        <textarea name="contenuto" id="contenuto" class="form-control" rows="6" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="allegati" class="form-label">Allegati</label>
                        <input type="file" name="allegati" id="allegati" class="form-control" multiple>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i> Invia
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle filters
        const toggleFiltersBtn = document.getElementById('toggleFilters');
        const filtersCard = document.getElementById('filtersCard');

        if (toggleFiltersBtn && filtersCard) {
            // If there are active filters, show the filters card by default
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.toString() !== '') {
                filtersCard.style.display = 'block';
            }

            toggleFiltersBtn.addEventListener('click', function() {
                if (filtersCard.style.display === 'none' || filtersCard.style.display === '') {
                    filtersCard.style.display = 'block';
                } else {
                    filtersCard.style.display = 'none';
                }
            });
        }

        // Click on table row to navigate to detail
        const rows = document.querySelectorAll('.messages-table tbody tr[data-href]');
        rows.forEach(row => {
            row.addEventListener('click', function() {
                window.location.href = this.dataset.href;
            });
        });
    });
</script>
{% endblock %}
