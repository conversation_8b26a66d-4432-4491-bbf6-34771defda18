{% extends 'base/base.html' %}

{% block title %}Account PEC - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .account-card {
        border-radius: 12px;
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .account-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
    }

    .account-header {
        padding: 20px;
        background-color: #eff6ff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .account-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .account-email {
        color: #6b7280;
        font-size: 14px;
    }

    .account-status {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .account-info {
        padding: 20px;
    }

    .info-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: #6b7280;
    }

    .info-value {
        font-weight: 500;
    }

    .info-label {
        font-size: 13px;
        color: #6b7280;
        display: block;
    }

    .account-actions {
        padding: 15px 20px;
        background-color: #f9fafb;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .account-add-card {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 300px;
        cursor: pointer;
        transition: border-color 0.2s, background-color 0.2s;
    }

    .account-add-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }

    .add-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
    }

    .loading-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .loading-content {
        text-align: center;
        color: white;
        background-color: rgba(0, 0, 0, 0.4);
        border-radius: 12px;
        padding: 30px;
        max-width: 400px;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .loading-message {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .loading-description {
        font-size: 14px;
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Account PEC</h1>
    <div>
        {% if request.user.is_superuser or is_manager %}
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#newAccountModal">
            <i class="fas fa-plus me-1"></i> Nuovo account
        </button>
        {% endif %}
    </div>
</div>

<div class="row g-4">
    {% for account in accounts %}
    <div class="col-md-6 col-lg-4">
        <div class="card account-card border-0 shadow-sm h-100">
            <div class="account-header position-relative">
                <h3 class="account-title">{{ account.nome }}</h3>
                <div class="account-email">{{ account.indirizzo_email }}</div>

                <div class="account-status">
                    {% if account.attivo %}
                    <span class="badge bg-success">Attivo</span>
                    {% else %}
                    <span class="badge bg-danger">Inattivo</span>
                    {% endif %}
                </div>
            </div>

            <div class="account-info">
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div>
                        <span class="info-label">Server in entrata</span>
                        <div class="info-value">{{ account.server_imap }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div>
                        <span class="info-label">Server in uscita</span>
                        <div class="info-value">{{ account.server_smtp }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div>
                        <span class="info-label">Ultima sincronizzazione</span>
                        <div class="info-value">
                            {% if account.ultima_sincronizzazione %}
                            {{ account.ultima_sincronizzazione|date:"d/m/Y H:i" }}
                            {% else %}
                            <em class="text-muted">Mai sincronizzato</em>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="account-actions">
                <div class="btn-group w-100" role="group">
                    <a href="{% url 'account_detail' account.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i>
                    </a>
                    <a href="{% url 'message_list' %}?account={{ account.id }}" class="btn btn-outline-secondary">
                        <i class="fas fa-envelope"></i> Messaggi
                    </a>
                    <form method="post" action="{% url 'account_sync' account.id %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i> Sincronizza
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-envelope fa-3x mb-3 opacity-50"></i>
                <p class="mb-1">Nessun account PEC configurato</p>
                <p class="small">Clicca su "Nuovo account" per aggiungerne uno</p>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Add account card (alternative way to add a new account) -->
    {% if request.user.is_superuser or is_manager %}
    <div class="col-md-6 col-lg-4">
        <div class="account-add-card" data-bs-toggle="modal" data-bs-target="#newAccountModal">
            <div class="add-icon">
                <i class="fas fa-plus"></i>
            </div>
            <h3 class="fs-5 mb-1">Aggiungi account</h3>
            <p class="text-muted small">Configura un nuovo account PEC</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- New Account Modal -->
<div class="modal fade" id="newAccountModal" tabindex="-1" aria-labelledby="newAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newAccountModalLabel">Nuovo account PEC</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'account_create' %}" id="newAccountForm">
                    {% csrf_token %}

                    <!-- Account details -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informazioni account</h6>

                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome account</label>
                            <input type="text" class="form-control" id="nome" name="nome" required placeholder="Es. PEC Aziendale">
                            <div class="form-text">Un nome identificativo per questo account</div>
                        </div>

                        <div class="mb-3">
                            <label for="indirizzo_email" class="form-label">Indirizzo email PEC</label>
                            <input type="email" class="form-control" id="indirizzo_email" name="indirizzo_email" required placeholder="<EMAIL>">
                        </div>

                        <div class="mb-3">
                            <label for="organizzazione" class="form-label">Organizzazione</label>
                            {% if request.user.is_superuser %}
                            <!-- Admin può selezionare qualsiasi organizzazione -->
                            <select class="form-select" id="organizzazione" name="organizzazione">
                                <option value="">-- Nessuna organizzazione --</option>
                                {% for org in organizzazioni %}
                                <option value="{{ org.id }}">{{ org.ragione_sociale }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Seleziona l'organizzazione a cui appartiene questo account PEC</div>
                            {% elif is_manager and request.user.organizzazione %}
                            <!-- Manager vede solo la sua organizzazione (non modificabile) -->
                            <input type="text" class="form-control" value="{{ request.user.organizzazione.ragione_sociale }}" readonly>
                            <input type="hidden" name="organizzazione" value="{{ request.user.organizzazione.id }}">
                            <div class="form-text">L'account sarà associato alla tua organizzazione</div>
                            {% else %}
                            <!-- Nessuna organizzazione disponibile -->
                            <input type="text" class="form-control" value="Nessuna organizzazione disponibile" readonly disabled>
                            <div class="form-text">Non hai un'organizzazione associata</div>
                            {% endif %}
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="attivo" name="attivo" checked>
                            <label class="form-check-label" for="attivo">Account attivo</label>
                            <div class="form-text">Gli account inattivi non verranno sincronizzati</div>
                        </div>
                    </div>

                    <!-- Server settings -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Impostazioni server</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="server_imap" class="form-label">Server IMAP</label>
                                    <input type="text" class="form-control" id="server_imap" name="server_imap" required placeholder="imap.example.com">
                                </div>

                                <div class="mb-3">
                                    <label for="porta_imap" class="form-label">Porta IMAP</label>
                                    <input type="number" class="form-control" id="porta_imap" name="porta_imap" required value="993">
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="ssl_imap" name="ssl_imap" checked>
                                    <label class="form-check-label" for="ssl_imap">Usa SSL/TLS</label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="server_smtp" class="form-label">Server SMTP</label>
                                    <input type="text" class="form-control" id="server_smtp" name="server_smtp" required placeholder="smtp.example.com">
                                </div>

                                <div class="mb-3">
                                    <label for="porta_smtp" class="form-label">Porta SMTP</label>
                                    <input type="number" class="form-control" id="porta_smtp" name="porta_smtp" required value="465">
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="ssl_smtp" name="ssl_smtp" checked>
                                    <label class="form-check-label" for="ssl_smtp">Usa SSL/TLS</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Authentication -->
                    <div>
                        <h6 class="fw-bold mb-3">Autenticazione</h6>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required placeholder="Spesso coincide con l'indirizzo email">
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('newAccountForm').submit()">
                    <i class="fas fa-save me-1"></i> Salva account
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-message" id="loadingMessage">Sincronizzazione in corso...</div>
        <div class="loading-description" id="loadingDescription">Stiamo recuperando i messaggi PEC. L'operazione potrebbe richiedere alcuni minuti.</div>
    </div>
</div>

<!-- JavaScript per gestire l'overlay di caricamento -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ottiene i riferimenti agli elementi
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingDescription = document.getElementById('loadingDescription');

        // Funzione per mostrare l'overlay
        function showLoading(message, description) {
            loadingMessage.textContent = message || 'Operazione in corso...';
            loadingDescription.textContent = description || 'Attendere il completamento dell\'operazione.';
            loadingOverlay.classList.add('active');
        }

        // Aggiungi ID e class ai form di sincronizzazione per poterli selezionare
        document.querySelectorAll('form[action*="sync"]').forEach((form, index) => {
            form.classList.add('sync-form');
            form.id = 'syncForm-' + index;

            // Aggiungi event listener per mostrare l'overlay di caricamento
            form.addEventListener('submit', function() {
                const accountName = this.closest('.account-card').querySelector('.account-title').textContent.trim();
                showLoading(
                    'Sincronizzazione in corso...',
                    'Stiamo recuperando i messaggi PEC per l\'account "' + accountName + '". L\'operazione potrebbe richiedere alcuni minuti.'
                );
            });
        });
    });
</script>
{% endblock %}