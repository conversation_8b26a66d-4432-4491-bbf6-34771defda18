{% extends 'base/base.html' %}

{% block title %}{{ messaggio.oggetto }} - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .message-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .message-subject {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .message-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }

    .message-meta-item {
        display: flex;
        align-items: center;
    }

    .message-meta-item i {
        color: #6b7280;
        margin-right: 8px;
        width: 16px;
    }

    .message-attachments {
        margin-top: 25px;
    }

    .message-attachment {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: #f9fafb;
        border-radius: 8px;
        margin-bottom: 10px;
        transition: background-color 0.15s;
    }

    .message-attachment:hover {
        background-color: #eff6ff;
    }

    .attachment-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        background-color: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 20px;
        color: #6b7280;
    }

    .attachment-info {
        flex-grow: 1;
    }

    .attachment-name {
        font-weight: 500;
        margin-bottom: 2px;
    }

    .attachment-size {
        font-size: 12px;
        color: #6b7280;
    }

    .message-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .bg-da_leggere {
        background-color: #ef4444;
        color: white;
    }

    .bg-da_assegnare {
        background-color: #f59e0b;
        color: white;
    }

    .bg-assegnato {
        background-color: #3b82f6;
        color: white;
    }

    .bg-lavorato {
        background-color: #10b981;
        color: white;
    }

    .message-content {
        background-color: white;
        padding: 25px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .content-html {
        overflow-x: auto;
        max-width: 100%;
    }

    .content-html * {
        max-width: 100%;
    }

    .content-html img {
        height: auto;
    }

    .content-html table {
        width: auto;
        max-width: 100%;
        border-collapse: collapse;
    }

    .content-html td, .content-html th {
        padding: 4px 8px;
        border: 1px solid #ddd;
    }

    .message-sidebar {
        position: sticky;
        top: 80px;
    }

    .sidebar-card {
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .sidebar-section {
        margin-bottom: 20px;
    }

    .sidebar-title {
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .sidebar-title i {
        margin-right: 8px;
        color: #6b7280;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url list_url %}">{% if is_outbox %}Messaggi in Uscita{% else %}Messaggi{% endif %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ messaggio.oggetto|truncatechars:30 }}</li>
        </ol>
    </nav>

    <div class="message-actions">
        <button class="btn btn-sm btn-outline-secondary" onclick="window.history.back()">
            <i class="fas fa-arrow-left me-1"></i> Indietro
        </button>

        {% if not is_outbox %}
        <a href="{% url 'message_download' messaggio.id %}" class="btn btn-sm btn-outline-primary">
            <i class="fas fa-download me-1"></i> Scarica EML
        </a>

        <a href="{% url 'message_view_postacert' messaggio.id %}" class="btn btn-sm btn-outline-info" target="_blank">
            <i class="fas fa-envelope-open-text me-1"></i> Visualizza Postacert
        </a>
        {% endif %}

        {% if is_outbox and messaggio.stato == 'ERRORE_INVIO' %}
        <form method="post" action="{% url 'retry_send_message' messaggio.id %}" style="display: inline;">
            {% csrf_token %}
            <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('Sei sicuro di voler ritentare l\'invio di questo messaggio?')">
                <i class="fas fa-redo me-1"></i> Riprova Invio
            </button>
        </form>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        {% if is_ricevuta %}
        <div class="alert alert-info mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-3 fa-2x"></i>
                <div>
                    <h5 class="mb-1">Messaggio di ricevuta PEC</h5>
                    <p class="mb-0">Questo è un messaggio di ricevuta PEC.</p>
                </div>
            </div>
        </div>
        {% endif %}

        {% if request.user.is_superuser %}
        <!-- Debug info solo per superuser -->
        <div class="alert alert-secondary mb-4">
            <h5>Debug Info</h5>
            <ul>
                <li>is_ricevuta: {{ is_ricevuta }}</li>
                <!-- Rimosso is_ricevuta_permessi perché non più utilizzato -->
                <li>puo_aggiornare_stato: {{ puo_aggiornare_stato }}</li>
                <li>puo_assegnare_cliente: {{ puo_assegnare_cliente }}</li>
                <li>puo_assegnare_referente: {{ puo_assegnare_referente }}</li>
                <li>Utente è staff: {{ request.user.is_staff_user }}</li>
                <li>Utente è manager: {{ request.user.is_manager }}</li>
                <li>Oggetto messaggio: {{ messaggio.oggetto }}</li>
            </ul>
        </div>
        {% endif %}
        <!-- Message header -->
        <div class="message-header">
            <div class="d-flex align-items-center mb-3">
                <h1 class="message-subject mb-0 me-3">{{ messaggio.oggetto }}</h1>
                {% if is_outbox %}
                <span class="badge bg-inviato">Inviato</span>
                {% else %}
                <span class="badge bg-{{ messaggio.stato|lower }}">{{ messaggio.get_stato_display }}</span>
                {% endif %}
            </div>

            <div class="message-meta mb-3">
                <div class="message-meta-item">
                    <i class="fas fa-user"></i>
                    <span>Da: <strong>{% if is_outbox %}{{ messaggio.account_pec.nome }} &lt;{{ messaggio.account_pec.indirizzo_email }}&gt;{% elif mittente_postacert %}{{ mittente_postacert }}{% elif messaggio.mittente %}{{ messaggio.mittente.nome }} &lt;{{ messaggio.mittente.email }}&gt;{% else %}{{ messaggio.mittente_raw|default:"Non disponibile" }}{% endif %}</strong></span>
                </div>

                <div class="message-meta-item">
                    <i class="fas fa-envelope"></i>
                    <span>Account: <strong>{{ messaggio.account_pec.nome }} ({{ messaggio.account_pec.indirizzo_email }})</strong></span>
                </div>

                <div class="message-meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>Data: <strong>{{ messaggio.data_ricezione|date:"d/m/Y H:i" }}</strong></span>
                </div>
            </div>

            <div class="message-meta">
                {% if messaggio.destinatari.all %}
                <div class="message-meta-item mb-2 w-100">
                    <i class="fas fa-users"></i>
                    <span>A:
                        <strong>
                        {% for dest in messaggio.destinatari.all %}
                            {{ dest.nome }} &lt;{{ dest.email }}&gt;{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                        </strong>
                    </span>
                </div>
                {% endif %}

                {% if messaggio.destinatari_cc %}
                <div class="message-meta-item w-100">
                    <i class="fas fa-copy"></i>
                    <span>CC: <strong>{{ messaggio.destinatari_cc }}</strong></span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Error details for outbox messages with errors -->
        {% if is_outbox and messaggio.stato == 'ERRORE_INVIO' and messaggio.errore_invio %}
        <div class="card border-0 shadow-sm mb-4 border-danger">
            <div class="card-header bg-danger text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-semibold">
                        <i class="fas fa-exclamation-triangle me-2"></i>Dettagli Errore Invio
                    </h5>
                    <span class="badge bg-light text-danger">
                        Tentativo {{ messaggio.numero_tentativi }}
                    </span>
                </div>
                {% if messaggio.data_ultimo_tentativo %}
                <small class="text-white-50">
                    Ultimo tentativo: {{ messaggio.data_ultimo_tentativo|date:"d/m/Y H:i:s" }}
                </small>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="alert alert-danger mb-0">
                    <h6 class="alert-heading">Errore durante l'invio:</h6>
                    <pre class="mb-0" style="white-space: pre-wrap; word-wrap: break-word; font-size: 0.9em;">{{ messaggio.errore_invio }}</pre>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Message content -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-semibold">Contenuto del messaggio</h5>
                    <div>
                        {% if is_ricevuta %}
                        <span class="badge bg-warning">{{ ricevuta_tipo }}</span>
                        {% endif %}
                        {% if is_messaggio_originale %}
                        <span class="badge bg-info">Messaggio originale sbustato</span>
                        {% endif %}
                        {% if mittente_postacert and not is_ricevuta and not is_outbox %}
                        <span class="badge bg-success">Mittente da postacert</span>
                        {% endif %}
                    </div>
                </div>
                {% if messaggio_originale_info %}
                <small class="text-muted">Estratto da: {{ messaggio_originale_info }}</small>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="message-content">
                    {% if messaggio_body %}
                        {% if is_html %}
                            <div class="content-html">
                                {{ messaggio_body|safe }}
                            </div>
                        {% else %}
                            <div class="content-text">
                                <pre class="text-body" style="white-space: pre-wrap; word-wrap: break-word;">{{ messaggio_body }}</pre>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="content-text text-muted">
                            <p>Il contenuto del messaggio non è disponibile.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Attachments -->
        {% if allegati %}
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fw-semibold">Allegati ({{ allegati|length }})</h5>
            </div>
            <div class="card-body">
                <div class="message-attachments">
                    {% for allegato in allegati %}
                    <a href="{% url 'attachment_download' allegato.id %}" class="message-attachment text-decoration-none text-reset">
                        <div class="attachment-icon">
                            <i class="fas fa-file"></i>
                        </div>
                        <div class="attachment-info">
                            <div class="attachment-name">{{ allegato.nome_file }}</div>
                            <div class="attachment-size">{{ allegato.dimensione|filesizeformat }}</div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i>
                        </button>
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <div class="message-sidebar">
            <!-- Actions sidebar -->
            <div class="card border-0 shadow-sm sidebar-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0 fw-semibold">Azioni</h5>
                </div>
                <div class="card-body">
                    <!-- Change status form -->
                    <div class="sidebar-section">
                        <div class="sidebar-title">
                            <i class="fas fa-tag"></i> Stato
                        </div>

                        {% if is_outbox and user.is_manager or is_outbox and user.is_superuser or is_outbox and puo_aggiornare_stato %}
                        <form method="post" action="{% url 'message_change_status' messaggio.id %}" class="mb-3">
                            {% csrf_token %}
                            <select name="stato" class="form-select mb-2">
                                <option value="INVIATO" selected>Inviato</option>
                            </select>
                            <button type="submit" class="btn btn-primary btn-sm w-100">Aggiorna stato</button>
                            <div class="small text-muted mt-2">
                                I messaggi in uscita hanno sempre lo stato "Inviato".
                            </div>
                        </form>
                        {% elif not is_outbox and user.is_manager or not is_outbox and user.is_superuser or not is_outbox and puo_aggiornare_stato %}
                        <form method="post" action="{% url 'message_change_status' messaggio.id %}" class="mb-3">
                            {% csrf_token %}
                            <select name="stato" class="form-select mb-2">
                                <option value="DA_LEGGERE" {% if messaggio.stato == 'DA_LEGGERE' %}selected{% endif %}>Da leggere</option>
                                <option value="DA_ASSEGNARE" {% if messaggio.stato == 'DA_ASSEGNARE' %}selected{% endif %}>Da assegnare</option>
                                <option value="ASSEGNATO" {% if messaggio.stato == 'ASSEGNATO' %}selected{% endif %}>Assegnato</option>
                                <option value="LAVORATO" {% if messaggio.stato == 'LAVORATO' %}selected{% endif %}>Lavorato</option>
                            </select>
                            <button type="submit" class="btn btn-primary btn-sm w-100">Aggiorna stato</button>
                        </form>
                        {% else %}
                        <div class="alert alert-secondary mb-3">
                            {% if is_outbox %}
                            <div class="fw-bold mb-2">Inviato</div>
                            {% else %}
                            <span class="badge bg-{{ messaggio.stato|lower }} mb-2">{{ messaggio.get_stato_display }}</span>
                            {% endif %}
                            <div class="small text-muted">
                                Non hai il permesso di aggiornare lo stato di questo messaggio.
                                Contatta l'amministratore per ottenere i permessi necessari.
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Assign client form -->
                    <div class="sidebar-section">
                        <div class="sidebar-title">
                            <i class="fas fa-building"></i> Cliente
                        </div>

                        {% if user.is_manager or user.is_superuser or puo_assegnare_cliente %}
                        <form method="post" action="{% url 'message_assign_client' messaggio.id %}">
                            {% csrf_token %}
                            <select name="cliente_id" class="form-select mb-2">
                                <option value="">-- Nessun cliente --</option>
                                {% for cliente in clienti %}
                                <option value="{{ cliente.id }}" {% if messaggio.cliente_id == cliente.id %}selected{% endif %}>
                                    {{ cliente.nome }}
                                </option>
                                {% endfor %}
                            </select>
                            <button type="submit" class="btn btn-primary btn-sm w-100">Assegna cliente</button>
                        </form>
                        {% else %}
                        <div class="alert alert-secondary mb-3">
                            {% if messaggio.cliente %}
                            <div class="fw-bold mb-2">{{ messaggio.cliente.nome }}</div>
                            {% else %}
                            <div class="fw-bold mb-2">Nessun cliente assegnato</div>
                            {% endif %}
                            <div class="small text-muted">
                                Non hai il permesso di assegnare un cliente a questo messaggio.
                                Contatta l'amministratore per ottenere i permessi necessari.
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Assign referent form (only if client is selected) -->
                    {% if messaggio.cliente_id %}
                    <div class="sidebar-section">
                        <div class="sidebar-title">
                            <i class="fas fa-user-tie"></i> Referente
                        </div>

                        {% if user.is_manager or user.is_superuser or puo_assegnare_referente %}
                        <form method="post" action="{% url 'message_assign_referent' messaggio.id %}">
                            {% csrf_token %}
                            <select name="referente_id" class="form-select mb-2">
                                <option value="">-- Nessun referente --</option>
                                {% for referente in referenti %}
                                <option value="{{ referente.id }}" {% if messaggio.referente_id == referente.id %}selected{% endif %}>
                                    {{ referente.utente.first_name }} {{ referente.utente.last_name }}
                                </option>
                                {% endfor %}
                            </select>
                            <button type="submit" class="btn btn-primary btn-sm w-100">Assegna referente</button>
                        </form>
                        {% else %}
                        <div class="alert alert-secondary mb-3">
                            {% if messaggio.referente %}
                            <div class="fw-bold mb-2">{{ messaggio.referente.utente.first_name }} {{ messaggio.referente.utente.last_name }}</div>
                            {% else %}
                            <div class="fw-bold mb-2">Nessun referente assegnato</div>
                            {% endif %}
                            <div class="small text-muted">
                                Non hai il permesso di assegnare un referente a questo messaggio.
                                Contatta l'amministratore per ottenere i permessi necessari.
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Forward message form -->
                    <div class="sidebar-section">
                        <div class="sidebar-title">
                            <i class="fas fa-share"></i> Inoltra messaggio
                        </div>

                        {% if user.is_manager or user.is_superuser or user.is_staff_user %}
                        <form method="post" action="{% url 'message_forward' messaggio.id %}">
                            {% csrf_token %}
                            <select name="utente_id" class="form-select mb-2" required>
                                <option value="">-- Seleziona utente --</option>
                                {% for utente in utenti %}
                                <option value="{{ utente.id }}">
                                    {{ utente.first_name }} {{ utente.last_name }} ({{ utente.email }})
                                </option>
                                {% endfor %}
                            </select>
                            <button type="submit" class="btn btn-primary btn-sm w-100">Inoltra via email</button>
                            <div class="small text-muted mt-2">
                                Il messaggio verrà inoltrato all'indirizzo email dell'utente selezionato.
                            </div>
                        </form>
                        {% else %}
                        <div class="alert alert-secondary mb-3">
                            <div class="small text-muted">
                                Non hai il permesso di inoltrare questo messaggio.
                                Contatta l'amministratore per ottenere i permessi necessari.
                            </div>
                        </div>
                        {% endif %}

                        {% if messaggio.inoltrato_a %}
                        <div class="alert alert-info mt-3 mb-0">
                            <div class="small">
                                <i class="fas fa-info-circle me-1"></i>
                                Messaggio già inoltrato a <strong>{{ messaggio.inoltrato_a.first_name }} {{ messaggio.inoltrato_a.last_name }}</strong>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Message info -->
            <div class="card border-0 shadow-sm sidebar-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0 fw-semibold">Informazioni</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>ID Messaggio:</span>
                            <strong>{{ messaggio.id }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>ID PEC:</span>
                            <strong>{{ messaggio.identificativo_messaggio }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Account:</span>
                            <strong>{{ messaggio.account_pec.nome }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Data ricezione:</span>
                            <strong>{{ messaggio.data_ricezione|date:"d/m/Y H:i" }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Stato:</span>
                            {% if is_outbox %}
                                <span class="badge badge-{{ messaggio.stato|lower }}">
                                    {{ messaggio.get_stato_display }}
                                </span>
                            {% else %}
                                <strong>{{ messaggio.get_stato_display }}</strong>
                            {% endif %}
                        </li>
                        {% if is_outbox and messaggio.numero_tentativi > 0 %}
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Tentativi invio:</span>
                            <strong>{{ messaggio.numero_tentativi }}</strong>
                        </li>
                        {% endif %}
                        {% if is_outbox and messaggio.data_ultimo_tentativo %}
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Ultimo tentativo:</span>
                            <strong>{{ messaggio.data_ultimo_tentativo|date:"d/m/Y H:i" }}</strong>
                        </li>
                        {% endif %}
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Cliente:</span>
                            <strong>{% if messaggio.cliente %}{{ messaggio.cliente.nome }}{% else %}Non assegnato{% endif %}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Referente:</span>
                            <strong>{% if messaggio.referente %}{{ messaggio.referente.utente.first_name }} {{ messaggio.referente.utente.last_name }}{% else %}Non assegnato{% endif %}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span>Allegati:</span>
                            <strong>{{ allegati|length }}</strong>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}