{% extends 'base/base.html' %}
{% load pec_extras %}

{% block title %}Schedulazioni Sincronizzazione - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .schedulazione-card {
        transition: all 0.2s ease;
    }
    .schedulazione-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .badge-frequenza {
        font-size: 0.8rem;
        padding: 0.35em 0.65em;
    }
    .schedulazione-inactive {
        opacity: 0.7;
    }
    .schedulazione-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
    }
    .schedulazione-card:hover .schedulazione-actions {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Schedulazioni Sincronizzazione</h1>

    {% if can_add %}
    <div>
        <a href="{% url 'schedulazione_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Nuova Schedulazione
        </a>
    </div>
    {% endif %}
</div>

<div class="card mb-4">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Gestione Schedulazioni</h5>
        </div>
    </div>
    <div class="card-body">
        <p class="text-muted mb-4">
            Qui puoi gestire le schedulazioni automatiche per la sincronizzazione degli account PEC.
            Le schedulazioni attive eseguiranno la sincronizzazione in base alla frequenza impostata.
        </p>

        {% if schedulazioni %}
        <div class="row">
            {% for schedulazione in schedulazioni %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 schedulazione-card {% if not schedulazione.attiva %}schedulazione-inactive{% endif %}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 text-truncate">
                            {{ schedulazione.account_pec.nome }}
                        </h5>
                        <div>
                            {% if schedulazione.attiva %}
                            <span class="badge bg-success">Attiva</span>
                            {% else %}
                            <span class="badge bg-secondary">Disattivata</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Account PEC:</span>
                                <span class="fw-semibold">{{ schedulazione.account_pec.indirizzo_email }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Frequenza:</span>
                                <span class="fw-semibold">{{ schedulazione.get_frequenza_display }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Tipo:</span>
                                <span class="fw-semibold">{{ schedulazione.get_tipo_sincronizzazione_display }}</span>
                            </div>
                            {% if schedulazione.ora_inizio %}
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Ora inizio:</span>
                                <span class="fw-semibold">{{ schedulazione.ora_inizio|time:"H:i" }}</span>
                            </div>
                            {% endif %}
                            {% if schedulazione.giorno_settimana is not None %}
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Giorno:</span>
                                <span class="fw-semibold">
                                    {% with giorni="Lunedì,Martedì,Mercoledì,Giovedì,Venerdì,Sabato,Domenica" %}
                                    {% with giorni_list=giorni|split:"," %}
                                    {{ giorni_list|index:schedulazione.giorno_settimana }}
                                    {% endwith %}
                                    {% endwith %}
                                </span>
                            </div>
                            {% endif %}
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">Creata da:</span>
                                <span class="fw-semibold">{{ schedulazione.creata_da }}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Ultima esecuzione:</span>
                                <span class="fw-semibold">
                                    {% if schedulazione.ultima_esecuzione %}
                                        {{ schedulazione.ultima_esecuzione|date:"d/m/Y H:i" }}
                                    {% else %}
                                        <span class="text-muted fst-italic">Mai eseguita</span>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between">
                            <div>
                                <small class="text-muted">Creata: {{ schedulazione.data_creazione|date:"d/m/Y H:i" }}</small>
                            </div>
                            <div class="schedulazione-actions">
                                <form method="post" action="{% url 'schedulazione_toggle' schedulazione.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-outline-primary">
                                        {% if schedulazione.attiva %}
                                        <i class="fas fa-pause"></i>
                                        {% else %}
                                        <i class="fas fa-play"></i>
                                        {% endif %}
                                    </button>
                                </form>
                                <a href="{% url 'schedulazione_update' schedulazione.id %}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'schedulazione_delete' schedulazione.id %}" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-calendar-alt fa-3x mb-3 opacity-50"></i>
                <p class="mb-1">Nessuna schedulazione configurata</p>
                <p class="small">Crea una nuova schedulazione per automatizzare la sincronizzazione degli account PEC</p>
            </div>
            {% if can_add %}
            <a href="{% url 'schedulazione_create' %}" class="btn btn-primary mt-3">
                <i class="fas fa-plus me-1"></i> Crea Schedulazione
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestione delle azioni sulle schedulazioni
        document.querySelectorAll('.schedulazione-actions form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                if (confirm('Sei sicuro di voler cambiare lo stato di questa schedulazione?')) {
                    this.submit();
                }
            });
        });
    });
</script>
{% endblock %}
