{% extends 'base/base.html' %}

{% block title %}Messaggi PEC - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .messages-table tbody tr {
        cursor: pointer;
        transition: background-color 0.15s;
    }

    .messages-table tbody tr:hover {
        background-color: #f9fafb;
    }

    .message-unread {
        font-weight: 500;
    }

    .filter-card {
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .btn-filter {
        border-radius: 6px;
    }

    .badge-da_leggere {
        background-color: #ef4444;
        color: white;
    }

    .badge-da_assegnare {
        background-color: #f59e0b;
        color: white;
    }

    .badge-assegnato {
        background-color: #3b82f6;
        color: white;
    }

    .badge-lavorato {
        background-color: #10b981;
        color: white;
    }

    .pagination .page-item.active .page-link {
        background-color: #3b82f6;
        border-color: #3b82f6;
    }

    .pagination .page-link {
        color: #3b82f6;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Messaggi PEC</h1>
    <div>
        <button class="btn btn-sm btn-outline-secondary me-2" id="toggleFilters">
            <i class="fas fa-filter me-1"></i> Filtri
        </button>
        <button class="btn btn-sm btn-outline-primary" id="refreshMessages">
            <i class="fas fa-sync-alt me-1"></i> Aggiorna
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm filter-card" id="filtersCard" style="display: none;">
    <div class="card-body">
        <form method="get" action="{% url 'message_list' %}" class="row g-3">
            <div class="col-md-4">
                <label for="account" class="form-label">Account PEC</label>
                <select name="account" id="account" class="form-select">
                    <option value="">Tutti gli account</option>
                    {% for account in accounts %}
                    <option value="{{ account.id }}" {% if request.GET.account == account.id|stringformat:"s" %}selected{% endif %}>
                        {{ account.nome }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-4">
                <label for="stato" class="form-label">Stato</label>
                <select name="stato" id="stato" class="form-select">
                    <option value="">Tutti gli stati</option>
                    <option value="DA_LEGGERE" {% if request.GET.stato == "DA_LEGGERE" %}selected{% endif %}>Da leggere</option>
                    <option value="DA_ASSEGNARE" {% if request.GET.stato == "DA_ASSEGNARE" %}selected{% endif %}>Da assegnare</option>
                    <option value="ASSEGNATO" {% if request.GET.stato == "ASSEGNATO" %}selected{% endif %}>Assegnato</option>
                    <option value="LAVORATO" {% if request.GET.stato == "LAVORATO" %}selected{% endif %}>Lavorato</option>
                </select>
            </div>

            <div class="col-md-4">
                <label for="cliente" class="form-label">Cliente</label>
                <select name="cliente" id="cliente" class="form-select">
                    <option value="">Tutti i clienti</option>
                    <option value="null" {% if request.GET.cliente == "null" %}selected{% endif %}>Senza cliente</option>
                    {% for cliente in clienti %}
                    <option value="{{ cliente.id }}" {% if request.GET.cliente == cliente.id|stringformat:"s" %}selected{% endif %}>
                        {{ cliente.nome }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-4">
                <label for="data_da" class="form-label">Data da</label>
                <input type="date" name="data_da" id="data_da" class="form-control" value="{{ request.GET.data_da }}">
            </div>

            <div class="col-md-4">
                <label for="data_a" class="form-label">Data a</label>
                <input type="date" name="data_a" id="data_a" class="form-control" value="{{ request.GET.data_a }}">
            </div>

            <div class="col-md-4">
                <label for="search" class="form-label">Ricerca</label>
                <input type="text" name="search" id="search" class="form-control" placeholder="Oggetto, mittente..." value="{{ request.GET.search }}">
            </div>

            <div class="col-12 d-flex justify-content-end">
                <a href="{% url 'message_list' %}" class="btn btn-sm btn-outline-secondary me-2">Reset</a>
                <button type="submit" class="btn btn-sm btn-primary btn-filter">Applica filtri</button>
            </div>
        </form>
    </div>
</div>

<!-- Messages table -->
<div class="card border-0 shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover messages-table mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Account PEC</th>
                        <th>Mittente</th>
                        <th>Oggetto</th>
                        <th>Data</th>
                        <th>Stato</th>
                        <th>Cliente</th>
                    </tr>
                </thead>
                <tbody>
                    {% for messaggio in messaggi %}
                    <tr data-href="{% url 'message_detail' messaggio.id %}" class="{% if messaggio.stato == 'DA_LEGGERE' %}message-unread{% endif %}">
                        <td>{{ messaggio.account_pec_info }}</td>
                        <td>{{ messaggio.mittente_info.nome }}</td>
                        <td>{{ messaggio.oggetto|truncatechars:60 }}</td>
                        <td>{{ messaggio.data_ricezione|date:"d/m/Y H:i" }}</td>
                        <td>
                            <span class="badge badge-{{ messaggio.stato|lower }}">
                                {{ messaggio.get_stato_display }}
                            </span>
                        </td>
                        <td>
                            {% if messaggio.cliente_info %}
                                {{ messaggio.cliente_info.nome }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                                <p class="mb-1">Nessun messaggio trovato</p>
                                <p class="small">Prova a modificare i filtri di ricerca</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if paginator.num_pages > 1 %}
<div class="mt-4 d-flex justify-content-between align-items-center">
    <div class="small text-muted">
        Mostrando {{ page_obj.start_index }}-{{ page_obj.end_index }} di {{ paginator.count }} messaggi
    </div>

    <nav aria-label="Page navigation">
        <ul class="pagination pagination-sm mb-0">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% for i in paginator.page_range %}
                {% if page_obj.number == i %}
                    <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ i }}">{{ i }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ paginator.num_pages }}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle filters
        const toggleFiltersBtn = document.getElementById('toggleFilters');
        const filtersCard = document.getElementById('filtersCard');

        if (toggleFiltersBtn && filtersCard) {
            // If there are active filters, show the filters card by default
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.toString() !== '') {
                filtersCard.style.display = 'block';
            }

            toggleFiltersBtn.addEventListener('click', function() {
                if (filtersCard.style.display === 'none' || filtersCard.style.display === '') {
                    filtersCard.style.display = 'block';
                } else {
                    filtersCard.style.display = 'none';
                }
            });
        }

        // Refresh button
        const refreshBtn = document.getElementById('refreshMessages');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                window.location.reload();
            });
        }

        // Click on table row to navigate to detail
        const rows = document.querySelectorAll('.messages-table tbody tr[data-href]');
        rows.forEach(row => {
            row.addEventListener('click', function() {
                window.location.href = this.dataset.href;
            });
        });
    });
</script>
{% endblock %}