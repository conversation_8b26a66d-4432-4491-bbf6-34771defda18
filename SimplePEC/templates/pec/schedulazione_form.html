{% extends 'base/base.html' %}

{% block title %}
{% if object %}Modifica Schedulazione - SimplePEC{% else %}Nuova Schedulazione - SimplePEC{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .form-group {
        margin-bottom: 1.5rem;
    }
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    .form-text {
        font-size: 0.85rem;
    }
    .conditional-field {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'schedulazione_list' %}">Schedulazioni</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                {% if object %}Modifica{% else %}Nuova{% endif %} Schedulazione
            </li>
        </ol>
    </nav>
</div>

<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">{% if object %}Modifica{% else %}Nuova{% endif %} Schedulazione</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.account_pec.id_for_label }}" class="form-label">Account PEC</label>
                        {{ form.account_pec }}
                        {% if form.account_pec.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.account_pec.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text text-muted">
                            Seleziona l'account PEC da sincronizzare automaticamente
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.frequenza.id_for_label }}" class="form-label">Frequenza</label>
                        {{ form.frequenza }}
                        {% if form.frequenza.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.frequenza.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text text-muted">
                            Seleziona la frequenza con cui eseguire la sincronizzazione
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.tipo_sincronizzazione.id_for_label }}" class="form-label">Tipo di sincronizzazione</label>
                        {{ form.tipo_sincronizzazione }}
                        {% if form.tipo_sincronizzazione.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.tipo_sincronizzazione.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text text-muted">
                            Scegli se sincronizzare solo i messaggi recenti o tutti i messaggi
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check form-switch mt-4">
                            {{ form.attiva }}
                            <label class="form-check-label" for="{{ form.attiva.id_for_label }}">
                                Schedulazione attiva
                            </label>
                        </div>
                        {% if form.attiva.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.attiva.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text text-muted">
                            Attiva o disattiva questa schedulazione
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 conditional-field" id="ora_inizio_container">
                    <div class="form-group">
                        <label for="{{ form.ora_inizio.id_for_label }}" class="form-label">Ora di inizio</label>
                        {{ form.ora_inizio }}
                        {% if form.ora_inizio.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.ora_inizio.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text text-muted">
                            Seleziona l'ora in cui eseguire la sincronizzazione
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 conditional-field" id="giorno_settimana_container">
                    <div class="form-group">
                        <label for="{{ form.giorno_settimana.id_for_label }}" class="form-label">Giorno della settimana</label>
                        {{ form.giorno_settimana }}
                        {% if form.giorno_settimana.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.giorno_settimana.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text text-muted">
                            Seleziona il giorno della settimana in cui eseguire la sincronizzazione
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'schedulazione_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Annulla
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Salva
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const frequenzaSelect = document.getElementById('{{ form.frequenza.id_for_label }}');
        const oraInizioContainer = document.getElementById('ora_inizio_container');
        const giornoSettimanaContainer = document.getElementById('giorno_settimana_container');
        
        // Funzione per aggiornare la visibilità dei campi condizionali
        function updateConditionalFields() {
            const frequenza = frequenzaSelect.value;
            
            // Mostra/nascondi il campo ora_inizio per frequenze giornaliere o settimanali
            if (frequenza === 'GIORNALIERA' || frequenza === 'SETTIMANALE') {
                oraInizioContainer.style.display = 'block';
            } else {
                oraInizioContainer.style.display = 'none';
            }
            
            // Mostra/nascondi il campo giorno_settimana solo per frequenza settimanale
            if (frequenza === 'SETTIMANALE') {
                giornoSettimanaContainer.style.display = 'block';
            } else {
                giornoSettimanaContainer.style.display = 'none';
            }
        }
        
        // Aggiorna i campi all'avvio
        updateConditionalFields();
        
        // Aggiorna i campi quando cambia la frequenza
        frequenzaSelect.addEventListener('change', updateConditionalFields);
    });
</script>
{% endblock %}
