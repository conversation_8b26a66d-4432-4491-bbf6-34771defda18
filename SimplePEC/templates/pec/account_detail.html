{% extends 'base/base.html' %}

{% block title %}{{ account.nome }} - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .account-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .status-badge {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .info-card {
        margin-bottom: 20px;
    }

    .info-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .info-title i {
        margin-right: 10px;
        color: #6b7280;
    }

    .info-item {
        display: flex;
        margin-bottom: 12px;
    }

    .info-label {
        width: 140px;
        font-weight: 500;
        color: #6b7280;
    }

    .info-value {
        flex-grow: 1;
    }

    .message-count {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .count-label {
        color: #6b7280;
        font-size: 14px;
    }

    .status-count {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .status-count-badge {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-count-value {
        font-weight: 500;
        margin-right: 5px;
    }

    .status-count-label {
        color: #6b7280;
        font-size: 14px;
    }

    .sync-info {
        font-size: 14px;
        color: #6b7280;
        margin-top: 5px;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
    }

    .loading-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .loading-content {
        text-align: center;
        color: white;
        background-color: rgba(0, 0, 0, 0.4);
        border-radius: 12px;
        padding: 30px;
        max-width: 400px;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .loading-message {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .loading-description {
        font-size: 14px;
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'account_list' %}">Account PEC</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ account.nome }}</li>
        </ol>
    </nav>

    <div class="actions">
        <a href="{% url 'message_list' %}?account={{ account.id }}" class="btn btn-outline-primary">
            <i class="fas fa-envelope me-1"></i> Vedi messaggi
        </a>
        <form method="post" action="{% url 'account_sync' account.id %}" class="d-inline">
            {% csrf_token %}
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-sync-alt me-1"></i> Sincronizza
            </button>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Account header -->
        <div class="account-header position-relative mb-4">
            <h1 class="fs-2 fw-bold mb-2">{{ account.nome }}</h1>
            <p class="mb-0">{{ account.indirizzo_email }}</p>

            <div class="status-badge">
                {% if account.attivo %}
                <span class="badge bg-success">Attivo</span>
                {% else %}
                <span class="badge bg-danger">Inattivo</span>
                {% endif %}
            </div>
        </div>

        <!-- Account details -->
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm info-card">
                    <div class="card-header bg-transparent">
                        <h5 class="info-title mb-0">
                            <i class="fas fa-server"></i> Impostazioni server in entrata
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="info-item">
                            <div class="info-label">Server IMAP</div>
                            <div class="info-value">{{ account.server_imap }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Porta</div>
                            <div class="info-value">{{ account.porta_imap }}</div>
                        </div>
                        <!-- Altre informazioni server IMAP -->
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 shadow-sm info-card">
                    <div class="card-header bg-transparent">
                        <h5 class="info-title mb-0">
                            <i class="fas fa-paper-plane"></i> Impostazioni server in uscita
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="info-item">
                            <div class="info-label">Server SMTP</div>
                            <div class="info-value">{{ account.server_smtp }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Porta</div>
                            <div class="info-value">{{ account.porta_smtp }}</div>
                        </div>
                        <!-- Altre informazioni server SMTP -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Authentication info -->
        <div class="card border-0 shadow-sm info-card">
            <div class="card-header bg-transparent">
                <h5 class="info-title mb-0">
                    <i class="fas fa-key"></i> Informazioni di autenticazione
                </h5>
            </div>
            <div class="card-body">
                <div class="info-item">
                    <div class="info-label">Username</div>
                    <div class="info-value">{{ account.username }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Password</div>
                    <div class="info-value">••••••••••••</div>
                </div>
            </div>
        </div>

        <!-- Authorized users -->
        <div class="card border-0 shadow-sm info-card">
            <div class="card-header bg-transparent">
                <h5 class="info-title mb-0">
                    <i class="fas fa-users"></i> Utenti autorizzati
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    {% for utente in account.utenti_autorizzati.all %}
                    <li class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <i class="fas fa-user text-secondary"></i>
                            </div>
                            <div>
                                {{ utente.get_full_name|default:utente.username }}
                            </div>
                        </div>
                    </li>
                    {% empty %}
                    <li class="list-group-item px-0 py-2 text-muted">
                        <em>Nessun utente autorizzato</em>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Account statistics -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fw-semibold">Statistiche account</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="message-count">{{ account.messaggiopec_set.count }}</div>
                    <div class="count-label">Messaggi totali</div>
                </div>

                <div class="mb-4">
                    <div class="status-count">
                        <div class="status-count-badge bg-danger"></div>
                        <div class="status-count-value">0</div>
                        <div class="status-count-label">Da leggere</div>
                    </div>

                    <div class="status-count">
                        <div class="status-count-badge bg-warning"></div>
                        <div class="status-count-value">0</div>
                        <div class="status-count-label">Da assegnare</div>
                    </div>

                    <div class="status-count">
                        <div class="status-count-badge bg-primary"></div>
                        <div class="status-count-value">0</div>
                        <div class="status-count-label">Assegnati</div>
                    </div>

                    <div class="status-count">
                        <div class="status-count-badge bg-success"></div>
                        <div class="status-count-value">0</div>
                        <div class="status-count-label">Lavorati</div>
                    </div>
                </div>

                <div>
                    <div class="fw-semibold mb-2">Ultima sincronizzazione</div>
                    {% if account.ultima_sincronizzazione %}
                    <div class="text-center py-3">
                        <div class="fs-5 mb-1">{{ account.ultima_sincronizzazione|date:"d/m/Y" }}</div>
                        <div class="fs-6">{{ account.ultima_sincronizzazione|date:"H:i:s" }}</div>
                        <div class="sync-info">{{ account.ultima_sincronizzazione|timesince }} fa</div>
                    </div>
                    {% else %}
                    <div class="text-center py-3 text-muted">
                        <em>Mai sincronizzato</em>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <form method="post" action="{% url 'account_sync' account.id %}" class="mb-2">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-sync-alt me-1"></i> Sincronizza messaggi recenti
                    </button>
                </form>
                <form method="post" action="{% url 'account_sync' account.id %}">
                    {% csrf_token %}
                    <input type="hidden" name="force_all" value="1">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-sync-alt me-1"></i> Sincronizza tutti i messaggi
                    </button>
                </form>
            </div>
        </div>

        <!-- Account actions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fw-semibold">Azioni account</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'message_list' %}?account={{ account.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i> Visualizza messaggi
                    </a>

                    <!-- Test connection button -->
                    <form method="post" action="{% url 'account_test_connection' account.id %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plug me-1"></i> Testa connessione
                        </button>
                    </form>

                    {% if request.user.is_superuser or is_manager %}
                    <!-- Edit account button (solo per Manager e admin) -->
                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#editAccountModal">
                        <i class="fas fa-edit me-1"></i> Modifica account
                    </button>

                    <!-- Toggle status button (solo per Manager e admin) -->
                    <form method="post" action="{% url 'account_toggle_status' account.id %}">
                        {% csrf_token %}
                        {% if account.attivo %}
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-ban me-1"></i> Disattiva
                        </button>
                        {% else %}
                        <button type="submit" class="btn btn-outline-success w-100">
                            <i class="fas fa-check me-1"></i> Attiva
                        </button>
                        {% endif %}
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Account Modal -->
<div class="modal fade" id="editAccountModal" tabindex="-1" aria-labelledby="editAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAccountModalLabel">Modifica account PEC</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'account_update' account.id %}" id="editAccountForm">
                    {% csrf_token %}

                    <!-- Account details -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informazioni account</h6>

                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome account</label>
                            <input type="text" class="form-control" id="nome" name="nome" required value="{{ account.nome }}">
                            <div class="form-text">Un nome identificativo per questo account</div>
                        </div>

                        <div class="mb-3">
                            <label for="indirizzo_email" class="form-label">Indirizzo email PEC</label>
                            <input type="email" class="form-control" id="indirizzo_email" name="indirizzo_email" required value="{{ account.indirizzo_email }}">
                        </div>

                        <div class="mb-3">
                            <label for="organizzazione" class="form-label">Organizzazione</label>
                            {% if request.user.is_superuser %}
                            <!-- Admin può selezionare qualsiasi organizzazione -->
                            <select class="form-select" id="organizzazione" name="organizzazione">
                                <option value="">-- Nessuna organizzazione --</option>
                                {% for org in organizzazioni %}
                                <option value="{{ org.id }}" {% if account.organizzazione and account.organizzazione.id == org.id %}selected{% endif %}>{{ org.ragione_sociale }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Seleziona l'organizzazione a cui appartiene questo account PEC</div>
                            {% elif is_manager and request.user.organizzazione %}
                            <!-- Manager vede solo la sua organizzazione (non modificabile) -->
                            <input type="text" class="form-control" value="{{ request.user.organizzazione.ragione_sociale }}" readonly>
                            <input type="hidden" name="organizzazione" value="{{ request.user.organizzazione.id }}">
                            <div class="form-text">L'account è associato alla tua organizzazione</div>
                            {% else %}
                            <!-- Nessuna organizzazione disponibile -->
                            <input type="text" class="form-control" value="{% if account.organizzazione %}{{ account.organizzazione.ragione_sociale }}{% else %}Nessuna organizzazione{% endif %}" readonly disabled>
                            <div class="form-text">Non puoi modificare l'organizzazione</div>
                            {% endif %}
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="attivo" name="attivo" {% if account.attivo %}checked{% endif %}>
                            <label class="form-check-label" for="attivo">Account attivo</label>
                            <div class="form-text">Gli account inattivi non verranno sincronizzati</div>
                        </div>
                    </div>

                    <!-- Server settings -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Impostazioni server</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="server_imap" class="form-label">Server IMAP</label>
                                    <input type="text" class="form-control" id="server_imap" name="server_imap" required value="{{ account.server_imap }}">
                                </div>

                                <div class="mb-3">
                                    <label for="porta_imap" class="form-label">Porta IMAP</label>
                                    <input type="number" class="form-control" id="porta_imap" name="porta_imap" required value="{{ account.porta_imap }}">
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="ssl_imap" name="ssl_imap" checked>
                                    <label class="form-check-label" for="ssl_imap">Usa SSL/TLS</label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="server_smtp" class="form-label">Server SMTP</label>
                                    <input type="text" class="form-control" id="server_smtp" name="server_smtp" required value="{{ account.server_smtp }}">
                                </div>

                                <div class="mb-3">
                                    <label for="porta_smtp" class="form-label">Porta SMTP</label>
                                    <input type="number" class="form-control" id="porta_smtp" name="porta_smtp" required value="{{ account.porta_smtp }}">
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="ssl_smtp" name="ssl_smtp" checked>
                                    <label class="form-check-label" for="ssl_smtp">Usa SSL/TLS</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Authentication -->
                    <div>
                        <h6 class="fw-bold mb-3">Autenticazione</h6>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required value="{{ account.username }}">
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Lascia vuoto per mantenere la password attuale">
                            <div class="form-text">Compila solo se vuoi cambiare la password attuale</div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('editAccountForm').submit()">
                    <i class="fas fa-save me-1"></i> Salva modifiche
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-message" id="loadingMessage">Operazione in corso...</div>
        <div class="loading-description" id="loadingDescription">Attendere il completamento dell'operazione.</div>
    </div>
</div>

<!-- JavaScript per gestire l'overlay di caricamento -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ottiene i riferimenti agli elementi
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingDescription = document.getElementById('loadingDescription');

        // Funzione per mostrare l'overlay
        function showLoading(message, description) {
            loadingMessage.textContent = message || 'Operazione in corso...';
            loadingDescription.textContent = description || 'Attendere il completamento dell\'operazione.';
            loadingOverlay.classList.add('active');
        }

        // Aggiungi ID ai form per poterli selezionare
        document.querySelectorAll('form[action*="sync"]').forEach((form, index) => {
            // Controlla se il form contiene un input con name="force_all"
            const isSyncAll = form.querySelector('input[name="force_all"]') !== null;
            form.id = isSyncAll ? 'syncAllForm' : 'syncRecentForm';

            // Aggiungi event listener per mostrare l'overlay di caricamento
            if (isSyncAll) {
                form.addEventListener('submit', function() {
                    showLoading('Sincronizzazione completa in corso...', 'Stiamo recuperando tutti i messaggi dalla casella PEC. Questa operazione potrebbe richiedere diversi minuti, a seconda del numero di messaggi presenti.');
                });
            } else {
                form.addEventListener('submit', function() {
                    showLoading('Sincronizzazione messaggi recenti in corso...', 'Stiamo recuperando i messaggi non letti e recenti dalla casella PEC. Questa operazione potrebbe richiedere alcuni minuti.');
                });
            }
        });

        // Aggiungi ID al form di test connessione
        document.querySelector('form[action*="test-connection"]').id = 'testConnectionForm';

        // Aggiungi event listener per mostrare l'overlay di caricamento
        document.getElementById('testConnectionForm').addEventListener('submit', function() {
            showLoading('Test di connessione in corso...', 'Stiamo verificando la connessione al server PEC usando le credenziali configurate.');
        });
    });
</script>
{% endblock %}