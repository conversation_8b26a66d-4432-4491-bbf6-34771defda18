{% extends 'base/base.html' %}

{% block title %}Dashboard - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-radius: 10px;
        transition: transform 0.2s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .stats-card h2 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stats-card p {
        margin-bottom: 0;
        color: #6b7280;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .table-message-preview tbody tr {
        cursor: pointer;
        transition: background-color 0.15s;
    }

    .table-message-preview tbody tr:hover {
        background-color: #f9fafb;
    }

    .badge-da_leggere {
        background-color: #ef4444;
        color: white;
    }

    .badge-da_assegnare {
        background-color: #f59e0b;
        color: white;
    }

    .badge-assegnato {
        background-color: #3b82f6;
        color: white;
    }

    .badge-lavorato {
        background-color: #10b981;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Dashboard</h1>
    <button class="btn btn-sm btn-outline-primary" id="refreshDashboard">
        <i class="fas fa-sync-alt me-1"></i> Aggiorna
    </button>
</div>

<!-- Stats cards -->
<div class="row g-3 mb-4">
    <div class="col-md-6 col-lg-4">
        <div class="stats-card card h-100 border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="stats-icon bg-danger-subtle text-danger">
                    <i class="fas fa-envelope"></i>
                </div>
                <div>
                    <h2>{{ conteggi.da_leggere }}</h2>
                    <p>Da leggere</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-4">
        <div class="stats-card card h-100 border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="stats-icon bg-warning-subtle text-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div>
                    <h2>{{ conteggi.da_assegnare }}</h2>
                    <p>Da assegnare</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-4">
        <div class="stats-card card h-100 border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="stats-icon bg-primary-subtle text-primary">
                    <i class="fas fa-user-check"></i>
                </div>
                <div>
                    <h2>{{ conteggi.assegnato }}</h2>
                    <p>Assegnato</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Charts -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">Andamento messaggi</h5>
                <div class="chart-filters">
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-secondary period-selector" data-period="7">7 giorni</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary period-selector" data-period="30">30 giorni</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary period-selector" data-period="90">3 mesi</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary period-selector" data-period="180">6 mesi</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary period-selector" data-period="365">1 anno</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="messagesTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Account PEC stats -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fw-semibold">Account PEC</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 250px;">
                    <canvas id="accountsChart"></canvas>
                </div>

                <hr>

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Account</th>
                                <th class="text-end">Messaggi</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in per_account %}
                            <tr>
                                <td>{{ account.nome }}</td>
                                <td class="text-end">{{ account.totale_messaggi }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent messages -->
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">Messaggi recenti</h5>
                <a href="{% url 'message_list' %}" class="btn btn-sm btn-outline-primary">Vedi tutti</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-message-preview align-middle">
                        <thead>
                            <tr>
                                <th>Account PEC</th>
                                <th>Mittente</th>
                                <th>Oggetto</th>
                                <th>Data</th>
                                <th>Stato</th>
                                <th>Cliente</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for messaggio in messaggi_recenti %}
                            <tr data-href="{% url 'message_detail' messaggio.id %}">
                                <td>{{ messaggio.account_pec_info }}</td>
                                <td>{{ messaggio.mittente_info.nome }}</td>
                                <td>
                                    {% if messaggio.stato == 'DA_LEGGERE' %}<span class="fw-medium">{% endif %}
                                    {{ messaggio.oggetto|truncatechars:50 }}
                                    {% if messaggio.stato == 'DA_LEGGERE' %}</span>{% endif %}
                                </td>
                                <td>{{ messaggio.data_ricezione|date:"d/m/Y H:i" }}</td>
                                <td>
                                    <span class="badge badge-{{ messaggio.stato|lower }}">
                                        {{ messaggio.get_stato_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if messaggio.cliente_info %}
                                        {{ messaggio.cliente_info.nome }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-2x mb-3 opacity-50"></i>
                                        <p>Nessun messaggio recente</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Click on table row to navigate to detail
        const rows = document.querySelectorAll('.table-message-preview tbody tr[data-href]');
        rows.forEach(row => {
            row.addEventListener('click', function() {
                window.location.href = this.dataset.href;
            });
        });

        // Refresh button
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                window.location.reload();
            });
        }

        // Load charts
        loadCharts();

        // Imposta periodo di default a 7 giorni
        document.querySelector('.period-selector[data-period="7"]').classList.add('active');

        // Assicurati che il grafico si aggiorni anche alla prima visualizzazione
        const defaultPeriodBtn = document.querySelector('.period-selector[data-period="7"]');
        if (defaultPeriodBtn) {
            setTimeout(() => {
                defaultPeriodBtn.click();
            }, 100);
        }
    });

    function loadCharts() {
        // Dati trend con tutti i periodi supportati
        const allTrendData = {
            {% for period, data in trend_data.items %}
            '{{ period }}': {
                labels: [{% for item in data %}"{{ item.label }}",{% endfor %}],
                datasets: [
                    {
                        label: 'Totale',
                        data: [{% for item in data %}{{ item.count }},{% endfor %}],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: 'Da leggere',
                        data: [{% for item in data %}{{ item.da_leggere }},{% endfor %}],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: 'Da assegnare',
                        data: [{% for item in data %}{{ item.da_assegnare }},{% endfor %}],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: 'Assegnato',
                        data: [{% for item in data %}{{ item.assegnato }},{% endfor %}],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: 'Lavorato',
                        data: [{% for item in data %}{{ item.lavorato }},{% endfor %}],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.3,
                        fill: true,
                        hidden: true
                    }
                ]
            },
            {% endfor %}
        };

        // Creiamo un oggetto vuoto come base, che verrà aggiornato dal selettore di periodo
        const messagesTrendData = {
            labels: [],
            datasets: [{
                label: 'Messaggi',
                data: [],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.3,
                fill: true
            }]
        };

        const accountsData = {
            labels: [],
            datasets: [
                {
                    data: [],
                    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
                    borderWidth: 0
                }
            ]
        };

        // Popoliamo i grafici con dati reali degli account
        {% if per_account %}
        // Dati per il grafico degli account
        accountsData.labels = [{% for account in per_account %}"{{ account.nome }}",{% endfor %}];
        accountsData.datasets[0].data = [{% for account in per_account %}{{ account.totale_messaggi }},{% endfor %}];
        {% endif %}

        // Messages trend chart
        let trendChart = null;
        const trendCtx = document.getElementById('messagesTrendChart');
        if (trendCtx) {
            trendChart = new Chart(trendCtx, {
                type: 'line',
                data: messagesTrendData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gestione click sui selettori di periodo
            document.querySelectorAll('.period-selector').forEach(button => {
                button.addEventListener('click', function() {
                    // Rimuovi classe active da tutti i bottoni
                    document.querySelectorAll('.period-selector').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Aggiungi classe active al bottone cliccato
                    this.classList.add('active');

                    // Aggiorna il grafico con i dati del periodo selezionato
                    const period = this.getAttribute('data-period');
                    const newData = allTrendData[period];

                    if (newData) {
                        trendChart.data.labels = newData.labels;
                        trendChart.data.datasets = newData.datasets;
                        trendChart.update();
                    }
                });
            });
        }

        // Accounts chart
        const accountsCtx = document.getElementById('accountsChart');
        if (accountsCtx) {
            new Chart(accountsCtx, {
                type: 'doughnut',
                data: accountsData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    },
                    cutout: '65%'
                }
            });
        }
    }
</script>
{% endblock %}