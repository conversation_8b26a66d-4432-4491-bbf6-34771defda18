<aside class="sidebar">
    <div class="sidebar-header">
        <div class="app-brand">
            <a href="{% url 'dashboard' %}" class="app-logo">
                <i class="fas fa-envelope-open-text app-logo-icon"></i>
                <span class="app-logo-text">SimplePEC</span>
            </a>
        </div>
    </div>

    <div class="sidebar-user">
        {% if request.user.organizzazione %}
        <div class="sidebar-organization-info">
            <div class="sidebar-organization-logo">
                {% if request.user.organizzazione.logo %}
                    <img src="{{ request.user.organizzazione.logo.url }}" alt="{{ request.user.organizzazione.ragione_sociale }}">
                {% else %}
                    <div class="organization-logo-placeholder">
                        <i class="fas fa-building"></i>
                    </div>
                {% endif %}
            </div>
            <div class="sidebar-organization-details">
                <div class="sidebar-organization-name">{{ request.user.organizzazione.ragione_sociale }}</div>
            </div>
        </div>
        {% else %}
        <!-- Box vuoto per mantenere lo spazio -->
        <div class="sidebar-empty-box"></div>
        {% endif %}
    </div>

    <div class="sidebar-menu-container">
        <h6 class="sidebar-heading">PRINCIPALE</h6>
        <nav class="sidebar-nav">
            <a href="{% url 'dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <span class="nav-link-text">Dashboard</span>
            </a>

            <a href="{% url 'message_list' %}" class="nav-link {% if request.resolver_match.url_name == 'message_list' or request.resolver_match.url_name == 'message_detail' %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-inbox"></i>
                </div>
                <span class="nav-link-text">Messaggi in Entrata</span>
                {% load pec_extras %}
                {% with unread_count=request.user.account_pec_autorizzati.all|unread_messages_count %}
                {% if unread_count > 0 %}
                <span class="nav-link-badge badge rounded-pill bg-primary">Nuovo</span>
                {% endif %}
                {% endwith %}
            </a>

            <a href="{% url 'outbox_list' %}" class="nav-link {% if request.resolver_match.url_name == 'outbox_list' %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <span class="nav-link-text">Messaggi in Uscita</span>
            </a>

            <h6 class="sidebar-heading">GESTIONE</h6>

            <a href="{% url 'account_list' %}" class="nav-link {% if 'account' in request.resolver_match.url_name %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <span class="nav-link-text">Account PEC</span>
            </a>

            <a href="{% url 'cliente_list' %}" class="nav-link {% if 'cliente' in request.resolver_match.url_name %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-users"></i>
                </div>
                <span class="nav-link-text">Clienti</span>
            </a>

            {% if request.user.is_staff or request.user.is_manager %}
            <h6 class="sidebar-heading">AMMINISTRAZIONE</h6>

            <a href="{% url 'user_list' %}" class="nav-link {% if 'user' in request.resolver_match.url_name %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <span class="nav-link-text">Utenti</span>
            </a>

            <a href="{% url 'organizzazione_list' %}" class="nav-link {% if 'organizzazione' in request.resolver_match.url_name %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-building"></i>
                </div>
                <span class="nav-link-text">Organizzazioni</span>
            </a>

            {% if request.user.is_superuser or request.user.is_manager %}
            <a href="{% url 'schedulazione_list' %}" class="nav-link {% if 'schedulazione' in request.resolver_match.url_name %}active{% endif %}">
                <div class="nav-link-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <span class="nav-link-text">Schedulazioni</span>
            </a>
            {% endif %}

            {% if request.user.is_staff %}
            <a href="{% url 'admin:index' %}" class="nav-link">
                <div class="nav-link-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <span class="nav-link-text">Configurazione</span>
            </a>
            {% endif %}
            {% endif %}
        </nav>
    </div>

    <div class="sidebar-footer">
        <div class="sidebar-footer-content">
            <div class="app-version">
                <i class="fas fa-code-branch me-1"></i>
                <span>v1.0.0</span>
            </div>
            <div class="sidebar-actions">
                <a href="{% url 'documentation' %}" class="sidebar-action-btn" title="Documentazione">
                    <i class="fas fa-question-circle"></i>
                </a>
            </div>
        </div>
    </div>
</aside>