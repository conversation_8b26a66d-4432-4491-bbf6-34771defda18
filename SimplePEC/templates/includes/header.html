{% load static %}
<header class="header">
    <div class="header-left">
        <button class="sidebar-toggle" aria-label="Toggle sidebar">
            <i class="fas fa-bars"></i>
        </button>
        <a href="{% url 'dashboard' %}" class="logo">
            <i class="fas fa-envelope-open-text logo-icon"></i>
            <span class="logo-text">SimplePEC</span>
        </a>
    </div>
    <div class="header-center">
        <!-- Menu orizzontale rimosso come richiesto -->
    </div>
    <div class="header-right">
        {% if request.user.is_authenticated %}
            <div class="header-actions">
                <!-- Ricerca rapida -->
                <div class="quick-search-container">
                    <button class="header-action-btn search-btn" id="searchBtn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <!-- Form di ricerca che appare con animazione -->
                <div class="search-overlay" id="searchOverlay">
                    <div class="search-overlay-content">
                        <form action="{% url 'message_list' %}" method="get" id="quickSearchForm">
                            <div class="search-form-container">
                                <input type="text" class="search-input" id="quickSearchInput" name="search" placeholder="Cerca nei messaggi..." autocomplete="off">
                                <button type="submit" class="search-submit-btn">
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                                <button type="button" class="search-close-btn" id="searchCloseBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Notifiche -->
                <div class="dropdown">
                    <button class="header-action-btn notification-btn" id="notificationsBtn" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        {% load pec_extras %}
                        {% with unread_count=request.user.account_pec_autorizzati.all|unread_messages_count %}
                        {% if unread_count > 0 %}
                        <span class="notification-indicator"></span>
                        {% endif %}
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-dropdown shadow-sm p-0">
                        <div class="notification-header p-3 border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Notifiche</h6>
                                {% if unread_count > 0 %}
                                <span class="badge bg-primary rounded-pill">{{ unread_count }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="notification-body">
                            {% if unread_count > 0 %}
                                <div class="p-3">
                                    <a href="{% url 'message_list' %}?stato=DA_LEGGERE" class="notification-item d-flex align-items-center">
                                        <div class="notification-icon bg-primary text-white">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="notification-content ms-3">
                                            <div class="notification-title">Messaggi da leggere</div>
                                            <div class="notification-text text-muted small">Hai {{ unread_count }} messaggi da leggere</div>
                                        </div>
                                    </a>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-check-circle fa-3x mb-3 opacity-50"></i>
                                        <p>Nessuna notifica</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        <div class="notification-footer p-2 border-top text-center">
                            <a href="{% url 'message_list' %}" class="small">Vedi tutti i messaggi</a>
                        </div>
                    </div>
                    {% endwith %}
                </div>
            </div>
            <div class="dropdown ms-3">
                <div class="user-menu" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="avatar">
                        {% if request.user.immagine_profilo %}
                            <img src="{{ request.user.immagine_profilo.url }}" alt="{{ request.user.get_full_name }}" style="width: 100%; height: 100%; object-fit: cover;">
                        {% else %}
                            {{ request.user.first_name|slice:"0:1" }}{{ request.user.last_name|slice:"0:1" }}
                        {% endif %}
                    </div>
                    <div class="user-info d-none d-md-block">
                        <div class="user-name">{{ request.user.first_name }} {{ request.user.last_name }}</div>
                        <div class="user-role text-muted small d-flex align-items-center">
                            {% if request.user.is_staff %}Amministratore{% else %}Utente{% endif %}
                            {% if request.user.organizzazione %}
                            <span class="mx-1">•</span>
                            <span>{{ request.user.organizzazione.ragione_sociale }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <i class="fas fa-chevron-down ms-2 text-muted small"></i>
                </div>
                <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                    <li>
                        <div class="dropdown-user-details p-3 border-bottom">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md me-3">
                                    {% if request.user.immagine_profilo %}
                                        <img src="{{ request.user.immagine_profilo.url }}" alt="{{ request.user.get_full_name }}" style="width: 100%; height: 100%; object-fit: cover;">
                                    {% else %}
                                        {{ request.user.first_name|slice:"0:1" }}{{ request.user.last_name|slice:"0:1" }}
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ request.user.get_full_name }}</div>
                                    <div class="text-muted small">{{ request.user.email }}</div>
                                    {% if request.user.organizzazione %}
                                    <div class="mt-1 d-flex align-items-center">
                                        {% if request.user.organizzazione.logo %}
                                        <img src="{{ request.user.organizzazione.logo.url }}" alt="{{ request.user.organizzazione.ragione_sociale }}" class="me-1" style="max-height: 16px; max-width: 16px;">
                                        {% endif %}
                                        <span class="small">{{ request.user.organizzazione.ragione_sociale }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </li>
                    <li><a class="dropdown-item" href="{% url 'user_detail' request.user.id %}"><i class="fas fa-user-circle me-2"></i> Profilo</a></li>
                    <li><a class="dropdown-item" href="{% url 'account_list' %}"><i class="fas fa-envelope me-2"></i> Account PEC</a></li>
                    {% if request.user.is_staff %}
                    <li><a class="dropdown-item" href="{% url 'admin:index' %}"><i class="fas fa-shield-alt me-2"></i> Amministrazione</a></li>
                    <li><a class="dropdown-item" href="{% url 'user_list' %}"><i class="fas fa-users-cog me-2"></i> Gestione Utenti</a></li>
                    {% endif %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="post" action="{% url 'logout' %}">
                            {% csrf_token %}
                            <button type="submit" class="dropdown-item"><i class="fas fa-sign-out-alt me-2"></i> Logout</button>
                        </form>
                    </li>
                </ul>
            </div>
        {% else %}
            <a href="{% url 'login' %}" class="btn btn-primary btn-sm">Accedi</a>
        {% endif %}
    </div>
</header>