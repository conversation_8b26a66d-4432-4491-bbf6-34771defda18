{% extends 'base/base.html' %}
{% load static %}

{% block title %}Elimina Organizzazione - SimplePEC{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Elimina Organizzazione</h1>
        <a href="{% url 'organizzazione_detail' object.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i> Annulla
        </a>
    </div>

    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4 text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-danger fa-4x mb-3"></i>
                        <h4>Sei sicuro di voler eliminare questa organizzazione?</h4>
                        <p class="text-muted">
                            Stai per eliminare l'organizzazione <strong>{{ object.ragione_sociale }}</strong>.<br>
                            Questa azione non può essere annullata.
                        </p>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Attenzione:</strong> Eliminando questa organizzazione, gli utenti e gli account PEC associati rimarranno nel sistema ma non saranno più collegati a nessuna organizzazione.
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-center mt-4">
                            <a href="{% url 'organizzazione_detail' object.id %}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times me-2"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i> Elimina
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
