{% extends 'base/base.html' %}

{% block title %}Utenti - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .user-card {
        border-radius: 12px;
        transition: transform 0.2s;
        overflow: hidden;
    }

    .user-card:hover {
        transform: translateY(-3px);
    }

    .user-header {
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
    }

    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: 600;
        margin-right: 15px;
    }

    .user-info {
        flex-grow: 1;
    }

    .user-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .user-username {
        color: #6b7280;
        font-size: 14px;
    }

    .user-badges {
        display: flex;
        gap: 8px;
        margin-top: 8px;
    }

    .user-badge {
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .badge-admin {
        background-color: #fee2e2;
        color: #ef4444;
    }

    .badge-staff {
        background-color: #fef3c7;
        color: #f59e0b;
    }

    .badge-active {
        background-color: #dcfce7;
        color: #10b981;
    }

    .user-accounts {
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .user-section-title {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #4b5563;
    }

    .account-chip {
        display: inline-flex;
        align-items: center;
        background-color: #f3f4f6;
        border-radius: 16px;
        padding: 5px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        font-size: 13px;
    }

    .account-chip i {
        margin-right: 6px;
        color: #6b7280;
    }

    .user-actions {
        padding: 15px 20px;
        background-color: #f9fafb;
    }

    .user-add-card {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 260px;
        cursor: pointer;
        transition: border-color 0.2s, background-color 0.2s;
    }

    .user-add-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }

    .add-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Utenti</h1>
    <div>
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#newUserModal">
            <i class="fas fa-plus me-1"></i> Nuovo utente
        </button>
    </div>
</div>

<div class="row g-4">
    {% for user in users %}
    <div class="col-md-6 col-lg-4">
        <div class="card user-card border-0 shadow-sm h-100">
            <div class="user-header">
                <div class="user-avatar">
                    {{ user.first_name|slice:"0:1" }}{{ user.last_name|slice:"0:1" }}
                </div>
                <div class="user-info">
                    <h3 class="user-name">{{ user.first_name }} {{ user.last_name }}</h3>
                    <div class="user-username">@{{ user.username }}</div>
                    <div class="user-badges">
                        {% if user.is_superuser %}
                        <span class="user-badge badge-admin">Admin</span>
                        {% elif user.is_staff %}
                        <span class="user-badge badge-staff">Staff Django</span>
                        {% endif %}

                        {% if user.is_manager %}
                        <span class="user-badge" style="background-color: #dbeafe; color: #2563eb;">Manager</span>
                        {% elif user.is_staff_user %}
                        <span class="user-badge" style="background-color: #fef9c3; color: #ca8a04;">Staff</span>
                        {% endif %}

                        {% if user.is_active %}
                        <span class="user-badge badge-active">Attivo</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="user-accounts">
                <h4 class="user-section-title">Account PEC autorizzati</h4>
                {% for account in user.account_pec_autorizzati.all %}
                <div class="account-chip">
                    <i class="fas fa-envelope"></i>
                    {{ account.nome }}
                </div>
                {% empty %}
                <div class="text-muted small">Nessun account PEC autorizzato</div>
                {% endfor %}
            </div>

            <div class="user-actions">
                <div class="btn-group w-100" role="group">
                    {% if request.user.is_staff or request.user.is_manager %}
                    <a href="{% url 'user_detail' user.id %}" class="btn btn-primary w-100">
                        <i class="fas fa-cog"></i> Gestisci
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-user-shield fa-3x mb-3 opacity-50"></i>
                <p class="mb-1">Nessun utente configurato</p>
                <p class="small">Clicca su "Nuovo utente" per aggiungerne uno</p>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Add user card (solo per admin) -->
    {% if request.user.is_staff %}
    <div class="col-md-6 col-lg-4">
        <div class="user-add-card" data-bs-toggle="modal" data-bs-target="#newUserModal">
            <div class="add-icon">
                <i class="fas fa-plus"></i>
            </div>
            <h3 class="fs-5 mb-1">Aggiungi utente</h3>
            <p class="text-muted small">Crea un nuovo account utente</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- New User Modal -->
<div class="modal fade" id="newUserModal" tabindex="-1" aria-labelledby="newUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newUserModalLabel">Nuovo utente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'user_create' %}" id="newUserForm">
                    {% csrf_token %}

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informazioni utente</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">Nome</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Cognome</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informazioni di accesso</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            L'username sarà impostato automaticamente con l'indirizzo email.<br>
                            Una password temporanea verrà generata e inviata all'indirizzo email specificato.<br>
                            Al primo accesso, l'utente dovrà cambiare la password.
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Permessi</h6>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">Utente attivo</label>
                        </div>

                        <!-- Opzione Staff nascosta e sempre impostata a False -->
                        <input type="hidden" id="is_staff" name="is_staff" value="False">

                        <div class="mb-3">
                            <label for="user_role" class="form-label">Ruolo utente <span class="text-danger">*</span></label>
                            <select class="form-select" id="user_role" name="user_role" required>
                                <option value="">-- Seleziona ruolo --</option>
                                <option value="manager">Manager</option>
                                <option value="staff">Staff</option>
                            </select>
                            <div class="form-text">
                                <strong>Manager:</strong> accesso completo ai dati della propria organizzazione, può gestire account PEC, clienti e utenti.<br>
                                <strong>Staff:</strong> accesso limitato solo agli account e clienti assegnati, con permessi di sola lettura per la maggior parte delle funzionalità.
                            </div>
                        </div>
                    </div>

                    <!-- Il campo organizzazione è nascosto e verrà impostato automaticamente con l'organizzazione dell'utente corrente -->
                    <input type="hidden" id="organizzazione" name="organizzazione" value="{% if user.organizzazione %}{{ user.organizzazione.id }}{% endif %}">

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="submit" form="newUserForm" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Salva utente
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inizializza i tooltip
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Gestione del form di creazione utente
        const newUserForm = document.getElementById('newUserForm');
        if (newUserForm) {
            newUserForm.addEventListener('submit', function(event) {
                // Verifica che il ruolo sia selezionato
                const userRole = document.getElementById('user_role').value;
                if (!userRole) {
                    event.preventDefault();
                    alert('È necessario selezionare un ruolo (Manager o Staff)');
                    return false;
                }

                // Verifica che tutti i campi obbligatori siano compilati
                const requiredFields = ['first_name', 'last_name', 'email'];
                for (const field of requiredFields) {
                    const input = document.getElementById(field);
                    if (!input.value.trim()) {
                        event.preventDefault();
                        alert('Tutti i campi obbligatori devono essere compilati');
                        input.focus();
                        return false;
                    }
                }

                // Se tutto è ok, il form viene inviato
                return true;
            });
        }
    });
</script>
{% endblock %}