{% extends 'base/base.html' %}

{% block title %}Modifica Profilo - {{ user_obj.get_full_name }} - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 25px;
        text-align: center;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40px;
        font-weight: 600;
        margin: 0 auto 15px;
        position: relative;
        overflow: hidden;
    }

    .profile-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .profile-name {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .profile-username {
        color: #6b7280;
        font-size: 16px;
        margin-bottom: 15px;
    }

    .form-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin-bottom: 25px;
    }

    .form-section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e5e7eb;
    }

    .password-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 25px;
    }

    .avatar-upload {
        position: relative;
        margin: 0 auto;
        width: 120px;
    }

    .avatar-edit {
        position: absolute;
        right: 5px;
        bottom: 5px;
        z-index: 1;
    }

    .avatar-edit input {
        display: none;
    }

    .avatar-edit label {
        display: inline-block;
        width: 34px;
        height: 34px;
        border-radius: 50%;
        background: #3b82f6;
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        font-weight: normal;
        transition: all 0.2s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .avatar-edit label:hover {
        background: #2563eb;
    }

    .avatar-preview {
        width: 120px;
        height: 120px;
        position: relative;
        border-radius: 50%;
        overflow: hidden;
        background-color: #eff6ff;
    }

    .avatar-preview > div {
        width: 100%;
        height: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'user_list' %}">Utenti</a></li>
            <li class="breadcrumb-item"><a href="{% url 'user_detail' user_obj.id %}">{{ user_obj.username }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Modifica</li>
        </ol>
    </nav>
</div>

<div class="profile-header">
    <div class="avatar-upload">
        <div class="avatar-preview">
            {% if user_obj.immagine_profilo %}
                <div style="background-image: url('{{ user_obj.immagine_profilo.url }}');"></div>
            {% else %}
                <div class="profile-avatar">
                    {{ user_obj.first_name|slice:"0:1" }}{{ user_obj.last_name|slice:"0:1" }}
                </div>
            {% endif %}
        </div>
    </div>
    <h1 class="profile-name">{{ user_obj.get_full_name }}</h1>
    <div class="profile-username">@{{ user_obj.username }}</div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="form-section">
            <h2 class="form-section-title">Informazioni personali</h2>

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="text-danger">{{ form.first_name.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="text-danger">{{ form.last_name.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="mb-3">
                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="text-danger">{{ form.email.errors }}</div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.telefono.id_for_label }}" class="form-label">{{ form.telefono.label }}</label>
                    {{ form.telefono }}
                    {% if form.telefono.errors %}
                        <div class="text-danger">{{ form.telefono.errors }}</div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.immagine_profilo.id_for_label }}" class="form-label">{{ form.immagine_profilo.label }}</label>
                    {{ form.immagine_profilo }}
                    {% if form.immagine_profilo.errors %}
                        <div class="text-danger">{{ form.immagine_profilo.errors }}</div>
                    {% endif %}
                    <div class="form-text">Carica un'immagine per il tuo profilo (formato consigliato: quadrato)</div>
                </div>

                {% if request.user.is_staff %}
                <div class="mb-3">
                    <label for="organizzazione" class="form-label">Organizzazione</label>
                    <select class="form-select" id="organizzazione" name="organizzazione">
                        <option value="">-- Nessuna organizzazione --</option>
                        {% for org in organizzazioni %}
                        <option value="{{ org.id }}" {% if user_obj.organizzazione and user_obj.organizzazione.id == org.id %}selected{% endif %}>{{ org.ragione_sociale }}</option>
                        {% endfor %}
                    </select>
                    <div class="form-text">Seleziona l'organizzazione a cui appartiene questo utente</div>
                </div>
                {% endif %}

                {% if request.user.is_staff or request.user.is_manager %}
                <div class="mb-3">
                    <label for="user_role" class="form-label">Ruolo utente</label>
                    <select class="form-select" id="user_role" name="user_role">
                        <option value="">-- Nessun ruolo --</option>
                        <option value="manager" {% if user_obj.is_manager %}selected{% endif %}>Manager</option>
                        <option value="staff" {% if user_obj.is_staff_user %}selected{% endif %}>Staff</option>
                    </select>
                    <div class="form-text">
                        <strong>Manager:</strong> accesso completo ai dati della propria organizzazione, può gestire account PEC, clienti e utenti.<br>
                        <strong>Staff:</strong> accesso limitato solo agli account e clienti assegnati, con permessi di sola lettura per la maggior parte delle funzionalità.
                    </div>
                </div>
                {% endif %}

                <div class="d-flex justify-content-end">
                    <a href="{% url 'user_detail' user_obj.id %}" class="btn btn-outline-secondary me-2">Annulla</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Salva modifiche
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="password-section">
            <h2 class="form-section-title">Cambio password</h2>

            <p class="text-muted mb-3">Per cambiare la password, clicca sul pulsante qui sotto.</p>

            <a href="{% url 'user_password_change' user_obj.id %}" class="btn btn-outline-primary w-100">
                <i class="fas fa-key me-1"></i> Cambia password
            </a>
        </div>
    </div>
</div>

<script>
    // Anteprima dell'immagine del profilo
    document.addEventListener('DOMContentLoaded', function() {
        const input = document.getElementById('{{ form.immagine_profilo.id_for_label }}');
        const preview = document.querySelector('.avatar-preview div');

        input.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.style.backgroundImage = `url(${e.target.result})`;
                }
                reader.readAsDataURL(file);
            }
        });
    });
</script>
{% endblock %}
