{% extends 'base/base.html' %}

{% block title %}Profilo - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .profile-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
        font-weight: 600;
        margin: 0 auto 15px;
    }
    
    .profile-name {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 5px;
        text-align: center;
    }
    
    .profile-username {
        color: #6b7280;
        font-size: 16px;
        margin-bottom: 15px;
        text-align: center;
    }
    
    .profile-badges {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 20px;
    }
    
    .profile-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
    }
    
    .badge-admin {
        background-color: #fee2e2;
        color: #ef4444;
    }
    
    .badge-staff {
        background-color: #fef3c7;
        color: #f59e0b;
    }
    
    .badge-active {
        background-color: #dcfce7;
        color: #10b981;
    }
    
    .profile-meta {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 25px;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
    }
    
    .meta-item i {
        color: #6b7280;
        margin-right: 8px;
        width: 18px;
    }
    
    .profile-section {
        margin-bottom: 30px;
    }
    
    .form-label {
        font-weight: 500;
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e5e7eb;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Il mio profilo</h1>
</div>

<!-- Profile header -->
<div class="profile-header">
    <div class="profile-avatar">
        {{ user_obj.first_name|slice:"0:1" }}{{ user_obj.last_name|slice:"0:1" }}
    </div>
    
    <h1 class="profile-name">{{ user_obj.get_full_name }}</h1>
    <div class="profile-username">@{{ user_obj.username }}</div>
    
    <div class="profile-badges">
        {% if user_obj.is_superuser %}
        <span class="profile-badge badge-admin">Admin</span>
        {% elif user_obj.is_staff %}
        <span class="profile-badge badge-staff">Staff</span>
        {% endif %}
        
        {% if user_obj.is_active %}
        <span class="profile-badge badge-active">Attivo</span>
        {% endif %}
    </div>
    
    <div class="profile-meta">
        <div class="meta-item">
            <i class="fas fa-envelope"></i>
            <span>{{ user_obj.email }}</span>
        </div>
        
        <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Registrato il: {{ user_obj.date_joined|date:"d/m/Y" }}</span>
        </div>
        
        <div class="meta-item">
            <i class="fas fa-clock"></i>
            <span>Ultimo accesso: {{ user_obj.last_login|date:"d/m/Y H:i" }}</span>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- Personal Information -->
        <div class="card border-0 shadow-sm profile-section">
            <div class="card-body">
                <h2 class="section-title">Informazioni personali</h2>
                
                <form method="post" action="#">
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user_obj.first_name }}">
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">Cognome</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user_obj.last_name }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user_obj.email }}">
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Aggiorna informazioni</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Change Password -->
        <div class="card border-0 shadow-sm profile-section">
            <div class="card-body">
                <h2 class="section-title">Modifica password</h2>
                
                <form method="post" action="#">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Password attuale</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Nuova password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Conferma nuova password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Cambia password</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Preferences -->
        <div class="card border-0 shadow-sm profile-section">
            <div class="card-body">
                <h2 class="section-title">Preferenze</h2>
                
                <form method="post" action="#">
                    {% csrf_token %}
                    
                    <div class="mb-3 form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" checked>
                        <label class="form-check-label" for="email_notifications">Ricevi notifiche email</label>
                        <div class="form-text">Ricevi notifiche via email per nuovi messaggi PEC</div>
                    </div>
                    
                    <div class="mb-3 form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="auto_refresh" name="auto_refresh" checked>
                        <label class="form-check-label" for="auto_refresh">Aggiornamento automatico</label>
                        <div class="form-text">Aggiorna automaticamente le liste dei messaggi PEC</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Salva preferenze</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}