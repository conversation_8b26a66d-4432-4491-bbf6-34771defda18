{% extends 'base/base.html' %}

{% block title %}{{ user_obj.get_full_name }} - SimplePEC{% endblock %}

{% block extra_css %}
<!-- Includi Chart.js per i grafici -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .user-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 25px;
        display: flex;
        flex-wrap: wrap;
    }

    .user-avatar {
        width: 90px;
        height: 90px;
        border-radius: 12px;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        font-weight: 600;
        margin-right: 25px;
    }

    .user-info {
        flex: 1;
        min-width: 250px;
    }

    .user-name {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .user-username {
        color: #6b7280;
        font-size: 16px;
        margin-bottom: 12px;
    }

    .user-badges {
        display: flex;
        gap: 8px;
    }

    .user-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
    }

    .badge-admin {
        background-color: #fee2e2;
        color: #ef4444;
    }

    .badge-staff {
        background-color: #fef3c7;
        color: #f59e0b;
    }

    .badge-active {
        background-color: #dcfce7;
        color: #10b981;
    }

    .user-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 25px;
        margin-top: 20px;
        width: 100%;
    }

    .meta-item {
        display: flex;
        align-items: center;
    }

    .meta-item i {
        color: #6b7280;
        margin-right: 8px;
        width: 18px;
    }

    .tab-content {
        padding: 25px 0;
    }

    .nav-tabs .nav-link {
        color: #6b7280;
        font-weight: 500;
        padding: 12px 20px;
        border: none;
        border-bottom: 2px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #3b82f6;
        border-bottom: 2px solid #3b82f6;
        background-color: transparent;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        border-bottom: 2px solid #e5e7eb;
    }

    .account-card {
        border-radius: 8px;
        transition: transform 0.2s;
    }

    .account-card:hover {
        transform: translateY(-3px);
    }

    .account-header {
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .account-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .account-email {
        color: #6b7280;
        font-size: 14px;
    }

    .stat-card {
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 20px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 2px;
    }

    .stat-label {
        color: #6b7280;
        font-size: 14px;
    }

    .timeline {
        position: relative;
        margin-left: 35px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: -15px;
        top: 15px;
        bottom: 15px;
        width: 2px;
        background-color: #e5e7eb;
    }

    .timeline-item {
        padding: 15px 0;
        position: relative;
    }

    .timeline-dot {
        position: absolute;
        left: -21px;
        top: 18px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #3b82f6;
    }

    .timeline-content {
        padding-left: 10px;
    }

    .timeline-time {
        color: #6b7280;
        font-size: 13px;
        margin-bottom: 5px;
    }

    .timeline-title {
        font-weight: 500;
        margin-bottom: 3px;
    }

    .timeline-desc {
        color: #6b7280;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'user_list' %}">Utenti</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ user_obj.username }}</li>
        </ol>
    </nav>

    <div>
        {% if request.user.is_staff or request.user.is_manager or request.user.id == user_obj.id %}
        <a href="{% url 'user_edit' user_obj.id %}" class="btn btn-outline-primary">
            <i class="fas fa-edit me-1"></i> Modifica
        </a>
        {% endif %}
    </div>
</div>

<!-- User header -->
<div class="user-header">
    <div class="user-avatar">
        {% if user_obj.immagine_profilo %}
            <img src="{{ user_obj.immagine_profilo.url }}" alt="{{ user_obj.get_full_name }}" style="width: 100%; height: 100%; object-fit: cover;">
        {% else %}
            {{ user_obj.first_name|slice:"0:1" }}{{ user_obj.last_name|slice:"0:1" }}
        {% endif %}
    </div>

    <div class="user-info">
        <h1 class="user-name">{{ user_obj.get_full_name }}</h1>
        <div class="user-username">@{{ user_obj.username }}</div>

        <div class="user-badges">
            {% if user_obj.is_superuser %}
            <span class="user-badge badge-admin">Admin</span>
            {% elif user_obj.is_staff %}
            <span class="user-badge badge-staff">Staff Django</span>
            {% endif %}

            {% if user_obj.is_manager %}
            <span class="user-badge" style="background-color: #dbeafe; color: #2563eb;">Manager</span>
            {% elif user_obj.is_staff_user %}
            <span class="user-badge" style="background-color: #fef9c3; color: #ca8a04;">Staff</span>
            {% endif %}

            {% if user_obj.is_active %}
            <span class="user-badge badge-active">Attivo</span>
            {% endif %}
        </div>
    </div>

    <div class="user-meta">
        <div class="meta-item">
            <i class="fas fa-envelope"></i>
            <span>{{ user_obj.email }}</span>
        </div>

        <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Registrato il: {{ user_obj.date_joined|date:"d/m/Y" }}</span>
        </div>

        <div class="meta-item">
            <i class="fas fa-clock"></i>
            <span>Ultimo accesso: {{ user_obj.last_login|date:"d/m/Y H:i" }}</span>
        </div>
    </div>
</div>

<!-- Tabs -->
<ul class="nav nav-tabs" id="userTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if tab_attiva == 'account' %}active{% endif %}" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab" aria-controls="account" aria-selected="{% if tab_attiva == 'account' %}true{% else %}false{% endif %}">
            <i class="fas fa-envelope me-1"></i> Account PEC
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if tab_attiva == 'stats' %}active{% endif %}" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab" aria-controls="stats" aria-selected="{% if tab_attiva == 'stats' %}true{% else %}false{% endif %}">
            <i class="fas fa-chart-bar me-1"></i> Statistiche
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if tab_attiva == 'logs' %}active{% endif %}" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="{% if tab_attiva == 'logs' %}true{% else %}false{% endif %}">
            <i class="fas fa-history me-1"></i> Attività
        </button>
    </li>
</ul>

<div class="tab-content" id="userTabsContent">
    <!-- Account Tab -->
    <div class="tab-pane fade {% if tab_attiva == 'account' %}show active{% endif %}" id="account" role="tabpanel" aria-labelledby="account-tab">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-5 fw-bold mb-0">Account PEC autorizzati</h3>

            {% if request.user.is_staff or request.user.is_manager %}
            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assegnaAccountModal">
                <i class="fas fa-plus me-1"></i> Assegna account
            </button>
            {% endif %}
        </div>

        <div class="row g-4">
            {% for account in user_obj.account_pec_autorizzati.all %}
            <div class="col-md-6 col-lg-4">
                <div class="card account-card border-0 shadow-sm h-100">
                    <div class="account-header">
                        <h4 class="account-name">{{ account.nome }}</h4>
                        <div class="account-email">{{ account.indirizzo_email }}</div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div>
                                <div class="small text-muted">Server</div>
                                <div>{{ account.server_imap }}</div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div>
                                <div class="small text-muted">Ultima sincronizzazione</div>
                                <div>
                                    {% if account.ultima_sincronizzazione %}
                                    {{ account.ultima_sincronizzazione|date:"d/m/Y H:i" }}
                                    {% else %}
                                    Mai sincronizzato
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        {% if True %}
                        <div class="d-flex align-items-center">
                            <div class="contact-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div>
                                <div class="small text-muted">Permessi</div>
                                <div class="mt-1">
                                    {% for autorizzazione in account.autorizzazioni_utente.all %}
                                        {% if autorizzazione.utente.id == user_obj.id %}
                                            <div class="d-flex flex-column gap-1">
                                                <span class="badge {% if autorizzazione.puo_aggiornare_stato %}bg-success{% else %}bg-secondary{% endif %} w-100 text-start permission-badge"
                                                      data-permission-type="puo_aggiornare_stato"
                                                      data-account-id="{{ account.id }}"
                                                      data-user-id="{{ user_obj.id }}"
                                                      style="cursor: pointer;">
                                                    <i class="fas fa-tag me-1"></i> Aggiorna stato
                                                </span>
                                                <span class="badge {% if autorizzazione.puo_assegnare_cliente %}bg-success{% else %}bg-secondary{% endif %} w-100 text-start permission-badge"
                                                      data-permission-type="puo_assegnare_cliente"
                                                      data-account-id="{{ account.id }}"
                                                      data-user-id="{{ user_obj.id }}"
                                                      style="cursor: pointer;">
                                                    <i class="fas fa-building me-1"></i> Assegna cliente
                                                </span>
                                                <span class="badge {% if autorizzazione.puo_assegnare_referente %}bg-success{% else %}bg-secondary{% endif %} w-100 text-start permission-badge"
                                                      data-permission-type="puo_assegnare_referente"
                                                      data-account-id="{{ account.id }}"
                                                      data-user-id="{{ user_obj.id }}"
                                                      style="cursor: pointer;">
                                                    <i class="fas fa-user-tie me-1"></i> Assegna referente
                                                </span>
                                            </div>
                                        {% endif %}
                                    {% empty %}
                                        <span class="badge bg-success w-100 text-start">
                                            <i class="fas fa-check-circle me-1"></i> Tutti i permessi
                                        </span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex gap-2">
                            <a href="{% url 'account_detail' account.id %}" class="btn btn-sm btn-outline-primary flex-grow-1">
                                <i class="fas fa-eye me-1"></i> Visualizza
                            </a>
                            {% if request.user.is_staff or request.user.is_manager %}
                            <button type="button" class="btn btn-sm btn-outline-danger"
                                    data-bs-toggle="modal"
                                    data-bs-target="#confermaRimozioneModal"
                                    data-user-id="{{ user_obj.id }}"
                                    data-account-id="{{ account.id }}"
                                    data-account-nome="{{ account.nome }}">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-envelope fa-3x mb-3 opacity-50"></i>
                        <p class="mb-1">Nessun account PEC autorizzato</p>
                        {% if request.user.is_staff or request.user.is_manager %}
                        <p class="small">Clicca su "Assegna account" per autorizzare l'utente ad accedere ad account PEC</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Stats Tab -->
    <div class="tab-pane fade {% if tab_attiva == 'stats' %}show active{% endif %}" id="stats" role="tabpanel" aria-labelledby="stats-tab">
        <div class="mb-4">
            <h3 class="fs-5 fw-bold mb-3">Statistiche di utilizzo</h3>

            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card border-0 shadow-sm">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary-subtle text-primary">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div>
                                <div class="stat-value">{{ statistiche.messaggi_gestiti }}</div>
                                <div class="stat-label">Messaggi gestiti</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card border-0 shadow-sm">
                        <div class="stat-card">
                            <div class="stat-icon bg-success-subtle text-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div>
                                <div class="stat-value">{{ statistiche.assegnazioni }}</div>
                                <div class="stat-label">Assegnazioni</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card border-0 shadow-sm">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning-subtle text-warning">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div>
                                <div class="stat-value">{{ statistiche.sincronizzazioni }}</div>
                                <div class="stat-label">Sincronizzazioni</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Riquadro "Ore di attività" rimosso come richiesto -->
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0 fw-semibold">Attività nel tempo</h5>
            </div>
            <div class="card-body">
                {% if grafico_attivita.dati|length > 0 %}
                    <div style="height: 300px;">
                        <canvas id="activityChart"></canvas>
                    </div>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const ctx = document.getElementById('activityChart').getContext('2d');
                            const chart = new Chart(ctx, {
                                type: 'line',
                                data: {
                                    labels: {{ grafico_attivita.labels|safe }},
                                    datasets: [{
                                        label: 'Attività giornaliera',
                                        data: {{ grafico_attivita.dati|safe }},
                                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                        borderColor: 'rgba(59, 130, 246, 1)',
                                        borderWidth: 2,
                                        tension: 0.3,
                                        fill: true,
                                        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                                        pointRadius: 3
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            ticks: {
                                                precision: 0
                                            }
                                        }
                                    },
                                    plugins: {
                                        tooltip: {
                                            callbacks: {
                                                title: function(tooltipItems) {
                                                    return 'Data: ' + tooltipItems[0].label;
                                                },
                                                label: function(context) {
                                                    return 'Accessi: ' + context.raw;
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                        });
                    </script>
                {% else %}
                    <div style="height: 300px; display: flex; align-items: center; justify-content: center;">
                        <div class="text-muted text-center">
                            <i class="fas fa-chart-line fa-3x mb-3 opacity-50"></i>
                            <p>Non ci sono ancora dati sufficienti per mostrare il grafico</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Logs Tab -->
    <div class="tab-pane fade {% if tab_attiva == 'logs' %}show active{% endif %}" id="logs" role="tabpanel" aria-labelledby="logs-tab">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-5 fw-bold mb-0">Registro attività</h3>

            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filtroAttivita" aria-expanded="false" aria-controls="filtroAttivita">
                <i class="fas fa-filter me-1"></i> Filtra per data
            </button>
        </div>

        <!-- Filtro per data -->
        <div class="collapse mb-4" id="filtroAttivita">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="get" action="" class="row g-3 align-items-end">
                        <input type="hidden" name="tab" value="logs">
                        <div class="col-md-5">
                            <label for="data_inizio" class="form-label">Data inizio</label>
                            <input type="date" class="form-control" id="data_inizio" name="data_inizio" value="{{ filtro_date.data_inizio }}">
                        </div>
                        <div class="col-md-5">
                            <label for="data_fine" class="form-label">Data fine</label>
                            <input type="date" class="form-control" id="data_fine" name="data_fine" value="{{ filtro_date.data_fine }}">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">Applica</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="timeline">
                    <!-- Timeline items -->
                    {% for item in attivita %}
                    <div class="timeline-item">
                        <div class="timeline-dot {% if item.tipo == 'accesso' %}bg-primary{% elif item.tipo == 'associazione' %}bg-success{% else %}bg-info{% endif %}"></div>
                        <div class="timeline-content">
                            <div class="timeline-time">{{ item.data|date:"d/m/Y H:i" }}</div>
                            <div class="timeline-title">
                                {% if item.tipo == 'accesso' %}
                                    <i class="fas fa-eye me-1 text-primary"></i>
                                {% elif item.tipo == 'associazione' %}
                                    <i class="fas fa-link me-1 text-success"></i>
                                {% else %}
                                    <i class="fas fa-edit me-1 text-info"></i>
                                {% endif %}
                                {{ item.descrizione }}
                            </div>
                            {% if item.url %}
                            <div class="timeline-desc">
                                <a href="{{ item.url }}" class="btn btn-sm btn-outline-secondary mt-2">
                                    <i class="fas fa-external-link-alt me-1"></i> Visualizza
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-history fa-3x mb-3 opacity-50"></i>
                            <p class="mb-1">Nessuna attività registrata nel periodo selezionato</p>
                            <p class="small">Prova a selezionare un intervallo di date diverso</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal per assegnare account PEC -->
{% if request.user.is_staff or request.user.is_manager %}
<div class="modal fade" id="assegnaAccountModal" tabindex="-1" aria-labelledby="assegnaAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assegnaAccountModalLabel">Assegna Account PEC a {{ user_obj.get_full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'assegna_account' user_obj.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="clear_existing" name="clear_existing">
                            <label class="form-check-label" for="clear_existing">
                                Rimuovi tutti gli account esistenti
                            </label>
                            <div class="form-text">Se selezionato, tutti gli account PEC attualmente assegnati all'utente verranno rimossi prima di aggiungere quelli selezionati.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Seleziona gli account PEC da assegnare:</label>
                        <div class="form-text mb-3">Gli account già assegnati all'utente sono evidenziati.</div>

                        <div class="row g-3">
                            {% for account in all_accounts %}
                            <div class="col-md-6">
                                <div class="card {% if account in user_obj.account_pec_autorizzati.all %}border-primary{% else %}border{% endif %}">
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="account_{{ account.id }}" name="account_ids" value="{{ account.id }}"
                                                {% if account in user_obj.account_pec_autorizzati.all %}checked{% endif %}>
                                            <label class="form-check-label" for="account_{{ account.id }}">
                                                <div class="fw-bold">{{ account.nome }}</div>
                                                <div class="small text-muted">{{ account.indirizzo_email }}</div>
                                            </label>
                                        </div>

                                        {% if account in user_obj.account_pec_autorizzati.all %}
                                        <div class="mt-2 border-top pt-2">
                                            <div class="small fw-bold mb-1">Permessi:</div>
                                            <div class="form-check mb-1">
                                                <input class="form-check-input" type="checkbox" id="puo_aggiornare_stato_{{ account.id }}"
                                                       name="puo_aggiornare_stato_{{ account.id }}"
                                                       {% for autorizzazione in account.autorizzazioni_utente.all %}
                                                           {% if autorizzazione.utente.id == user_obj.id and autorizzazione.puo_aggiornare_stato %}checked{% endif %}
                                                       {% endfor %}>
                                                <label class="form-check-label small" for="puo_aggiornare_stato_{{ account.id }}">
                                                    Aggiorna stato
                                                </label>
                                            </div>
                                            <div class="form-check mb-1">
                                                <input class="form-check-input" type="checkbox" id="puo_assegnare_cliente_{{ account.id }}"
                                                       name="puo_assegnare_cliente_{{ account.id }}"
                                                       {% for autorizzazione in account.autorizzazioni_utente.all %}
                                                           {% if autorizzazione.utente.id == user_obj.id and autorizzazione.puo_assegnare_cliente %}checked{% endif %}
                                                       {% endfor %}>
                                                <label class="form-check-label small" for="puo_assegnare_cliente_{{ account.id }}">
                                                    Assegna cliente
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="puo_assegnare_referente_{{ account.id }}"
                                                       name="puo_assegnare_referente_{{ account.id }}"
                                                       {% for autorizzazione in account.autorizzazioni_utente.all %}
                                                           {% if autorizzazione.utente.id == user_obj.id and autorizzazione.puo_assegnare_referente %}checked{% endif %}
                                                       {% endfor %}>
                                                <label class="form-check-label small" for="puo_assegnare_referente_{{ account.id }}">
                                                    Assegna referente
                                                </label>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="col-12">
                                <div class="alert alert-info">
                                    Non ci sono account PEC disponibili nel sistema.
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Salva assegnazioni
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<!-- Modal di conferma rimozione account -->
{% if request.user.is_staff or request.user.is_manager %}
<div class="modal fade" id="confermaRimozioneModal" tabindex="-1" aria-labelledby="confermaRimozioneModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confermaRimozioneModalLabel">Conferma rimozione</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <p class="mb-1">Sei sicuro di voler rimuovere l'account <strong id="accountNome"></strong> dall'utente <strong>{{ user_obj.get_full_name }}</strong>?</p>
                    <p class="small text-muted">Questa azione non può essere annullata.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <form id="formRimozioneAccount" method="post" action="">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Rimuovi
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Script per gestire la modale di conferma e i permessi -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestione della modale di conferma rimozione account
        var confermaRimozioneModal = document.getElementById('confermaRimozioneModal');
        if (confermaRimozioneModal) {
            confermaRimozioneModal.addEventListener('show.bs.modal', function(event) {
                // Pulsante che ha attivato la modale
                var button = event.relatedTarget;

                // Estrai le informazioni dai data attributes
                var userId = button.getAttribute('data-user-id');
                var accountId = button.getAttribute('data-account-id');
                var accountNome = button.getAttribute('data-account-nome');

                // Aggiorna il contenuto della modale
                var accountNomeElement = confermaRimozioneModal.querySelector('#accountNome');
                if (accountNomeElement) {
                    accountNomeElement.textContent = accountNome;
                }

                // Aggiorna l'action del form
                var form = confermaRimozioneModal.querySelector('#formRimozioneAccount');
                if (form) {
                    form.action = '/users/' + userId + '/rimuovi-account/' + accountId + '/';
                }
            });
        }

        // Gestione del click sui badge dei permessi
        const permissionBadges = document.querySelectorAll('.permission-badge');
        permissionBadges.forEach(badge => {
            badge.addEventListener('click', function() {
                const permissionType = this.getAttribute('data-permission-type');
                const accountId = this.getAttribute('data-account-id');
                const userId = this.getAttribute('data-user-id');

                // Ottieni il token CSRF
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                // Invia la richiesta AJAX per aggiornare il permesso
                fetch('/users/' + userId + '/toggle-permission/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({
                        permission_type: permissionType,
                        account_id: accountId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Aggiorna l'aspetto del badge
                        if (data.enabled) {
                            this.classList.remove('bg-secondary');
                            this.classList.add('bg-success');
                        } else {
                            this.classList.remove('bg-success');
                            this.classList.add('bg-secondary');
                        }
                    } else {
                        // Mostra un messaggio di errore
                        alert('Errore: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Errore:', error);
                    alert('Si è verificato un errore durante l\'aggiornamento del permesso.');
                });
            });
        });
    });
</script>
{% endblock %}