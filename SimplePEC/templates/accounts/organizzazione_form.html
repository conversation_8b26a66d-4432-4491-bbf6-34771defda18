{% extends 'base/base.html' %}
{% load static %}

{% block title %}
    {% if object %}Modifica Organizzazione{% else %}Nuova Organizzazione{% endif %} - SimplePEC
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            {% if object %}Modifica Organizzazione{% else %}Nuova Organizzazione{% endif %}
        </h1>
        <a href="{% if object %}{% url 'organizzazione_detail' object.id %}{% else %}{% url 'organizzazione_list' %}{% endif %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i> Annulla
        </a>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="mb-3">Informazioni principali</h5>
                            </div>

                            <div class="col-md-8 mb-3">
                                <label for="id_ragione_sociale" class="form-label">Ragione Sociale <span class="text-danger">*</span></label>
                                <input type="text" name="ragione_sociale" id="id_ragione_sociale" class="form-control {% if form.ragione_sociale.errors %}is-invalid{% endif %}" value="{{ form.ragione_sociale.value|default:'' }}" required>
                                {% if form.ragione_sociale.errors %}
                                <div class="invalid-feedback">
                                    {{ form.ragione_sociale.errors.0 }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="id_logo" class="form-label">Logo</label>
                                <input type="file" name="logo" id="id_logo" class="form-control {% if form.logo.errors %}is-invalid{% endif %}" accept="image/*">
                                {% if form.logo.errors %}
                                <div class="invalid-feedback">
                                    {{ form.logo.errors.0 }}
                                </div>
                                {% endif %}
                                {% if object and object.logo %}
                                <div class="mt-2">
                                    <img src="{{ object.logo.url }}" alt="Logo attuale" class="img-thumbnail" style="max-height: 100px;">
                                    <div class="form-text">Logo attuale</div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="mb-3">Contatti</h5>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label for="id_indirizzo" class="form-label">Indirizzo</label>
                                <input type="text" name="indirizzo" id="id_indirizzo" class="form-control {% if form.indirizzo.errors %}is-invalid{% endif %}" value="{{ form.indirizzo.value|default:'' }}">
                                {% if form.indirizzo.errors %}
                                <div class="invalid-feedback">
                                    {{ form.indirizzo.errors.0 }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="id_telefono" class="form-label">Telefono</label>
                                <input type="text" name="telefono" id="id_telefono" class="form-control {% if form.telefono.errors %}is-invalid{% endif %}" value="{{ form.telefono.value|default:'' }}">
                                {% if form.telefono.errors %}
                                <div class="invalid-feedback">
                                    {{ form.telefono.errors.0 }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="id_email" class="form-label">Email</label>
                                <input type="email" name="email" id="id_email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" value="{{ form.email.value|default:'' }}">
                                {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {{ form.email.errors.0 }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="id_pec" class="form-label">PEC</label>
                                <input type="email" name="pec" id="id_pec" class="form-control {% if form.pec.errors %}is-invalid{% endif %}" value="{{ form.pec.value|default:'' }}">
                                {% if form.pec.errors %}
                                <div class="invalid-feedback">
                                    {{ form.pec.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Salva
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
