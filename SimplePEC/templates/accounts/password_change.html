{% extends 'base/base.html' %}

{% block title %}Cambio Password - {{ user.get_full_name }} - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .password-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 25px;
        text-align: center;
    }
    
    .password-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        margin: 0 auto 15px;
    }
    
    .password-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .password-subtitle {
        color: #6b7280;
        font-size: 16px;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .form-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin-bottom: 25px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .form-section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .password-requirements {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .password-requirements h3 {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .password-requirements ul {
        font-size: 14px;
        color: #6b7280;
        padding-left: 20px;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'user_list' %}">Utenti</a></li>
            <li class="breadcrumb-item"><a href="{% url 'user_detail' user.id %}">{{ user.username }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Cambio Password</li>
        </ol>
    </nav>
</div>

<div class="password-header">
    <div class="password-icon">
        <i class="fas fa-key"></i>
    </div>
    <h1 class="password-title">Cambio Password</h1>
    <p class="password-subtitle">Inserisci la tua password attuale e la nuova password per aggiornare le tue credenziali di accesso.</p>
</div>

<div class="form-section">
    <h2 class="form-section-title">Aggiorna la tua password</h2>
    
    <div class="password-requirements">
        <h3>Requisiti per la password:</h3>
        <ul>
            <li>La password deve contenere almeno 8 caratteri</li>
            <li>Non può essere troppo simile alle tue informazioni personali</li>
            <li>Non può essere una password comunemente utilizzata</li>
            <li>Non può essere interamente numerica</li>
        </ul>
    </div>
    
    <form method="post">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="{{ form.old_password.id_for_label }}" class="form-label">{{ form.old_password.label }}</label>
            {{ form.old_password }}
            {% if form.old_password.errors %}
                <div class="text-danger">{{ form.old_password.errors }}</div>
            {% endif %}
        </div>
        
        <div class="mb-3">
            <label for="{{ form.new_password1.id_for_label }}" class="form-label">{{ form.new_password1.label }}</label>
            {{ form.new_password1 }}
            {% if form.new_password1.errors %}
                <div class="text-danger">{{ form.new_password1.errors }}</div>
            {% endif %}
        </div>
        
        <div class="mb-3">
            <label for="{{ form.new_password2.id_for_label }}" class="form-label">{{ form.new_password2.label }}</label>
            {{ form.new_password2 }}
            {% if form.new_password2.errors %}
                <div class="text-danger">{{ form.new_password2.errors }}</div>
            {% endif %}
        </div>
        
        <div class="d-flex justify-content-end">
            <a href="{% url 'user_detail' user.id %}" class="btn btn-outline-secondary me-2">Annulla</a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i> Cambia password
            </button>
        </div>
    </form>
</div>
{% endblock %}
