{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ organizzazione.ragione_sociale }} - SimplePEC{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ organizzazione.ragione_sociale }}</h1>
        <div>
            <a href="{% url 'organizzazione_update' organizzazione.id %}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i> Modifica
            </a>
            <a href="{% url 'organizzazione_list' %}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-arrow-left me-2"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Informazioni organizzazione -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">Informazioni</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if organizzazione.logo %}
                            <img src="{{ organizzazione.logo.url }}" alt="{{ organizzazione.ragione_sociale }}" class="img-fluid mb-3" style="max-height: 150px;">
                        {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center mb-3 mx-auto" style="width: 150px; height: 150px;">
                                <i class="fas fa-building fa-4x text-muted"></i>
                            </div>
                        {% endif %}
                        <h4>{{ organizzazione.ragione_sociale }}</h4>
                    </div>

                    <ul class="list-group list-group-flush">
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span><i class="fas fa-map-marker-alt me-2"></i> Indirizzo:</span>
                            <strong>{{ organizzazione.indirizzo|default:"Non specificato" }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span><i class="fas fa-phone me-2"></i> Telefono:</span>
                            <strong>{{ organizzazione.telefono|default:"Non specificato" }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span><i class="fas fa-envelope me-2"></i> Email:</span>
                            <strong>{{ organizzazione.email|default:"Non specificata" }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span><i class="fas fa-envelope-open-text me-2"></i> PEC:</span>
                            <strong>{{ organizzazione.pec|default:"Non specificata" }}</strong>
                        </li>
                        <li class="list-group-item px-0 py-2 d-flex justify-content-between">
                            <span><i class="fas fa-calendar-alt me-2"></i> Data creazione:</span>
                            <strong>{{ organizzazione.data_creazione|date:"d/m/Y" }}</strong>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Utenti dell'organizzazione -->
        <div class="col-md-8 mb-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Utenti ({{ utenti.count }})</h5>
                    {% if is_admin %}
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#associaUtentiModal">
                        <i class="fas fa-user-plus me-1"></i> Associa utenti
                    </button>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Telefono</th>
                                    <th>Gruppo</th>
                                    <th>Azioni</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for utente in utenti %}
                                <tr>
                                    <td>{{ utente.get_full_name }}</td>
                                    <td>{{ utente.email }}</td>
                                    <td>{{ utente.telefono|default:"-" }}</td>
                                    <td>{{ utente.gruppo|default:"-" }}</td>
                                    <td>
                                        <a href="{% url 'user_detail' utente.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-users fa-2x mb-3 opacity-50"></i>
                                            <p>Nessun utente associato a questa organizzazione</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Account PEC dell'organizzazione -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Account PEC ({{ account_pec.count }})</h5>
                    {% if is_admin %}
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#associaAccountPECModal">
                        <i class="fas fa-envelope-open-text me-1"></i> Associa account PEC
                    </button>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nome</th>
                                    <th>Indirizzo Email</th>
                                    <th>Stato</th>
                                    <th>Ultima sincronizzazione</th>
                                    <th>Azioni</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in account_pec %}
                                <tr>
                                    <td>{{ account.nome }}</td>
                                    <td>{{ account.indirizzo_email }}</td>
                                    <td>
                                        {% if account.attivo %}
                                        <span class="badge bg-success">Attivo</span>
                                        {% else %}
                                        <span class="badge bg-danger">Disattivato</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if account.ultima_sincronizzazione %}
                                        {{ account.ultima_sincronizzazione|date:"d/m/Y H:i" }}
                                        {% else %}
                                        <span class="text-muted">Mai</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'account_detail' account.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-envelope fa-2x mb-3 opacity-50"></i>
                                            <p>Nessun account PEC associato a questa organizzazione</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal per associare utenti all'organizzazione -->
<div class="modal fade" id="associaUtentiModal" tabindex="-1" aria-labelledby="associaUtentiModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="associaUtentiModalLabel">Associa utenti a {{ organizzazione.ragione_sociale }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizzazione_associa_utenti' organizzazione.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="clear_existing_users" name="clear_existing_users">
                            <label class="form-check-label" for="clear_existing_users">
                                Rimuovi associazioni esistenti
                            </label>
                            <div class="form-text">Se selezionato, tutti gli utenti attualmente associati verranno rimossi prima di aggiungere quelli selezionati.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Seleziona gli utenti da associare:</label>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th style="width: 50px;"></th>
                                        <th>Nome</th>
                                        <th>Email</th>
                                        <th>Gruppo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for utente in all_users %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="user_ids" value="{{ utente.id }}" id="user_{{ utente.id }}"
                                                {% if utente.organizzazione and utente.organizzazione.id == organizzazione.id %}checked{% endif %}>
                                            </div>
                                        </td>
                                        <td><label for="user_{{ utente.id }}">{{ utente.get_full_name }}</label></td>
                                        <td>{{ utente.email }}</td>
                                        <td>{{ utente.gruppo|default:"-" }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="4" class="text-center py-3">Nessun utente disponibile</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="submit" class="btn btn-primary">Salva</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal per associare account PEC all'organizzazione -->
<div class="modal fade" id="associaAccountPECModal" tabindex="-1" aria-labelledby="associaAccountPECModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="associaAccountPECModalLabel">Associa account PEC a {{ organizzazione.ragione_sociale }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizzazione_associa_account_pec' organizzazione.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="clear_existing_accounts" name="clear_existing_accounts">
                            <label class="form-check-label" for="clear_existing_accounts">
                                Rimuovi associazioni esistenti
                            </label>
                            <div class="form-text">Se selezionato, tutti gli account PEC attualmente associati verranno rimossi prima di aggiungere quelli selezionati.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Seleziona gli account PEC da associare:</label>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th style="width: 50px;"></th>
                                        <th>Nome</th>
                                        <th>Indirizzo Email</th>
                                        <th>Stato</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for account in all_accounts %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="account_ids" value="{{ account.id }}" id="account_{{ account.id }}"
                                                {% if account.organizzazione and account.organizzazione.id == organizzazione.id %}checked{% endif %}>
                                            </div>
                                        </td>
                                        <td><label for="account_{{ account.id }}">{{ account.nome }}</label></td>
                                        <td>{{ account.indirizzo_email }}</td>
                                        <td>
                                            {% if account.attivo %}
                                            <span class="badge bg-success">Attivo</span>
                                            {% else %}
                                            <span class="badge bg-danger">Disattivato</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="4" class="text-center py-3">Nessun account PEC disponibile</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="submit" class="btn btn-primary">Salva</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
