{% extends 'base/base.html' %}

{% block title %}Assegna utenti a {{ cliente.nome }} - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .user-list {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .user-item {
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid #e5e7eb;
        transition: all 0.2s;
    }
    
    .user-item:hover {
        background-color: #f9fafb;
    }
    
    .user-item .form-check-input {
        margin-top: 0;
    }
    
    .user-name {
        font-weight: 500;
        margin-bottom: 2px;
    }
    
    .user-email {
        font-size: 14px;
        color: #6b7280;
    }
    
    .no-users-message {
        padding: 30px;
        text-align: center;
        color: #6b7280;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'cliente_list' %}">Clienti</a></li>
            <li class="breadcrumb-item"><a href="{% url 'cliente_detail' cliente.id %}">{{ cliente.nome }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Assegna utenti</li>
        </ol>
    </nav>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-header bg-transparent">
        <h5 class="mb-0 fw-semibold">Assegna utenti Staff a {{ cliente.nome }}</h5>
    </div>
    <div class="card-body">
        <p class="text-muted mb-4">
            Seleziona gli utenti Staff che possono accedere a questo cliente. Gli utenti selezionati potranno visualizzare e modificare i dati del cliente.
        </p>
        
        <form method="post">
            {% csrf_token %}
            
            <div class="user-list">
                {% if form.utenti.field.queryset.exists %}
                    {% for utente in form.utenti.field.queryset %}
                    <div class="user-item d-flex align-items-center">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="utenti" value="{{ utente.id }}" id="user-{{ utente.id }}"
                                {% if utente in form.fields.utenti.initial %}checked{% endif %}>
                        </div>
                        <div class="ms-3">
                            <div class="user-name">{{ utente.get_full_name|default:utente.username }}</div>
                            <div class="user-email">{{ utente.email }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-users-message">
                        <i class="fas fa-users fa-2x mb-3 opacity-50"></i>
                        <p class="mb-0">Non ci sono utenti Staff disponibili nell'organizzazione</p>
                    </div>
                {% endif %}
            </div>
            
            <div class="mt-4 d-flex justify-content-end">
                <a href="{% url 'cliente_detail' cliente.id %}" class="btn btn-outline-secondary me-2">Annulla</a>
                <button type="submit" class="btn btn-primary">Salva assegnazioni</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
