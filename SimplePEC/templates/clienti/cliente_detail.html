{% extends 'base/base.html' %}

{% block title %}{{ cliente.nome }} - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .cliente-header {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .cliente-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .cliente-code {
        color: #6b7280;
        font-size: 16px;
    }

    .cliente-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 15px;
    }

    .meta-item {
        display: flex;
        align-items: center;
    }

    .meta-item i {
        color: #6b7280;
        margin-right: 8px;
        width: 18px;
    }

    .tab-content {
        padding: 25px 0;
    }

    .nav-tabs .nav-link {
        color: #6b7280;
        font-weight: 500;
        padding: 12px 20px;
        border: none;
        border-bottom: 2px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #3b82f6;
        border-bottom: 2px solid #3b82f6;
        background-color: transparent;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        border-bottom: 2px solid #e5e7eb;
    }

    .referente-card {
        border-radius: 8px;
        transition: transform 0.2s;
    }

    .referente-card:hover {
        transform: translateY(-3px);
    }

    .referente-header {
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .referente-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .referente-role {
        color: #6b7280;
        font-size: 14px;
    }

    .contact-icon {
        width: 40px;
        height: 40px;
        background-color: #f3f4f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: #6b7280;
    }

    .add-btn-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #eff6ff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #3b82f6;
        border: none;
        transition: all 0.2s;
    }

    .add-btn-circle:hover {
        background-color: #3b82f6;
        color: white;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Gestione ricerca utenti con autocomplete
    let searchTimeout;
    const userSearch = document.getElementById('user_search');
    const searchResults = document.getElementById('userSearchResults');
    const selectedUserId = document.getElementById('selected_user_id');
    const existingReferenteForm = document.getElementById('existingReferenteForm');

    userSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = this.value.trim();

        if (searchTerm.length < 2) {
            searchResults.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`/api/search-users/?q=${encodeURIComponent(searchTerm)}`)
                .then(response => {
                    if (!response.ok) {
                        console.error('Errore HTTP:', response.status, response.statusText);
                        return Promise.reject('Errore nella risposta del server');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Dati ricevuti:', data);
                    searchResults.innerHTML = '';
                    data.forEach(user => {
                        const item = document.createElement('a');
                        item.href = '#';
                        item.className = 'list-group-item list-group-item-action';
                        item.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${user.first_name} ${user.last_name}</strong>
                                    <div class="text-muted small">${user.email}</div>
                                </div>
                            </div>
                        `;
                        item.addEventListener('click', (e) => {
                            e.preventDefault();
                            userSearch.value = `${user.first_name} ${user.last_name} (${user.email})`;
                            selectedUserId.value = user.id;
                            searchResults.style.display = 'none';
                        });
                        searchResults.appendChild(item);
                    });
                    searchResults.style.display = data.length > 0 ? 'block' : 'none';
                })
                .catch(error => {
                    console.error('Errore nella chiamata API:', error);
                    searchResults.innerHTML = '<div class="list-group-item text-danger">Errore nella ricerca: ' + error + '</div>';
                    searchResults.style.display = 'block';
                });
        }, 300);
    });

    // Nascondi i risultati quando si clicca fuori
    document.addEventListener('click', function(e) {
        if (!userSearch.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });

    // Validazione del form
    existingReferenteForm.addEventListener('submit', function(e) {
        if (!selectedUserId.value) {
            e.preventDefault();
            alert('Seleziona un utente dalla lista');
        }
    });
</script>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{% url 'cliente_list' %}">Clienti</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ cliente.nome }}</li>
        </ol>
    </nav>

    <div>
        {% if can_edit %}
            {% if request.user.is_superuser or is_manager %}
            <div class="btn-group">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editClienteModal">
                    <i class="fas fa-edit me-1"></i> Modifica
                </button>
                <a href="{% url 'cliente_assegna_utenti' cliente.id %}" class="btn btn-outline-primary">
                    <i class="fas fa-users me-1"></i> Assegna utenti
                </a>
            </div>
            {% else %}
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editClienteModal">
                <i class="fas fa-edit me-1"></i> Modifica
            </button>
            {% endif %}
        {% endif %}
    </div>
</div>

<!-- Cliente header -->
<div class="cliente-header">
    <div class="d-flex justify-content-between">
        <div>
            <h1 class="cliente-title">{{ cliente.nome }}</h1>
            <div class="cliente-code">Codice: {{ cliente.codice }}</div>
        </div>
        <div>
            {% if cliente.attivo %}
            <span class="badge bg-success">Attivo</span>
            {% else %}
            <span class="badge bg-danger">Inattivo</span>
            {% endif %}
        </div>
    </div>

    <div class="cliente-meta">
        <div class="meta-item">
            <i class="fas fa-id-card"></i>
            <span>P.IVA: {{ cliente.partita_iva|default:"Non specificata" }}</span>
        </div>

        <div class="meta-item">
            <i class="fas fa-address-card"></i>
            <span>CF: {{ cliente.codice_fiscale|default:"Non specificato" }}</span>
        </div>

        <div class="meta-item">
            <i class="fas fa-building"></i>
            <span>{{ cliente.indirizzo|default:"Indirizzo non specificato" }}</span>
        </div>

        <div class="meta-item">
            <i class="fas fa-envelope"></i>
            <span>{{ cliente.email|default:"Email non specificata" }}</span>
        </div>

        <div class="meta-item">
            <i class="fas fa-phone"></i>
            <span>{{ cliente.telefono|default:"Telefono non specificato" }}</span>
        </div>
    </div>
</div>

<!-- Tabs -->
<ul class="nav nav-tabs" id="clienteTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="referenti-tab" data-bs-toggle="tab" data-bs-target="#referenti" type="button" role="tab" aria-controls="referenti" aria-selected="true">
            <i class="fas fa-user-tie me-1"></i> Referenti
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="messaggi-tab" data-bs-toggle="tab" data-bs-target="#messaggi" type="button" role="tab" aria-controls="messaggi" aria-selected="false">
            <i class="fas fa-envelope me-1"></i> Messaggi
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="note-tab" data-bs-toggle="tab" data-bs-target="#note" type="button" role="tab" aria-controls="note" aria-selected="false">
            <i class="fas fa-sticky-note me-1"></i> Note
        </button>
    </li>
</ul>

<div class="tab-content" id="clienteTabsContent">
    <!-- Referenti Tab -->
    <div class="tab-pane fade show active" id="referenti" role="tabpanel" aria-labelledby="referenti-tab">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-5 fw-bold mb-0">Referenti</h3>
            {% if request.user.is_superuser or is_manager %}
            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#newReferenteModal">
                <i class="fas fa-plus me-1"></i> Nuovo referente
            </button>
            {% endif %}
        </div>

        <div class="row g-4">
            {% for referente in referenti %}
            <div class="col-md-6 col-lg-4">
                <div class="card referente-card border-0 shadow-sm h-100">
                    <div class="referente-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h4 class="referente-name">{{ referente.utente.first_name }} {{ referente.utente.last_name }}</h4>
                                <div class="referente-role">{{ referente.utente.groups.first.name|default:"" }}</div>
                            </div>
                            {% if request.user.is_superuser or is_manager %}
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light rounded-circle" type="button" id="dropdownMenuButton-{{ referente.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton-{{ referente.id }}">
                                    <li>
                                        <button class="dropdown-item" data-bs-toggle="modal" data-bs-target="#editReferenteModal-{{ referente.id }}">
                                            <i class="fas fa-edit me-2 text-primary"></i> Modifica
                                        </button>
                                    </li>
                                    <li>
                                        <button class="dropdown-item" data-bs-toggle="modal" data-bs-target="#deleteReferenteModal-{{ referente.id }}">
                                            <i class="fas fa-trash-alt me-2 text-danger"></i> Elimina
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <div class="small text-muted">Email</div>
                                <div>{{ referente.utente.email|default:"Non specificato" }}</div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <div class="small text-muted">Telefono</div>
                                <div>{{ referente.utente.telefono|default:"Non specificato" }}</div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="contact-icon">
                                <i class="fas fa-envelope-open-text"></i>
                            </div>
                            <div>
                                <div class="small text-muted">Data registrazione</div>
                                <div>{{ referente.utente.date_joined|date:"d/m/Y" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-user-tie fa-3x mb-3 opacity-50"></i>
                        <p class="mb-1">Nessun referente associato al cliente</p>
                        <p class="small">Clicca su "Nuovo referente" per aggiungerne uno</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Messaggi Tab -->
    <div class="tab-pane fade" id="messaggi" role="tabpanel" aria-labelledby="messaggi-tab">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-5 fw-bold mb-0">Messaggi PEC</h3>
            <a href="{% url 'message_list' %}?cliente={{ cliente.id }}" class="btn btn-sm btn-primary">
                <i class="fas fa-search me-1"></i> Vedi tutti i messaggi
            </a>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Oggetto</th>
                                <th>Mittente</th>
                                <th>Data</th>
                                <th>Stato</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for messaggio in messaggi %}
                            <tr>
                                <td>{{ messaggio.oggetto|truncatechars:50 }}</td>
                                <td>{{ messaggio.mittente_info.nome }}</td>
                                <td>{{ messaggio.data_ricezione|date:"d/m/Y H:i" }}</td>
                                <td>
                                    {% if messaggio.stato == 'DA_LEGGERE' %}
                                    <span class="badge bg-danger">Da leggere</span>
                                    {% elif messaggio.stato == 'DA_ASSEGNARE' %}
                                    <span class="badge bg-warning">Da assegnare</span>
                                    {% elif messaggio.stato == 'ASSEGNATO' %}
                                    <span class="badge bg-primary">Assegnato</span>
                                    {% elif messaggio.stato == 'LAVORATO' %}
                                    <span class="badge bg-success">Lavorato</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'message_detail' messaggio.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="text-muted">
                                        <p class="mb-0">Nessun messaggio associato al cliente</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Note Tab -->
    <div class="tab-pane fade" id="note" role="tabpanel" aria-labelledby="note-tab">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-5 fw-bold mb-0">Note cliente</h3>
            <button class="add-btn-circle" data-bs-toggle="modal" data-bs-target="#editNoteModal">
                <i class="fas fa-plus"></i>
            </button>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-body">
                {% if cliente.note %}
                <p>{{ cliente.note }}</p>
                {% else %}
                <div class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-sticky-note fa-2x mb-3 opacity-50"></i>
                        <p class="mb-0">Nessuna nota disponibile</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Edit Cliente Modal -->
<div class="modal fade" id="editClienteModal" tabindex="-1" aria-labelledby="editClienteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editClienteModalLabel">Modifica cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'cliente_update' cliente.id %}" id="editClienteForm">
                    {% csrf_token %}

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informazioni cliente</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nome" class="form-label">Nome cliente</label>
                                    <input type="text" class="form-control" id="nome" name="nome" required value="{{ cliente.nome }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="codice" class="form-label">Codice cliente</label>
                                    <input type="text" class="form-control" id="codice" name="codice" value="{{ cliente.codice }}">
                                    <div class="form-text">Codice univoco per identificare il cliente</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="partita_iva" class="form-label">Partita IVA</label>
                                    <input type="text" class="form-control" id="partita_iva" name="partita_iva" maxlength="11" value="{{ cliente.partita_iva }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="codice_fiscale" class="form-label">Codice Fiscale</label>
                                    <input type="text" class="form-control" id="codice_fiscale" name="codice_fiscale" maxlength="16" value="{{ cliente.codice_fiscale }}">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="indirizzo" class="form-label">Indirizzo</label>
                            <input type="text" class="form-control" id="indirizzo" name="indirizzo" value="{{ cliente.indirizzo }}">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ cliente.email }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telefono" class="form-label">Telefono</label>
                                    <input type="tel" class="form-control" id="telefono" name="telefono" value="{{ cliente.telefono }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="attivo" name="attivo" {% if cliente.attivo %}checked{% endif %}>
                            <label class="form-check-label" for="attivo">Cliente attivo</label>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Note</h6>
                        <div class="mb-3">
                            <label for="note" class="form-label">Note aggiuntive</label>
                            <textarea class="form-control" id="note" name="note" rows="3">{{ cliente.note }}</textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('editClienteForm').submit()">
                    <i class="fas fa-save me-1"></i> Salva modifiche
                </button>
            </div>
        </div>
    </div>
</div>

<!-- New Referente Modal -->
<div class="modal fade" id="newReferenteModal" tabindex="-1" aria-labelledby="newReferenteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newReferenteModalLabel">Nuovo referente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Utente esistente -->
                <form method="post" action="{% url 'cliente_add_referente_existing' cliente.id %}" id="existingReferenteForm">
                    {% csrf_token %}
                    <input type="hidden" name="form_type" value="existing_user">
                    <input type="hidden" name="user_id" id="selected_user_id">

                    <div class="mb-4">
                        <label for="user_search" class="form-label">Cerca e seleziona utente</label>
                        <input type="text" class="form-control" id="user_search" 
                               placeholder="Cerca per nome, email o username"
                               autocomplete="off"
                               required>
                        <div id="userSearchResults" class="list-group position-absolute w-100 shadow-sm mt-1" style="z-index: 1000; display: none; max-height: 300px; overflow-y: auto;"></div>
                        <div class="form-text">Inizia a digitare per cercare un utente</div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Annulla</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-1"></i> Associa utente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Note Modal -->
<div class="modal fade" id="editNoteModal" tabindex="-1" aria-labelledby="editNoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editNoteModalLabel">Modifica note</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'cliente_update_note' cliente.id %}" id="editNoteForm">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="note_modal" class="form-label">Note cliente</label>
                        <textarea class="form-control" id="note_modal" name="note" rows="8">{{ cliente.note }}</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('editNoteForm').submit()">
                    <i class="fas fa-save me-1"></i> Salva note
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Referente Modals -->
{% for referente in referenti %}
<div class="modal fade" id="editReferenteModal-{{ referente.id }}" tabindex="-1" aria-labelledby="editReferenteModalLabel-{{ referente.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editReferenteModalLabel-{{ referente.id }}">Modifica referente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'referente_update' referente.id %}" id="editReferenteForm-{{ referente.id }}">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="nome-{{ referente.id }}" class="form-label">Nome</label>
                        <input type="text" class="form-control" id="nome-{{ referente.id }}" name="nome" value="{{ referente.utente.first_name }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="cognome-{{ referente.id }}" class="form-label">Cognome</label>
                        <input type="text" class="form-control" id="cognome-{{ referente.id }}" name="cognome" value="{{ referente.utente.last_name }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="email-{{ referente.id }}" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email-{{ referente.id }}" name="email" value="{{ referente.utente.email }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="telefono-{{ referente.id }}" class="form-label">Telefono</label>
                        <input type="tel" class="form-control" id="telefono-{{ referente.id }}" name="telefono" value="{{ referente.utente.telefono }}">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('editReferenteForm-{{ referente.id }}').submit()">
                    <i class="fas fa-save me-1"></i> Salva modifiche
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Referente Confirmation Modal -->
<div class="modal fade" id="deleteReferenteModal-{{ referente.id }}" tabindex="-1" aria-labelledby="deleteReferenteModalLabel-{{ referente.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteReferenteModalLabel-{{ referente.id }}">Conferma eliminazione</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Sei sicuro di voler eliminare il referente <strong>{{ referente.utente.first_name }} {{ referente.utente.last_name }}</strong>?</p>
                <p class="text-danger"><small>Questa azione rimuoverà l'associazione tra il referente e questo cliente, ma non eliminerà l'utente dal sistema.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <form method="post" action="{% url 'referente_delete' referente.id %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-1"></i> Elimina
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}