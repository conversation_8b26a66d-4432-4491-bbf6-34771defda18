{% extends 'base/base.html' %}

{% block title %}Clienti - SimplePEC{% endblock %}

{% block extra_css %}
<style>
    .cliente-card {
        border-radius: 12px;
        transition: transform 0.2s, box-shadow 0.2s;
        overflow: hidden;
    }

    .cliente-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .cliente-header {
        padding: 20px;
        background-color: rgba(59, 130, 246, 0.1);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .cliente-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .cliente-code {
        color: #6b7280;
        font-size: 14px;
    }

    .cliente-status {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .cliente-body {
        padding: 20px;
    }

    .info-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: #6b7280;
    }

    .info-value {
        font-weight: 500;
    }

    .info-label {
        font-size: 13px;
        color: #6b7280;
        display: block;
    }

    .cliente-actions {
        padding: 15px 20px;
        background-color: #f9fafb;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .cliente-add-card {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 250px;
        cursor: pointer;
        transition: border-color 0.2s, background-color 0.2s;
    }

    .cliente-add-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }

    .add-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #eff6ff;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
    }

    /* Stili per la visualizzazione tabellare */
    .table th {
        font-weight: 600;
        font-size: 14px;
    }

    .table td {
        font-size: 14px;
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block extra_js %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fs-2 fw-bold mb-0">Clienti</h1>
    <div>
        {% if request.user.is_superuser or is_manager %}
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#newClienteModal">
            <i class="fas fa-plus me-1"></i> Nuovo cliente
        </button>
        {% endif %}
    </div>
</div>

<!-- Barra di ricerca e filtri -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="get" action="{% url 'cliente_list' %}" class="row g-3 align-items-end">
            {% csrf_token %}
            <div class="col-md-6">
                <label for="searchQuery" class="form-label">Cerca cliente</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchQuery" name="q" placeholder="Nome, codice, P.IVA, email..." value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <div class="col-md-3">
                <label for="statoFilter" class="form-label">Stato</label>
                <select class="form-select" id="statoFilter" name="stato">
                    <option value="" {% if stato_filter == '' %}selected{% endif %}>Tutti</option>
                    <option value="attivo" {% if stato_filter == 'attivo' %}selected{% endif %}>Attivi</option>
                    <option value="inattivo" {% if stato_filter == 'inattivo' %}selected{% endif %}>Inattivi</option>
                </select>
            </div>

            <div class="col-md-3 text-end">
                <label class="form-label d-block">Visualizzazione</label>
                <div class="btn-group" role="group">
                    <a href="{% url 'cliente_list' %}?view=card{% if search_query %}&q={{ search_query }}{% endif %}{% if stato_filter %}&stato={{ stato_filter }}{% endif %}" class="btn btn-outline-secondary {% if view_type == 'card' %}active{% endif %}">
                        <i class="fas fa-th-large"></i>
                    </a>
                    <a href="{% url 'cliente_list' %}?view=table{% if search_query %}&q={{ search_query }}{% endif %}{% if stato_filter %}&stato={{ stato_filter }}{% endif %}" class="btn btn-outline-secondary {% if view_type == 'table' %}active{% endif %}">
                        <i class="fas fa-list"></i>
                    </a>
                </div>

                <button type="submit" class="btn btn-primary ms-2">
                    <i class="fas fa-filter me-1"></i> Applica
                </button>

                {% if search_query or stato_filter != '' %}
                <a href="{% url 'cliente_list' %}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-times me-1"></i> Reset
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

{% if view_type == 'card' %}
<!-- Visualizzazione a card -->
<div class="row g-4">
    {% for cliente in clienti %}
    <div class="col-md-6 col-lg-4">
        <div class="card cliente-card border-0 shadow-sm h-100">
            <div class="cliente-header position-relative">
                <h3 class="cliente-name">{{ cliente.nome }}</h3>
                <div class="cliente-code">Codice: {{ cliente.codice }}</div>

                <div class="cliente-status">
                    {% if cliente.attivo %}
                    <span class="badge bg-success">Attivo</span>
                    {% else %}
                    <span class="badge bg-danger">Inattivo</span>
                    {% endif %}
                </div>
            </div>

            <div class="cliente-body">
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div>
                        <span class="info-label">Partita IVA</span>
                        <div class="info-value">{{ cliente.partita_iva|default:"--" }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-address-card"></i>
                    </div>
                    <div>
                        <span class="info-label">Codice Fiscale</span>
                        <div class="info-value">{{ cliente.codice_fiscale|default:"--" }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div>
                        <span class="info-label">Indirizzo</span>
                        <div class="info-value">{{ cliente.indirizzo|default:"--" }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div>
                        <span class="info-label">Email</span>
                        <div class="info-value">{{ cliente.email|default:"--" }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div>
                        <span class="info-label">Telefono</span>
                        <div class="info-value">{{ cliente.telefono|default:"--" }}</div>
                    </div>
                </div>
            </div>

            <div class="cliente-actions">
                <div class="btn-group w-100" role="group">
                    <a href="{% url 'cliente_detail' cliente.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-info-circle"></i> Dettagli
                    </a>
                    <a href="{% url 'message_list' %}?cliente={{ cliente.id }}" class="btn btn-primary">
                        <i class="fas fa-envelope"></i> Messaggi
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-users fa-3x mb-3 opacity-50"></i>
                <p class="mb-1">Nessun cliente trovato</p>
                <p class="small">Prova a modificare i criteri di ricerca</p>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Add cliente card -->
    {% if request.user.is_superuser or is_manager %}
    <div class="col-md-6 col-lg-4">
        <div class="cliente-add-card" data-bs-toggle="modal" data-bs-target="#newClienteModal">
            <div class="add-icon">
                <i class="fas fa-plus"></i>
            </div>
            <h3 class="fs-5 mb-1">Aggiungi cliente</h3>
            <p class="text-muted small">Registra un nuovo cliente</p>
        </div>
    </div>
    {% endif %}
</div>

{% else %}
<!-- Visualizzazione tabellare -->
<div class="card border-0 shadow-sm">
    <div class="table-responsive">
        <table class="table table-hover align-middle mb-0">
            <thead class="table-light">
                <tr>
                    <th scope="col">Nome</th>
                    <th scope="col">Codice</th>
                    <th scope="col">Partita IVA</th>
                    <th scope="col">Codice Fiscale</th>
                    <th scope="col">Email</th>
                    <th scope="col">Telefono</th>
                    <th scope="col">Stato</th>
                    <th scope="col" class="text-end">Azioni</th>
                </tr>
            </thead>
            <tbody>
                {% for cliente in clienti %}
                <tr>
                    <td>
                        <div class="fw-semibold">{{ cliente.nome }}</div>
                    </td>
                    <td>{{ cliente.codice }}</td>
                    <td>{{ cliente.partita_iva|default:"--" }}</td>
                    <td>{{ cliente.codice_fiscale|default:"--" }}</td>
                    <td>{{ cliente.email|default:"--" }}</td>
                    <td>{{ cliente.telefono|default:"--" }}</td>
                    <td>
                        {% if cliente.attivo %}
                        <span class="badge bg-success">Attivo</span>
                        {% else %}
                        <span class="badge bg-danger">Inattivo</span>
                        {% endif %}
                    </td>
                    <td class="text-end">
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="{% url 'cliente_detail' cliente.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-info-circle"></i>
                            </a>
                            <a href="{% url 'message_list' %}?cliente={{ cliente.id }}" class="btn btn-primary">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-users fa-3x mb-3 opacity-50"></i>
                            <p class="mb-1">Nessun cliente trovato</p>
                            <p class="small">Prova a modificare i criteri di ricerca</p>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pulsante per aggiungere cliente (versione tabellare) -->
{% if request.user.is_superuser or is_manager %}
<div class="text-center mt-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newClienteModal">
        <i class="fas fa-plus me-1"></i> Nuovo cliente
    </button>
</div>
{% endif %}
{% endif %}

<!-- New Cliente Modal -->
<div class="modal fade" id="newClienteModal" tabindex="-1" aria-labelledby="newClienteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newClienteModalLabel">Nuovo cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'cliente_create' %}" id="newClienteForm">
                    {% csrf_token %}

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informazioni cliente</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nome" class="form-label">Nome cliente</label>
                                    <input type="text" class="form-control" id="nome" name="nome" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="codice" class="form-label">Codice cliente</label>
                                    <input type="text" class="form-control" id="codice" name="codice">
                                    <div class="form-text">Opzionale - se lasciato vuoto, verrà generato automaticamente</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="partita_iva" class="form-label">Partita IVA</label>
                                    <input type="text" class="form-control" id="partita_iva" name="partita_iva" maxlength="11">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="codice_fiscale" class="form-label">Codice Fiscale</label>
                                    <input type="text" class="form-control" id="codice_fiscale" name="codice_fiscale" maxlength="16">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="indirizzo" class="form-label">Indirizzo</label>
                            <input type="text" class="form-control" id="indirizzo" name="indirizzo">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telefono" class="form-label">Telefono</label>
                                    <input type="tel" class="form-control" id="telefono" name="telefono">
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="attivo" name="attivo" checked>
                            <label class="form-check-label" for="attivo">Cliente attivo</label>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Note</h6>
                        <div class="mb-3">
                            <label for="note" class="form-label">Note aggiuntive</label>
                            <textarea class="form-control" id="note" name="note" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('newClienteForm').submit()">
                    <i class="fas fa-save me-1"></i> Salva cliente
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}