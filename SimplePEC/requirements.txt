# Django e componenti correlati
django>=4.2,<5.0
djangorestframework>=3.16,<3.17
djangorestframework-simplejwt>=5.5,<5.6
django-filter>=25.0,<26.0
django-cors-headers>=4.7,<4.8
django-timezone-field>=5.0

# Database
psycopg2-binary>=2.9,<2.10  # Per PostgreSQL

# Celery e componenti correlati
celery>=5.5,<5.6
django-celery-beat>=2.5,<2.6
redis>=5.0,<5.1
python-crontab>=2.3.4
cron-descriptor>=1.2.32

# Utilità
python-dateutil>=2.8,<2.9
Pillow>=10.0.0  # Per la gestione delle immagini (logo organizzazioni, profili utenti)
tzdata>=2025.2  # Per il supporto dei fusi orari

# Solo per Python < 3.9
backports.zoneinfo>=0.2.1;python_version<"3.9"