# SimplePEC - Sistema di Gestione Centralizzata PEC

## Descrizione
SimplePEC è un'applicazione web completa per la gestione centralizzata delle caselle di Posta Elettronica Certificata (PEC). Consente alle organizzazioni di gestire più account PEC, monitorare i messaggi, assegnarli a clienti e referenti, e tenere traccia dello stato di ogni comunicazione.

## Tecnologie utilizzate

### Backend
- Python 3.8+
- Django 5.2
- Django REST Framework
- PostgreSQL (SQLite per sviluppo)
- Celery per task asincroni
- Redis come message broker
- JWT per l'autenticazione
- Django Celery Beat per la schedulazione

### Frontend
- Django Templates
- Bootstrap
- jQuery
- Chart.js per i grafici
- HTMX per interazioni dinamiche

## Struttura del progetto

```
SimplePEC/
├── apps/                  # Applicazioni Django
│   ├── accounts/          # Gestione utenti, autenticazione e organizzazioni
│   ├── pec/               # Gestione account PEC, messaggi e schedulazioni
│   ├── clienti/           # Gestione clienti e referenti
│   └── dashboard/         # Dashboard, statistiche e documentazione
├── config/                # Configurazione Django e Celery
├── core/                  # Funzionalità core condivise (permessi, utilità)
├── static/                # File statici (CSS, JS, immagini)
│   ├── css/
│   ├── js/
│   └── img/
├── templates/             # Template HTML Django
│   ├── accounts/
│   ├── pec/
│   ├── clienti/
│   └── dashboard/
├── logs/                  # Log di Django, Celery worker e Celery beat
└── venv/                  # Ambiente virtuale Python
```

## Funzionalità principali

- **Gestione account PEC**: configurazione e monitoraggio di account PEC multipli
- **Sincronizzazione messaggi**: sincronizzazione automatica e manuale dei messaggi
- **Schedulazione sincronizzazioni**: configurazione di sincronizzazioni periodiche con frequenze personalizzabili
- **Gestione messaggi**: visualizzazione, assegnazione e cambio di stato dei messaggi
- **Gestione clienti**: organizzazione dei messaggi per cliente
- **Gestione organizzazioni**: raggruppamento di utenti, account PEC e clienti per organizzazione
- **Dashboard**: statistiche e grafici sull'andamento delle comunicazioni PEC
- **Sistema di permessi**: controllo accessi basato su gruppi (Manager e Staff)
- **Documentazione utente**: documentazione integrata con contenuti differenziati per gruppo utente
- **Tema chiaro/scuro**: supporto per modalità dark mode

## Setup di sviluppo

### Prerequisiti
- Python 3.8+
- Redis (per Celery)
- SQLite (per sviluppo) o PostgreSQL (per produzione)

### Installazione

1. Creare e attivare l'ambiente virtuale:
```
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

2. Installare le dipendenze:
```
pip install -r requirements.txt
```

3. Configurare il database in `config/settings.py` (SQLite è configurato di default per lo sviluppo)

4. Eseguire le migrazioni:
```
python manage.py migrate
```

5. Creare un superuser:
```
python manage.py createsuperuser
```

6. Avviare tutti i servizi con lo script unificato:
```
./django-start.sh
```

Questo script avvia:
- Server Django
- Worker Celery
- Scheduler Celery Beat
- Server Redis (se non già in esecuzione)

### Frontend

Il frontend è integrato direttamente in Django utilizzando i template Django. Non è necessario avviare un server separato per il frontend, poiché viene servito direttamente dall'applicazione Django.

## Gruppi utenti e permessi

L'applicazione implementa un sistema di permessi basato su gruppi:

### Manager
- Accesso completo ai dati della propria organizzazione
- Gestione di account PEC, clienti e utenti della propria organizzazione
- Configurazione delle schedulazioni di sincronizzazione
- Non possono accedere all'interfaccia admin di Django

### Staff
- Accesso limitato agli account PEC e clienti assegnati
- Permessi di sola lettura per la maggior parte delle funzionalità
- Possono modificare i dati dei clienti assegnati ma non possono inserire o eliminare clienti
- Possono avere permessi specifici per aggiornare stato, assegnare cliente o referente

## Setup di produzione

### Backend

1. Configurare le variabili d'ambiente per la produzione
2. Installare e configurare Gunicorn, Nginx, PostgreSQL e Redis
3. Configurare Celery per i task asincroni
4. Impostare `DEBUG = False` in `settings.py`
5. Configurare correttamente le impostazioni di sicurezza:
   - `ALLOWED_HOSTS`
   - `CSRF_COOKIE_SECURE = True`
   - `SESSION_COOKIE_SECURE = True`
   - Limitare `CORS_ALLOW_ALL_ORIGINS`

### Frontend

Configurare Nginx per servire i file statici di Django (CSS, JS, immagini) utilizzando la configurazione standard per le applicazioni Django.
