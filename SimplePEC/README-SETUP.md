# Guida alla configurazione e test di SimplePEC

Questa guida fornisce i passi necessari per configurare e testare l'applicazione SimplePEC, basata interamente su Django.

## Prerequisiti

- Python 3.8 o superiore
- Un database PostgreSQL (opzionale per lo sviluppo, SQLite va bene per i test)
- Redis (necessario per Celery)

## Configurazione dell'Applicazione

1. **Clonare il repository e attivare l'ambiente virtuale:**

```bash
cd /path/to/your/projects
git clone <repository-url> SimplePEC
cd SimplePEC/SimplePEC
python -m venv venv
source venv/bin/activate  # Linux/Mac
# oppure
venv\Scripts\activate     # Windows
```

2. **Installare le dipendenze:**

```bash
pip install -r requirements.txt
```

3. **Configurare il database:**

Per lo sviluppo, SQLite è configurato di default in `config/settings.py`. Per utilizzare PostgreSQL, modificare le impostazioni del database:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'simplepec',
        'USER': 'your_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

4. **Eseguire le migrazioni:**

```bash
python manage.py migrate
```

5. **Creare un superuser per l'accesso amministrativo:**

```bash
python manage.py createsuperuser
```

6. **Avviare tutti i servizi con lo script unificato:**

```bash
./django-start.sh
```

Questo script avvia:
- Server Django sulla porta 8000
- Worker Celery per l'elaborazione dei task asincroni
- Scheduler Celery Beat per le attività pianificate
- Server Redis (se non già in esecuzione)

L'applicazione sarà disponibile all'indirizzo `http://localhost:8000/`.

## Test dell'Applicazione

1. **Verificare che tutti i servizi siano in esecuzione:**
   - Django deve essere in esecuzione sulla porta 8000
   - Celery worker deve essere attivo per l'elaborazione dei task
   - Celery beat deve essere attivo per le schedulazioni
   - Redis deve essere in esecuzione come broker per Celery

2. **Accedere all'applicazione:**
   - Apri un browser e vai a `http://localhost:8000/`
   - Dovresti vedere la pagina di login

3. **Accedere con le credenziali del superuser Django:**
   - Usa le credenziali create con il comando `createsuperuser`
   - Se il login ha successo, verrai reindirizzato alla dashboard

4. **Configurare i gruppi utente:**
   - Accedi all'interfaccia di amministrazione Django (`http://localhost:8000/admin/`)
   - Crea i gruppi "Manager" e "Staff" se non esistono già
   - Assegna gli utenti ai gruppi appropriati

5. **Testare le funzionalità:**
   - **Dashboard:** Visualizza statistiche e grafici
   - **Account PEC:** Crea, modifica, testa e sincronizza account PEC
   - **Messaggi:** Visualizza e gestisci i messaggi PEC
   - **Organizzazioni:** Gestisci le organizzazioni e associa utenti e account PEC
   - **Schedulazioni:** Configura le sincronizzazioni automatiche degli account PEC
   - **Documentazione:** Accedi alla documentazione utente integrata

## Troubleshooting

### Errori di avvio dei servizi

Se uno dei servizi non si avvia correttamente:

1. **Server Django:**
   - Verifica che tutte le dipendenze siano installate correttamente
   - Controlla eventuali errori nei log in `logs/django_server.log`
   - Assicurati che le impostazioni del database in `settings.py` siano corrette

2. **Celery worker:**
   - Controlla i log in `logs/celery_worker.log`
   - Verifica che Redis sia in esecuzione con `redis-cli ping`
   - Assicurati che le impostazioni Celery in `settings.py` siano corrette

3. **Celery beat:**
   - Controlla i log in `logs/celery_beat.log`
   - Verifica che il database sia accessibile
   - Controlla che le schedulazioni siano configurate correttamente

### Errori di autenticazione

Se non riesci ad accedere:

1. Verifica di utilizzare le credenziali corrette del superuser Django
2. Controlla che il server Django e il database siano correttamente configurati
3. Prova ad accedere direttamente all'interfaccia di amministrazione Django (`http://localhost:8000/admin/`)

### Errori di sincronizzazione PEC

Se la sincronizzazione degli account PEC non funziona:

1. Verifica che le credenziali dell'account PEC siano corrette
2. Controlla che il server IMAP sia accessibile
3. Verifica i log di Celery per eventuali errori durante l'esecuzione dei task
4. Testa la connessione manualmente dalla pagina di dettaglio dell'account PEC

## Note per lo sviluppo

- L'applicazione utilizza Django REST Framework per fornire le API
- I template Django sono utilizzati per il rendering delle pagine
- Il sistema di permessi è basato su gruppi Django (Manager e Staff)
- Le schedulazioni utilizzano Django Celery Beat con il DatabaseScheduler
- Per il deploying in produzione, sarà necessario configurare un web server come Nginx per servire l'applicazione e i file statici