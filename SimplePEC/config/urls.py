"""
URL configuration for SimplePEC project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import routers
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required

from apps.accounts.views import GruppoUtentiViewSet, UtenteViewSet
from apps.clienti.views import ClienteViewSet, ReferenteViewSet, ContattoViewSet
from apps.pec.views import AccountPECViewSet, MessaggioPECViewSet, AllegatoViewSet, SchedulazioneSincronizzazioneViewSet
from apps.dashboard.views import (
    ConteggiView, StatistichePerAccountView,
    StatistichePerClienteView, TrendTemporaleView
)

# Configurazione del router per API REST
router = routers.DefaultRouter()

# Registrazione di viewset per accounts
router.register(r'gruppi', GruppoUtentiViewSet)
router.register(r'utenti', UtenteViewSet)

# Registrazione di viewset per clienti
router.register(r'clienti', ClienteViewSet)
router.register(r'referenti', ReferenteViewSet)
router.register(r'contatti', ContattoViewSet)

# Registrazione di viewset per pec
router.register(r'account-pec', AccountPECViewSet)
router.register(r'messaggi', MessaggioPECViewSet)
router.register(r'allegati', AllegatoViewSet)
router.register(r'schedulazioni', SchedulazioneSincronizzazioneViewSet)

# Health check per il frontend
def health_check(request):
    return JsonResponse({"status": "ok"})

# Definizione degli URL per API REST
api_urlpatterns = [
    # Health check endpoint
    path('health-check/', health_check, name='health-check'),

    # API authentication
    path('auth/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Dashboard endpoints
    path('dashboard/conteggi/', ConteggiView.as_view(), name='dashboard-conteggi'),
    path('dashboard/per-account/', StatistichePerAccountView.as_view(), name='dashboard-per-account'),
    path('dashboard/per-cliente/', StatistichePerClienteView.as_view(), name='dashboard-per-cliente'),
    path('dashboard/trend-temporale/', TrendTemporaleView.as_view(), name='dashboard-trend-temporale'),

    # Tutte le altre API
    path('', include(router.urls)),
]

# Definizione degli URL principali
urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # API endpoints (manteniamo la compatibilità per eventuali client esistenti)
    path('api/', include(api_urlpatterns)),

    # Frontend con templates Django
    path('', include('apps.dashboard.urls')),
    path('', include('apps.accounts.urls')),
    path('', include('apps.pec.urls')),
    path('', include('apps.clienti.urls')),
]

# Configurazione per servire media e static durante lo sviluppo
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)